-- Create push subscriptions table for storing user notification preferences
CREATE TABLE IF NOT EXISTS public.push_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,

    -- Push subscription data (from browser)
    subscription_data JSONB NOT NULL, -- Contains endpoint, keys, etc.

    -- Device/browser information
    device_type VARCHAR(50), -- 'mobile', 'desktop', 'tablet'
    browser_name VARCHAR(100),
    user_agent TEXT,

    -- Subscription status
    is_active BOOLEAN DEFAULT TRUE,
    last_used_at TIMESTAMP WITH TIME ZONE DEFAULT now(),

    -- Notification preferences
    preferences JSONB DEFAULT '{
        "order_updates": true,
        "delivery_updates": true,
        "messages": true,
        "marketing": false,
        "quiet_hours": {
            "enabled": false,
            "start": "22:00",
            "end": "08:00"
        }
    }',

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),

    -- Unique constraint to prevent duplicate subscriptions
    UNIQUE(user_id, subscription_data)
);

-- Create notification log table for tracking sent notifications
CREATE TABLE IF NOT EXISTS public.notification_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,

    -- Notification details
    title VARCHAR(255) NOT NULL,
    body TEXT NOT NULL,
    type VARCHAR(50) NOT NULL, -- 'order_update', 'message', 'delivery', etc.

    -- Context
    order_id INTEGER REFERENCES public.orders(id) ON DELETE SET NULL,
    communication_id UUID REFERENCES public.communications(id) ON DELETE SET NULL,

    -- Delivery status
    status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'sent', 'delivered', 'failed'
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,

    -- Push notification data
    push_data JSONB,

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_push_subscriptions_user ON public.push_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_push_subscriptions_active ON public.push_subscriptions(user_id, is_active) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_notification_log_user ON public.notification_log(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_log_type ON public.notification_log(type);
CREATE INDEX IF NOT EXISTS idx_notification_log_status ON public.notification_log(status);
CREATE INDEX IF NOT EXISTS idx_notification_log_order ON public.notification_log(order_id);
CREATE INDEX IF NOT EXISTS idx_notification_log_created ON public.notification_log(created_at);

-- Enable RLS
ALTER TABLE public.push_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notification_log ENABLE ROW LEVEL SECURITY;

-- RLS Policies for push subscriptions
CREATE POLICY "Users can manage their own push subscriptions" ON public.push_subscriptions
    FOR ALL USING (auth.uid() = user_id);

-- RLS Policies for notification log
CREATE POLICY "Users can view their own notification log" ON public.notification_log
    FOR SELECT USING (auth.uid() = user_id);

-- Note: Admin policy for notification log can be added later when user_profiles table exists

-- Function to clean up old notification logs (keep last 30 days)
CREATE OR REPLACE FUNCTION cleanup_old_notification_logs() RETURNS void AS $$
BEGIN
    DELETE FROM public.notification_log
    WHERE created_at < now() - INTERVAL '30 days';
END;
$$ LANGUAGE plpgsql;

-- Function to update subscription last_used_at
CREATE OR REPLACE FUNCTION update_subscription_last_used() RETURNS TRIGGER AS $$
BEGIN
    NEW.last_used_at := now();
    NEW.updated_at := now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_subscription_last_used
    BEFORE UPDATE ON public.push_subscriptions
    FOR EACH ROW
    EXECUTE FUNCTION update_subscription_last_used();

-- Grant permissions
GRANT ALL ON public.push_subscriptions TO authenticated;
GRANT ALL ON public.notification_log TO authenticated;

-- Comments
COMMENT ON TABLE public.push_subscriptions IS 'Stores push notification subscriptions for users';
COMMENT ON TABLE public.notification_log IS 'Logs all sent push notifications for debugging and analytics';

-- Migration complete
SELECT 'Push notifications tables created successfully' as status;
