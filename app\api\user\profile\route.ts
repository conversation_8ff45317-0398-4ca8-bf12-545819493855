import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { cookies, headers } from "next/headers"
import { adminClient } from "@/lib/supabase-admin"
import { createServerClient } from "@/lib/supabase-server"

// GET handler to fetch user profile
export async function GET(request: Request) {
  try {
    console.log("Starting user profile API request")

    // Get the authorization header
    const authHeader = request.headers.get('Authorization')
    let email = null;
    let user = null;

    // First try to get user from Authorization header if present
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7)
      if (token) {
        console.log("Found Authorization header, attempting to use token")
        try {
          // Use admin client to verify the token
          const { data: userData, error } = await adminClient.auth.getUser(token)

          if (!error && userData?.user) {
            console.log("Successfully verified token for user:", userData.user.email)
            user = userData.user
            email = userData.user.email
          } else if (error) {
            console.error("Error getting user from token:", error)
            // Log more details about the token for debugging
            console.log("Token details:", {
              length: token.length,
              firstChars: token.substring(0, 10) + '...',
              lastChars: '...' + token.substring(token.length - 10)
            })
          }
        } catch (e) {
          console.error("Exception using Authorization token:", e)
        }
      }
    }

    // If we couldn't get user from token, try cookies
    if (!user) {
      console.log("No valid token found, trying cookie-based session")
      try {
        // Get the user's session using the server client
        const supabase = await createServerClient()
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()

        if (sessionError) {
          console.error("Session error in user profile API:", sessionError)
        } else if (session?.user) {
          console.log("Found user in cookie session:", session.user.email)
          user = session.user
          email = session.user.email
        }
      } catch (cookieError) {
        console.error("Error getting session from cookies:", cookieError)
      }
    }

    // Skip auth check in development for easier testing
    if (!user && process.env.NODE_ENV === 'development') {
      // Extract email from query params for development testing
      const url = new URL(request.url)
      const devEmail = url.searchParams.get('dev_email')

      if (devEmail) {
        console.log("Development mode: Using email from query param:", devEmail)
        email = devEmail
      } else {
        // In development, if no dev_email is provided, use a test email
        console.log("Development mode: No auth found and no dev_email param, using test email")
        email = '<EMAIL>'
      }
    } else if (!user) {
      console.log("No authenticated user found in user profile API")
      return NextResponse.json(
        { error: "Not authenticated" },
        { status: 401 }
      )
    }

    if (!email) {
      console.log("User email not found in session")
      return NextResponse.json(
        { error: "User email not found" },
        { status: 400 }
      )
    }

    console.log("Fetching user profile for email:", email)

    // Use admin client to bypass RLS
    console.log("Using admin client to fetch user profile for:", email)

    // Add a timeout for database queries with a longer timeout
    const queryTimeout = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Database query timed out')), 5000); // Increased from 2000ms to 5000ms
    });

    // First try to fetch by auth_id if we have a user object
    if (user && user.id) {
      console.log("Trying to fetch user profile by auth_id:", user.id)
      try {
        // Use Promise.race to implement timeout
        const authIdResult = await Promise.race([
          adminClient
            .from("users")
            .select("*")
            .eq("auth_id", user.id)
            .single(),
          queryTimeout
        ]);

        const { data: authIdData, error: authIdError } = authIdResult;

        if (!authIdError && authIdData) {
          console.log("Successfully found user profile by auth_id")
          return NextResponse.json({ data: authIdData })
        }

        console.log("User not found by auth_id, falling back to email lookup")
      } catch (err) {
        console.error("Auth ID lookup timed out or failed:", err)
        console.log("Falling back to email lookup")
      }
    }

    // Fall back to email lookup if auth_id lookup fails or we don't have a user object
    try {
      // Use Promise.race to implement timeout for email lookup
      const emailLookupResult = await Promise.race([
        adminClient
          .from("users")
          .select("*")
          .eq("email", email)
          .single(),
        queryTimeout
      ]);

      const { data, error } = emailLookupResult;

      if (error) {
        console.error("Error fetching user profile:", error)
        console.error("Error details:", {
        code: error.code,
        message: error.message,
        details: error.details,
        hint: error.hint
      })

      // If user not found, create a new user record
      if (error.code === "PGRST116") {
        console.log("User not found, creating new user record")
        const defaultName = email.split("@")[0] || "User"

        try {
          // Check if the user already exists in auth.users
          // The admin.getUserByEmail function is not available in the current Supabase version
          // Instead, we'll use a direct query to the auth.users table
          const { data: authUser, error: authError } = await adminClient
            .from('auth.users')
            .select('id, email')
            .eq('email', email)
            .single()

          if (authError) {
            console.error("Error checking auth user:", authError)
          } else if (authUser) {
            console.log("Found auth user:", authUser.user.id)
          }

          // Determine the appropriate role based on email
          const superAdminEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];
          const adminEmails = ['<EMAIL>', '<EMAIL>'];
          const businessManagerEmails = ['<EMAIL>', '<EMAIL>'];

          let role = "customer";

          if (superAdminEmails.includes(email.toLowerCase())) {
            role = "super_admin";
          } else if (adminEmails.includes(email.toLowerCase())) {
            role = "admin";
          } else if (businessManagerEmails.includes(email.toLowerCase())) {
            role = "business_manager";
          }

          // Create a new user record with auth_id if available
          console.log(`Creating new user record with role: ${role}`)
          const userData = {
            name: defaultName,
            email: email,
            first_name: null,
            last_name: null,
            role: role,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }

          // Add auth_id if we have a user object
          if (user && user.id) {
            console.log("Setting auth_id for new user:", user.id)
            userData.auth_id = user.id
          }

          const { data: newUser, error: createError } = await adminClient
            .from("users")
            .insert([userData])
            .select()
            .single()

          if (createError) {
            console.error("Error creating user profile:", createError)
            console.error("Create error details:", {
              code: createError.code,
              message: createError.message,
              details: createError.details,
              hint: createError.hint
            })

            // Determine the appropriate role based on email
            const superAdminEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];
            const adminEmails = ['<EMAIL>', '<EMAIL>'];
            const businessManagerEmails = ['<EMAIL>', '<EMAIL>'];

            let role = "customer";

            if (superAdminEmails.includes(email.toLowerCase())) {
              role = "super_admin";
            } else if (adminEmails.includes(email.toLowerCase())) {
              role = "admin";
            } else if (businessManagerEmails.includes(email.toLowerCase())) {
              role = "business_manager";
            }

            // Return a default profile if we couldn't create one
            const defaultProfile = {
              id: 0,
              email: email,
              name: defaultName,
              first_name: null,
              last_name: null,
              phone: null,
              address: null,
              role: role
            }

            console.log("Returning default profile due to create error:", defaultProfile.role)
            return NextResponse.json({ data: defaultProfile })
          }

          console.log("Created new user profile with role:", newUser.role)
          return NextResponse.json({ data: newUser })
        } catch (err) {
          console.error("Exception during user creation:", err)

          // Determine the appropriate role based on email
          const superAdminEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];
          const adminEmails = ['<EMAIL>', '<EMAIL>'];
          const businessManagerEmails = ['<EMAIL>', '<EMAIL>'];

          let role = "customer";

          if (superAdminEmails.includes(email.toLowerCase())) {
            role = "super_admin";
          } else if (adminEmails.includes(email.toLowerCase())) {
            role = "admin";
          } else if (businessManagerEmails.includes(email.toLowerCase())) {
            role = "business_manager";
          }

          // Return a default profile if we couldn't create one
          const defaultProfile = {
            id: 0,
            email: email,
            name: defaultName,
            first_name: null,
            last_name: null,
            phone: null,
            address: null,
            role: role
          }

          console.log("Returning default profile due to exception:", defaultProfile.role)
          return NextResponse.json({ data: defaultProfile })
        }
      }

      return NextResponse.json(
        { error: `Error fetching user profile: ${error.message}` },
        { status: 500 }
      )
    }

    // Check if this is a super admin email
    const superAdminEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];

    // Ensure the role is set correctly for super admin emails
    if (superAdminEmails.includes(email.toLowerCase()) && data.role !== 'super_admin') {
      console.log(`Updating role for ${email} from ${data.role} to super_admin`);

      try {
        // Update the role
        const { data: updatedUser, error: updateError } = await adminClient
          .from("users")
          .update({ role: 'super_admin', updated_at: new Date().toISOString() })
          .eq("id", data.id)
          .select()
          .single()

        if (updateError) {
          console.error("Error updating user role:", updateError)
        } else if (updatedUser) {
          console.log(`Updated user role to super_admin:`, updatedUser.id)
          return NextResponse.json({ data: updatedUser })
        }
      } catch (updateErr) {
        console.error("Exception updating user role:", updateErr)
      }
    }

    console.log("Returning existing user profile with role:", data.role)
    return NextResponse.json({ data })

    } catch (emailLookupError) {
      console.error("Email lookup timed out or failed:", emailLookupError)

      // Create a default profile based on email
      const defaultName = email.split("@")[0] || "User"

      // Determine the appropriate role based on email
      const superAdminEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];
      const adminEmails = ['<EMAIL>', '<EMAIL>'];
      const businessManagerEmails = ['<EMAIL>', '<EMAIL>'];

      let role = "customer";

      if (superAdminEmails.includes(email.toLowerCase())) {
        role = "super_admin";
      } else if (adminEmails.includes(email.toLowerCase())) {
        role = "admin";
      } else if (businessManagerEmails.includes(email.toLowerCase())) {
        role = "business_manager";
      }

      // Return a default profile
      const defaultProfile = {
        id: 0,
        email: email,
        name: defaultName,
        first_name: null,
        last_name: null,
        phone: null,
        address: null,
        role: role
      }

      console.log("Returning default profile due to email lookup error:", defaultProfile.role)
      return NextResponse.json({ data: defaultProfile })
    }
  } catch (error: any) {
    console.error("Unexpected error in GET /api/user/profile:", error)

    // Try to extract the email from the request if possible
    let email = null
    try {
      const supabase = createServerClient()
      const { data: { session } } = await supabase.auth.getSession()
      email = session?.user?.email
    } catch (e) {
      console.error("Error getting email from session:", e)
    }

    // If we have an email, return a default profile
    if (email) {
      // Determine the appropriate role based on email
      const superAdminEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];
      const adminEmails = ['<EMAIL>', '<EMAIL>'];
      const businessManagerEmails = ['<EMAIL>', '<EMAIL>'];

      let role = "customer";

      if (superAdminEmails.includes(email.toLowerCase())) {
        role = "super_admin";
      } else if (adminEmails.includes(email.toLowerCase())) {
        role = "admin";
      } else if (businessManagerEmails.includes(email.toLowerCase())) {
        role = "business_manager";
      }

      const defaultProfile = {
        id: 0,
        email: email,
        name: email.split('@')[0] || 'User',
        first_name: null,
        last_name: null,
        phone: null,
        address: null,
        role: role
      }

      console.log("Returning default profile due to unexpected error:", defaultProfile.role)
      return NextResponse.json({ data: defaultProfile })
    }

    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}

// POST handler to update user profile
export async function POST(request: Request) {
  try {
    // Get the user's session using the server client
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()

    if (!session?.user) {
      return NextResponse.json(
        { error: "Not authenticated" },
        { status: 401 }
      )
    }

    const email = session.user.email

    if (!email) {
      return NextResponse.json(
        { error: "User email not found" },
        { status: 400 }
      )
    }

    // Get request body
    const { name, first_name, last_name, phone } = await request.json()

    // Check if user exists
    const { data: existingUser, error: checkError } = await adminClient
      .from("users")
      .select("id")
      .eq("email", email)
      .single()

    if (checkError && checkError.code !== "PGRST116") {
      console.error("Error checking user existence:", checkError)
      return NextResponse.json(
        { error: `Error checking user existence: ${checkError.message}` },
        { status: 500 }
      )
    }

    let userData

    if (existingUser) {
      // Update existing user
      const { data, error } = await adminClient
        .from("users")
        .update({
          name,
          first_name,
          last_name,
          phone,
          updated_at: new Date().toISOString()
        })
        .eq("id", existingUser.id)
        .select()
        .single()

      if (error) {
        console.error("Error updating user profile:", error)
        return NextResponse.json(
          { error: `Error updating user profile: ${error.message}` },
          { status: 500 }
        )
      }

      userData = data
    } else {
      // Create new user with auth_id
      const userData = {
        name,
        email,
        first_name,
        last_name,
        phone,
        role: "customer",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        auth_id: session.user.id // Set auth_id from the session
      }

      console.log("Creating new user with auth_id:", session.user.id)

      const { data, error } = await adminClient
        .from("users")
        .insert([userData])
        .select()
        .single()

      if (error) {
        console.error("Error creating user profile:", error)
        return NextResponse.json(
          { error: `Error creating user profile: ${error.message}` },
          { status: 500 }
        )
      }

      userData = data
    }

    return NextResponse.json({
      data: userData,
      message: "Profile updated successfully"
    })
  } catch (error: any) {
    console.error("Unexpected error in POST /api/user/profile:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
