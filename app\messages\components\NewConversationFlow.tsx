"use client"

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Search,
  ArrowRight,
  Utensils,
  Truck,
  Coffee,
  Package,
  MessageSquare,
  Calendar,
  Star,
  Building2
} from "lucide-react"
import { cn } from "@/lib/utils"
import { addAuthHeaders } from '@/utils/auth-token'

interface User {
  id: string
  email?: string
  user_metadata?: any
}

interface Context {
  order_id?: string | null
  business_id?: string | null
  rider_id?: string | null
  role?: 'customer' | 'business' | 'rider' | null
  channel?: string | null
}

interface Contact {
  id: string
  name: string
  type: 'business' | 'rider' | 'customer'
  avatar?: string
  rating?: number
  specialties?: string[]
  isRecent?: boolean
}

interface ChannelOption {
  id: string
  name: string
  description: string
  icon: React.ReactNode
  channel_type: string
}

interface NewConversationFlowProps {
  user: User
  context: Context
  onConversationStarted: (conversationId: string) => void
  onCancel: () => void
}

type FlowStep = 'contact-selection' | 'channel-selection' | 'starting'

export function NewConversationFlow({
  user,
  context,
  onConversationStarted,
  onCancel
}: NewConversationFlowProps) {
  const [step, setStep] = useState<FlowStep>('contact-selection')
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null)
  const [selectedChannel, setSelectedChannel] = useState<ChannelOption | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [contacts, setContacts] = useState<Contact[]>([])
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    // If we have specific context, try to skip steps
    if (context.business_id || context.rider_id) {
      loadSpecificContact()
    } else {
      loadContacts()
    }
  }, [context])

  const loadSpecificContact = async () => {
    setIsLoading(true)
    try {
      // Load specific contact based on context
      const contact = await fetchContactById(context.business_id || context.rider_id!)
      if (contact) {
        setSelectedContact(contact)
        setStep('channel-selection')
      }
    } catch (error) {
      console.error('Error loading specific contact:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const loadContacts = async () => {
    setIsLoading(true)
    try {
      const contactList = await fetchContacts(user.id, searchQuery)
      setContacts(contactList)
    } catch (error) {
      console.error('Error loading contacts:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getChannelOptions = (contact: Contact): ChannelOption[] => {
    const userRole = context.role || getUserPrimaryRole(user)

    if (userRole === 'customer' && contact.type === 'business') {
      return [
        {
          id: 'customer_enquiries',
          name: 'Ask about menu/allergens',
          description: 'Questions about food, ingredients, availability',
          icon: <Utensils className="h-5 w-5 text-green-600" />,
          channel_type: 'customer_enquiries'
        },
        {
          id: 'pre_order_planning',
          name: 'Plan future order',
          description: 'Catering, bulk orders, special events',
          icon: <Calendar className="h-5 w-5 text-blue-600" />,
          channel_type: 'pre_order_planning'
        },
        {
          id: 'post_order_feedback',
          name: 'Feedback about recent order',
          description: 'Reviews, complaints, compliments',
          icon: <Star className="h-5 w-5 text-yellow-600" />,
          channel_type: 'post_order_feedback'
        },
        {
          id: 'general_networking',
          name: 'General conversation',
          description: 'Casual chat and networking',
          icon: <MessageSquare className="h-5 w-5 text-purple-600" />,
          channel_type: 'general_networking'
        }
      ]
    }

    if (userRole === 'customer' && contact.type === 'rider') {
      return [
        {
          id: 'active_order_delivery',
          name: 'About current delivery',
          description: 'Delivery updates, location, timing',
          icon: <Package className="h-5 w-5 text-orange-600" />,
          channel_type: 'active_order_delivery'
        },
        {
          id: 'general_networking',
          name: 'General conversation',
          description: 'Casual chat and networking',
          icon: <MessageSquare className="h-5 w-5 text-purple-600" />,
          channel_type: 'general_networking'
        }
      ]
    }

    if (userRole === 'business' && contact.type === 'rider') {
      return [
        {
          id: 'rider_coordination',
          name: 'Coordinate deliveries',
          description: 'Pickup times, routes, capacity planning',
          icon: <Truck className="h-5 w-5 text-blue-600" />,
          channel_type: 'rider_coordination'
        },
        {
          id: 'business_networking',
          name: 'Business partnership',
          description: 'Discuss ongoing collaboration',
          icon: <Building2 className="h-5 w-5 text-emerald-600" />,
          channel_type: 'business_networking'
        }
      ]
    }

    // Default fallback
    return [
      {
        id: 'general_networking',
        name: 'General conversation',
        description: 'Start a conversation',
        icon: <MessageSquare className="h-5 w-5 text-purple-600" />,
        channel_type: 'general_networking'
      }
    ]
  }

  const handleContactSelect = (contact: Contact) => {
    setSelectedContact(contact)
    setStep('channel-selection')
  }

  const handleChannelSelect = async (channel: ChannelOption) => {
    setSelectedChannel(channel)
    setStep('starting')

    try {
      // Create new conversation
      const conversationId = await createConversation({
        user_id: user.id,
        contact_id: selectedContact!.id,
        channel_type: channel.channel_type,
        context
      })

      onConversationStarted(conversationId)
    } catch (error) {
      console.error('Error creating conversation:', error)
      // Handle error - maybe show toast
      setStep('channel-selection')
    }
  }

  // Real API functions
  const fetchContactById = async (id: string): Promise<Contact | null> => {
    try {
      const response = await fetch(`/api/users/search?id=${id}`, {
        headers: addAuthHeaders()
      })

      if (!response.ok) return null

      const data = await response.json()
      return data.user ? {
        id: data.user.id,
        name: data.user.display_name || 'Unknown User',
        type: data.user.role_capabilities?.owns_business ? 'business' : 'customer',
        rating: data.user.average_rating || 0,
        specialties: Object.keys(data.user.specialties || {})
      } : null
    } catch (error) {
      console.error('Error fetching contact by ID:', error)
      return null
    }
  }

  const fetchContacts = async (userId: string, query: string): Promise<Contact[]> => {
    try {
      // Fetch from connections API to get real connections
      const response = await fetch(`/api/connections?status=active`, {
        headers: addAuthHeaders()
      })

      if (!response.ok) return []

      const data = await response.json()
      const connections = data.connections || []

      // Transform connections to contacts
      const contacts: Contact[] = connections.map((conn: any) => ({
        id: conn.other_user_id,
        name: conn.other_user?.display_name || 'Unknown User',
        type: conn.other_user?.role_capabilities?.owns_business ? 'business' : 'customer',
        rating: conn.other_user?.average_rating || 0,
        specialties: Object.keys(conn.other_user?.specialties || {}),
        isRecent: true // Mark all connections as recent for now
      }))

      // Filter by search query if provided
      if (query) {
        return contacts.filter(contact =>
          contact.name.toLowerCase().includes(query.toLowerCase())
        )
      }

      return contacts
    } catch (error) {
      console.error('Error fetching contacts:', error)
      return []
    }
  }

  const createConversation = async (data: any): Promise<string> => {
    try {
      const response = await fetch('/api/connections-hub/messages', {
        method: 'POST',
        headers: addAuthHeaders({
          'Content-Type': 'application/json',
        }),
        body: JSON.stringify({
          recipient_id: data.contact_id,
          content: data.message || 'Starting a conversation...',
          channel_type: data.channel_type,
          message_type: 'chat',
          thread_id: crypto.randomUUID()
        })
      })

      if (!response.ok) {
        throw new Error(`Failed to create conversation: ${response.status}`)
      }

      const result = await response.json()
      return result.data?.thread_id || `conv-${Date.now()}`
    } catch (error) {
      console.error('Error creating conversation:', error)
      throw error
    }
  }

  const getUserPrimaryRole = (user: User): string => {
    // Extract from user metadata or default to customer
    return 'customer'
  }

  if (step === 'starting') {
    return (
      <div className="flex items-center justify-center min-h-[50vh]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Starting conversation...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {step === 'contact-selection' && (
        <div className="p-4 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Who do you want to message?</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search businesses, riders..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Recent Contacts */}
              {contacts.filter(c => c.isRecent).length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Recent</h4>
                  <div className="space-y-2">
                    {contacts.filter(c => c.isRecent).map((contact) => (
                      <ContactCard
                        key={contact.id}
                        contact={contact}
                        onSelect={handleContactSelect}
                      />
                    ))}
                  </div>
                </div>
              )}

              {/* All Contacts */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Browse</h4>
                <div className="grid grid-cols-2 gap-2">
                  <Button variant="outline" className="h-auto p-3 flex-col">
                    <Utensils className="h-6 w-6 mb-1 text-green-600" />
                    <span className="text-xs">Restaurants</span>
                  </Button>
                  <Button variant="outline" className="h-auto p-3 flex-col">
                    <Truck className="h-6 w-6 mb-1 text-blue-600" />
                    <span className="text-xs">Riders</span>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {step === 'channel-selection' && selectedContact && (
        <div className="p-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">
                Message {selectedContact.name}
              </CardTitle>
              <p className="text-sm text-gray-600">What's this about?</p>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {getChannelOptions(selectedContact).map((channel) => (
                  <Button
                    key={channel.id}
                    variant="outline"
                    className="w-full justify-start h-auto p-4 text-left"
                    onClick={() => handleChannelSelect(channel)}
                  >
                    <div className="flex items-center space-x-3 w-full">
                      {channel.icon}
                      <div className="flex-1">
                        <p className="font-medium">{channel.name}</p>
                        <p className="text-xs text-gray-500">{channel.description}</p>
                      </div>
                      <ArrowRight className="h-4 w-4 text-gray-400" />
                    </div>
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}

function ContactCard({ contact, onSelect }: { contact: Contact; onSelect: (contact: Contact) => void }) {
  return (
    <Button
      variant="outline"
      className="w-full justify-start h-auto p-3 text-left"
      onClick={() => onSelect(contact)}
    >
      <div className="flex items-center space-x-3 w-full">
        <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
          {contact.type === 'business' ? (
            <Building2 className="h-5 w-5 text-gray-600" />
          ) : (
            <Truck className="h-5 w-5 text-gray-600" />
          )}
        </div>
        <div className="flex-1 min-w-0">
          <p className="font-medium truncate">{contact.name}</p>
          <div className="flex items-center space-x-2">
            {contact.rating && (
              <div className="flex items-center">
                <Star className="h-3 w-3 text-yellow-500 fill-current" />
                <span className="text-xs text-gray-600 ml-1">{contact.rating}</span>
              </div>
            )}
            <Badge variant="secondary" className="text-xs">
              {contact.type}
            </Badge>
          </div>
        </div>
        <ArrowRight className="h-4 w-4 text-gray-400" />
      </div>
    </Button>
  )
}
