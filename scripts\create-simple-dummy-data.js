const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function createSimpleDummyData() {
  console.log('🗄️ Creating Simple Dummy Messaging Data\n');

  try {
    // Step 1: Check existing users
    console.log('1️⃣ Checking existing users...');
    const { data: existingUsers, error: usersError } = await supabase.auth.admin.listUsers();
    
    if (usersError) {
      console.log('❌ Could not list users:', usersError.message);
      return;
    }

    console.log(`   Found ${existingUsers.users.length} existing users`);
    
    if (existingUsers.users.length < 2) {
      console.log('❌ Need at least 2 users to create conversations');
      console.log('   Please create some users through the app first');
      return;
    }

    // Use first two users for demo
    const user1 = existingUsers.users[0];
    const user2 = existingUsers.users[1];
    
    console.log(`   Using users: ${user1.email} and ${user2.email}`);

    // Step 2: Create some simple messages using correct channel types
    console.log('\n2️⃣ Creating messages with correct channel types...');
    
    const messages = [
      {
        sender_id: user1.id,
        recipient_id: user2.id,
        channel_type: 'general', // Valid channel type
        message_type: 'chat',    // Valid message type
        subject: 'Hello!',
        content: 'Hi there! This is a test message from the messaging system.',
        is_automated: false,
        is_urgent: false,
        priority: 0,
        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString() // 2 hours ago
      },
      {
        sender_id: user2.id,
        recipient_id: user1.id,
        channel_type: 'general',
        message_type: 'chat',
        content: 'Hello! Nice to meet you. How are you doing today?',
        is_automated: false,
        is_urgent: false,
        priority: 0,
        created_at: new Date(Date.now() - 1.5 * 60 * 60 * 1000).toISOString() // 1.5 hours ago
      },
      {
        sender_id: user1.id,
        recipient_id: user2.id,
        channel_type: 'order', // Order-related conversation
        message_type: 'chat',
        subject: 'Order Question',
        content: 'Do you have any vegetarian options available?',
        is_automated: false,
        is_urgent: false,
        priority: 0,
        created_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString() // 1 hour ago
      },
      {
        sender_id: user2.id,
        recipient_id: user1.id,
        channel_type: 'order',
        message_type: 'chat',
        content: 'Yes! We have several great vegetarian dishes. Would you like me to recommend some?',
        is_automated: false,
        is_urgent: false,
        priority: 0,
        created_at: new Date(Date.now() - 45 * 60 * 1000).toISOString() // 45 minutes ago
      },
      {
        sender_id: user1.id,
        recipient_id: user2.id,
        channel_type: 'order',
        message_type: 'status_update',
        subject: 'Order Status Update',
        content: 'Your order has been confirmed and is being prepared.',
        is_automated: true,
        is_urgent: false,
        priority: 1,
        created_at: new Date(Date.now() - 30 * 60 * 1000).toISOString() // 30 minutes ago
      },
      {
        sender_id: user2.id,
        recipient_id: user1.id,
        channel_type: 'order',
        message_type: 'status_update',
        subject: 'Order Ready',
        content: 'Your order is ready for pickup!',
        is_automated: true,
        is_urgent: true,
        priority: 1,
        created_at: new Date(Date.now() - 10 * 60 * 1000).toISOString() // 10 minutes ago
      },
      {
        sender_id: user1.id,
        recipient_id: user2.id,
        channel_type: 'pre-order',
        message_type: 'chat',
        subject: 'Catering Inquiry',
        content: 'Hi! I\'m planning an event next week. Do you do catering orders?',
        is_automated: false,
        is_urgent: false,
        priority: 0,
        created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString() // 2 days ago
      },
      {
        sender_id: user2.id,
        recipient_id: user1.id,
        channel_type: 'pre-order',
        message_type: 'chat',
        content: 'Absolutely! We love doing catering. What kind of event are you planning?',
        is_automated: false,
        is_urgent: false,
        priority: 0,
        created_at: new Date(Date.now() - 1.8 * 24 * 60 * 60 * 1000).toISOString() // 1.8 days ago
      }
    ];

    // Insert messages one by one
    for (let i = 0; i < messages.length; i++) {
      const message = messages[i];
      
      console.log(`   Creating message ${i + 1}: "${message.content.substring(0, 30)}..."`);
      
      const { data, error } = await supabase
        .from('communications')
        .insert(message)
        .select();
      
      if (error) {
        console.log(`     ❌ Error: ${error.message}`);
      } else {
        console.log(`     ✅ Created with thread_id: ${data[0]?.thread_id}`);
      }
    }

    // Step 3: Add some connection profiles for better display
    console.log('\n3️⃣ Creating connection profiles...');
    
    const profiles = [
      {
        user_id: user1.id,
        profile_type: 'customer',
        display_name: user1.user_metadata?.name || user1.email?.split('@')[0] || 'Customer User',
        bio: 'Food lover and regular customer',
        is_public: true,
        allow_direct_messages: true
      },
      {
        user_id: user2.id,
        profile_type: 'business',
        display_name: user2.user_metadata?.name || user2.email?.split('@')[0] || 'Business User',
        bio: 'Local restaurant providing great food',
        is_public: true,
        allow_direct_messages: true
      }
    ];

    for (const profile of profiles) {
      const { error } = await supabase
        .from('connection_profiles')
        .upsert(profile, { onConflict: 'user_id,profile_type' });
      
      if (error) {
        console.log(`   ⚠️  Profile error: ${error.message}`);
      } else {
        console.log(`   ✅ Profile created: ${profile.display_name}`);
      }
    }

    console.log('\n🎉 Simple dummy data creation completed!');
    console.log('\n📊 Summary:');
    console.log(`   👥 Used ${existingUsers.users.length} existing users`);
    console.log(`   💬 Created ${messages.length} messages`);
    console.log('   📝 Multiple conversation threads');
    console.log('   🤖 Automated and manual messages');
    console.log('   👤 User profiles created');
    console.log('\n🎯 You can now test the messaging system!');
    console.log('   Go to: http://localhost:3000/messages');

  } catch (error) {
    console.error('❌ Error creating dummy data:', error);
  }
}

// Run the script
createSimpleDummyData();
