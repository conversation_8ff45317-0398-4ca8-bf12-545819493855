import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create Supabase client with service role key (only available server-side)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Simple in-memory cache
const CACHE_DURATION = 60 * 1000; // 1 minute in milliseconds (reduced for testing)
const cache: Record<string, { data: any, timestamp: number }> = {};

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Extract business ID from URL path directly
    const url = request.url;
    const pathParts = url.split('/');
    const businessIdIndex = pathParts.findIndex(part => part === 'businesses') + 1;
    const businessId = businessIdIndex > 0 && businessIdIndex < pathParts.length
      ? pathParts[businessIdIndex]
      : '';

    if (!businessId) {
      return NextResponse.json(
        { error: 'Business ID is required' },
        { status: 400 }
      );
    }

    // Check if we have a valid cached response
    const now = Date.now();
    if (cache[businessId] && (now - cache[businessId].timestamp) < CACHE_DURATION) {
      // Return cached data
      return NextResponse.json(cache[businessId].data);
    }

    // Convert businessId to a number for database lookup
    const numericId = Number(businessId);

    // Get business details including delivery fee model and coordinates
    const { data: business, error } = await supabase
      .from('businesses')
      .select(`
        id,
        name,
        slug,
        business_type_id,
        delivery_fee,
        delivery_fee_model,
        delivery_fee_per_km,
        coordinates,
        preparation_time_minutes,
        delivery_radius,
        minimum_order_amount,
        delivery_available
      `)
      .eq('id', numericId)
      .single();

    // Log the business delivery model for debugging
    if (business) {
      // Check for potential issues with the delivery fee model
      if (business.delivery_fee_model === 'mixed' && (business.delivery_fee_per_km === undefined || business.delivery_fee_per_km === 0)) {
        console.warn(`WARNING: Business ${businessId} has mixed model but fee_per_km is ${business.delivery_fee_per_km}`);
      }
    }

    if (error) {
      console.error('Error fetching business details:', error);
      return NextResponse.json(
        { error: 'Failed to fetch business details', details: error.message },
        { status: 500 }
      );
    }

    if (!business) {
      return NextResponse.json(
        { error: 'Business not found' },
        { status: 404 }
      );
    }

    // Store in cache using the numeric ID as key
    const cacheKey = business.id.toString();

    cache[cacheKey] = {
      data: business,
      timestamp: Date.now()
    };

    return NextResponse.json(business);
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}
