"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { MapPin, Loader2, Search, AlertTriangle, X, MessageSquare } from 'lucide-react';
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { useLocation } from '@/context/location-context';
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from "@/components/ui/dialog";
import { UserPostcodeInput } from '@/components/delivery/user-postcode-input';
import { isValidJerseyPostcodeFormat, standardizeJerseyPostcodeFormat } from '@/lib/jersey-postcodes';
import { UserLocationData } from '@/lib/session-utils';
import { useAuth } from '@/context/unified-auth-context';

const HomeClient: React.FC = () => {
  const router = useRouter();
  const { user } = useAuth();
  const {
    postcode,
    setPostcode,
    geocodePostcode,
    isLoading
  } = useLocation();

  const [validationError, setValidationError] = useState<string | null>(null);
  const [errorDialogOpen, setErrorDialogOpen] = useState(false);
  const [invalidPostcode, setInvalidPostcode] = useState<string>("");

  // Function to validate Jersey postcode format
  const isValidJerseyPostcode = (postcode: string): boolean => {
    // Use our new standardized function
    return isValidJerseyPostcodeFormat(postcode);
  };

  // Format a Jersey postcode to ensure it has a space
  const formatJerseyPostcode = (postcode: string): string => {
    // Use our new standardized function
    const standardized = standardizeJerseyPostcodeFormat(postcode);
    return standardized || postcode.trim().toUpperCase();
  };

  // Handle proceeding with default coordinates
  const handleProceedWithDefault = () => {
    // Close the dialog
    setErrorDialogOpen(false);

    // Navigate to search page with the invalid postcode
    // The location context will use default coordinates
    router.push(`/search?postcode=${encodeURIComponent(invalidPostcode)}`);
  };

  const handlePostcodeSubmit = async (postcodeValue: string) => {
    try {
      // Save the postcode to the user's session
      const response = await fetch('/api/user/location', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ postcode: postcodeValue }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save location');
      }

      const data = await response.json();

      // Update the postcode in the location context
      setPostcode(postcodeValue);

      // Navigate to search page with postcode parameter
      router.push(`/search?postcode=${encodeURIComponent(postcodeValue)}`);
    } catch (error) {
      console.error('Error saving location:', error);
      setValidationError('Failed to save your location. Please try again.');
      setErrorDialogOpen(true);
    }
  };

  const handleValidationError = (error: string) => {
    setValidationError(error);
  };

  return (
    <div className="text-center w-full">
      {/* Orders. Delivered Text */}
      <div className="mb-8 md:mb-10 px-4 py-6">
        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight text-emerald-600" style={{ textShadow: '0 1px 2px rgba(255,255,255,0.8)' }}>
          <span className="inline-block">Orders.</span>{" "}
          <span className="inline-block animate-slide-in-from-right will-change-transform">Delivered.</span>
        </h2>
      </div>

      {/* Postcode Entry Bar */}
      <div className="w-full max-w-3xl mx-auto animate-fade-in-delay-1">
        <div className="relative">
          <div className="absolute left-4 top-[22px] z-10">
            <MapPin className="h-5 w-5 text-emerald-500" />
          </div>
          <UserPostcodeInput
            initialPostcode={postcode}
            onPostcodeSubmit={handlePostcodeSubmit}
            buttonText="Search"
            placeholder="Enter your postcode for delivery times to your door"
            className="pl-10" // Add left padding for the icon
          />
        </div>

        {/* Postcode Format Hint */}
        <p className="text-xs text-gray-600 mt-2 text-left pl-4">
          Enter a Jersey postcode (e.g., JE2 3NG) to see delivery times
        </p>

        {/* Quick Messages Access - Only for authenticated users */}
        {user && (
          <div className="mt-6 flex justify-center">
            <Button
              variant="outline"
              size="lg"
              onClick={() => router.push('/messages')}
              className="bg-white/80 backdrop-blur-sm border-emerald-200 hover:bg-emerald-50 hover:border-emerald-300 text-emerald-700 font-medium px-6 py-3 h-auto"
            >
              <MessageSquare className="h-5 w-5 mr-2" />
              Quick Messages
            </Button>
          </div>
        )}

        {/* Error Dialog */}
        <Dialog open={errorDialogOpen} onOpenChange={setErrorDialogOpen}>
          <DialogContent className="max-w-[95vw] sm:max-w-md p-4 sm:p-6">
            <DialogTitle className="sr-only">Invalid Postcode</DialogTitle>
            <div className="absolute right-4 top-4">
              <button
                onClick={() => setErrorDialogOpen(false)}
                className="rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
              >
                <X className="h-4 w-4" />
                <span className="sr-only">Close</span>
              </button>
            </div>
            <div className="flex flex-col gap-4">
              <div className="flex items-start gap-3">
                <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />
                <div>
                  <h3 className="text-lg font-semibold text-red-600">Invalid Postcode</h3>
                </div>
              </div>
              <p className="text-gray-600 mt-1 pl-8">
                {validationError}
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-2 pt-4 w-full justify-center">
              <Button
                variant="outline"
                onClick={() => setErrorDialogOpen(false)}
                className="w-[90%] mx-auto sm:w-[45%] text-[11px] sm:text-sm px-1 sm:px-2 whitespace-nowrap"
                size="sm"
                style={{ minHeight: "36px" }}
              >
                Enter a different postcode
              </Button>
              <Button
                onClick={handleProceedWithDefault}
                className="w-[90%] mx-auto sm:w-[55%] bg-emerald-600 hover:bg-emerald-700 text-[11px] sm:text-sm px-1 sm:px-2 whitespace-nowrap"
                size="sm"
                style={{ minHeight: "36px" }}
              >
                Continue with default location
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default HomeClient;
