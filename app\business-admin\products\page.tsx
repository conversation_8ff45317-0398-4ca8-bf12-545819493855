"use client"

import React, { useState, useEffect } from "react"
import { initScrollbar } from "./scrollbar-init"
import { addAuthHeaders } from "@/utils/auth-token"
import { useRouter } from "next/navigation"
import Link from "next/link"

import { format } from "date-fns"
import { useAuthDirect } from "@/context/auth-context-direct"
import { useRealtimeProducts, Product, ProductVariant, ProductStats } from "@/hooks/use-realtime-products"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, <PERSON>lt<PERSON>Provider, Toolt<PERSON>Trigger } from "@/components/ui/tooltip"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { useToast } from "@/components/ui/use-toast"
import ProductForm from "@/app/business-admin/products/product-form"
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Building2,
  ChevronDown,
  ChevronRight,
  Download,
  Edit,
  Filter,
  HelpCircle,
  Image as ImageIcon,
  Info,
  Layers,
  MoreHorizontal,
  Package,
  Pencil,
  Plus,
  Search,
  SlidersHorizontal,
  Trash2,
  Upload
} from "lucide-react"

interface Category {
  id: number
  name: string
  description?: string
  display_order: number
  product_count?: number
}

interface BusinessData {
  id: number
  name: string
  business_type_id: number
  business_type?: string
  logo_url?: string | null
}

interface BusinessOption {
  id: number
  name: string
  business_type?: string
}

export default function BusinessAdminProducts() {
  const router = useRouter()
  const { user, userProfile, isAdmin, isSuperAdmin } = useAuthDirect()
  const [business, setBusiness] = useState<BusinessData | null>(null)
  const [isPendingApproval, setIsPendingApproval] = useState(false)
  const [availableBusinesses, setAvailableBusinesses] = useState<BusinessOption[]>([])
  const [selectedBusinessId, setSelectedBusinessId] = useState<number | null>(null)
  const [isAdminUser, setIsAdminUser] = useState(false)
  const [categories, setCategories] = useState<Category[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>("all")
  const [isLoadingBusinessData, setIsLoadingBusinessData] = useState(true)
  const [isAddProductDialogOpen, setIsAddProductDialogOpen] = useState(false)
  const [currentProduct, setCurrentProduct] = useState<Product | null>(null)
  const [expandedProducts, setExpandedProducts] = useState<Record<number, boolean>>({})
  const { toast } = useToast()

  // Check if user is admin or super admin and get selected business ID from localStorage
  useEffect(() => {
    if (isAdmin || isSuperAdmin) {
      setIsAdminUser(true)

      // Get selected business ID from localStorage
      const storedBusinessId = localStorage.getItem('loop_jersey_selected_business_id');
      if (storedBusinessId) {
        const businessId = parseInt(storedBusinessId);
        console.log("Retrieved selected business ID from localStorage:", businessId);
        setSelectedBusinessId(businessId);
      }
    }
  }, [isAdmin, isSuperAdmin])

  // Set up real-time products with the hook
  const {
    products,
    filteredProducts,
    productStats,
    loading: isLoadingProducts,
    error: productsError,
    searchQuery,
    activeTab,
    handleSearch,
    handleTabChange,
    refetch: refetchProducts
  } = useRealtimeProducts({
    businessId: (isAdminUser && selectedBusinessId) ? selectedBusinessId : userProfile?.business_id,
    categoryId: selectedCategory !== "all" ? selectedCategory : undefined,
    enabled: !!user,
    showNotifications: true
  });

  // Define fetchBusinessData outside of useEffect so it can be called from multiple places
  const fetchBusinessData = async () => {
    try {
      console.log("Fetching business data...")
      setIsLoadingBusinessData(true)

      // Get the authentication token from localStorage
      const token = localStorage.getItem('loop_jersey_auth_token') || '';

      // Build the URL based on whether we're an admin user with a selected business
      let url = '/api/business-admin/business-data'

      // If admin user and a business is selected, add the business ID as a query parameter
      if (isAdminUser && selectedBusinessId) {
        url = `/api/business-admin/business-data?businessId=${selectedBusinessId}`
        console.log(`Admin user fetching data for business ID: ${selectedBusinessId}`)
      }

      const response = await fetch(url, {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      })

      if (!response.ok) {
        // If we get a 401 or 403, redirect to login
        if (response.status === 401 || response.status === 403) {
          console.log("Authentication error, redirecting to login")
          router.push("/login?redirectTo=/business-admin/products")
          return
        }

        throw new Error(`API error: ${response.status}`)
      }

      const data = await response.json()
      console.log("Business data received:", data)

      if (data.business) {
        setBusiness({
          id: data.business.id,
          name: data.business.name,
          business_type_id: data.business.business_type_id,
          business_type: data.business.business_type
        })

        // Check if the business is pending approval
        setIsPendingApproval(data.business.is_approved === false)
      } else {
        throw new Error("No business data found")
      }
    } catch (err) {
      console.error("Error fetching business data:", err)
    } finally {
      setIsLoadingBusinessData(false)
    }
  }

  // For admin users, fetch available businesses
  const fetchAvailableBusinesses = async () => {
    if (!isAdminUser) return

    try {
      console.log("Admin user detected, fetching available businesses")

      const response = await fetch('/api/admin/businesses-direct', {
        headers: addAuthHeaders()
      })

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`)
      }

      const data = await response.json()
      console.log("Available businesses:", data)

      if (data && Array.isArray(data)) {
        setAvailableBusinesses(data.map((b: any) => ({
          id: b.id,
          name: b.name,
          business_type: b.business_type || b.business_types?.name || "Business"
        })))
      }
    } catch (err) {
      console.error("Error fetching available businesses:", err)
    }
  }

  // Handle business selection change for admin users
  const handleBusinessChange = (businessId: number) => {
    console.log("Selected business changed to:", businessId)
    setSelectedBusinessId(businessId)

    // Store the selected business ID in localStorage
    localStorage.setItem('loop_jersey_selected_business_id', businessId.toString());
    console.log("Stored selected business ID in localStorage:", businessId);

    // Refetch data with the new business ID
    fetchBusinessData()
  }

  // Fetch categories
  const fetchCategories = async () => {
    try {
      // Fetch categories from the API
      const response = await fetch("/api/business-admin/categories", {
        method: "GET",
        headers: addAuthHeaders(),
        credentials: "include"
      })

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`)
      }

      const data = await response.json()
      console.log("Categories data:", data)

      setCategories(data.categories || [])
    } catch (err) {
      console.error("Error fetching categories:", err)
    }
  }

  // Handle category change
  const handleCategoryChange = (value: string) => {
    setSelectedCategory(value)
    // Refetch products when category changes
    refetchProducts()
  }

  // Handle search input change
  const handleSearchInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleSearch(e.target.value)
  }

  // Initial data loading
  useEffect(() => {
    if (user) {
      fetchBusinessData()
      fetchCategories()

      if (isAdminUser) {
        fetchAvailableBusinesses()

        // Refetch products when selectedBusinessId changes
        if (selectedBusinessId) {
          console.log("Selected business ID changed, refetching products:", selectedBusinessId);
          refetchProducts();
        }
      }
    }
  }, [user, isAdminUser, selectedBusinessId])

  // Initialize scrollbar when component mounts
  useEffect(() => {
    // Initialize scrollbar after component mounts
    initScrollbar()

    // Re-initialize scrollbar when window is resized
    const handleResize = () => {
      initScrollbar()
    }

    window.addEventListener('resize', handleResize)

    // Also initialize after products are loaded
    if (!isLoadingProducts && products.length > 0) {
      setTimeout(initScrollbar, 500)
    }

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [isLoadingProducts, products.length])

  // Format currency helper
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
      minimumFractionDigits: 2
    }).format(amount)
  }

  // Format date helper with time
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return format(date, 'dd MMM yyyy HH:mm')
  }

  // Display Cell Component (non-editable replacement for EditableCell)
  const DisplayCell = ({
    value,
    formatter = (val: any) => val?.toString() || "-"
  }: {
    value: any,
    formatter?: (val: any) => string
  }) => {
    const displayValue = formatter(value);

    return (
      <div className="relative">
        <span className="text-sm truncate max-w-full py-1" title={displayValue}>{displayValue}</span>
      </div>
    );
  };





  // Toggle product expansion to show/hide variants
  const toggleProductExpansion = (productId: number) => {
    setExpandedProducts(prev => ({
      ...prev,
      [productId]: !prev[productId]
    }))
  }

  // Toggle all product expansions
  const toggleAllProductExpansions = () => {
    // Check if any products are expanded
    const hasExpandedProducts = Object.values(expandedProducts).some(expanded => expanded);

    if (hasExpandedProducts) {
      // Collapse all
      setExpandedProducts({});
    } else {
      // Expand all products that have variants
      const newExpandedState: Record<number, boolean> = {};
      products.forEach(product => {
        if (product.variants && product.variants.length > 0) {
          newExpandedState[product.id] = true;
        }
      });
      setExpandedProducts(newExpandedState);
    }
  }

  // Count total variants across all products
  const getTotalVariantsCount = () => {
    return products.reduce((total, product) => {
      return total + (product.variants?.length || 0)
    }, 0)
  }

  // Get total stock quantity across all products
  const getTotalStockQuantity = () => {
    return products.reduce((total, product) => {
      const productStock = product.quantity || 0;
      // Variants don't have stock quantity in the database
      return total + productStock;
    }, 0);
  }

  // Count low stock products (less than 10 items)
  const getLowStockCount = () => {
    return products.filter(product => {
      return (product.quantity !== undefined && product.quantity < 10);
    }).length;
  }

  // Count popular products
  const getPopularProductsCount = () => {
    return products.filter(product => product.is_popular).length;
  }

  // Check if any products are expanded
  const hasExpandedProducts = () => {
    return Object.values(expandedProducts).some(expanded => expanded);
  }









  // Loading state
  const isLoading = isLoadingBusinessData || isLoadingProducts;
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading products...</p>
        </div>
      </div>
    )
  }

  // Error state
  if (productsError) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-red-500 text-5xl mb-4">⚠️</div>
          <h2 className="text-2xl font-bold mb-2">Error Loading Products</h2>
          <p className="text-gray-600 mb-4">{productsError.message}</p>
          <Button onClick={() => refetchProducts()}>Retry</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full max-w-full overflow-hidden" style={{ maxWidth: '100vw' }}>
      <style jsx global>{`
        /* Ensure the table container doesn't overflow the page */
        .card-content-wrapper {
          width: 100%;
          overflow: hidden;
          position: relative;
        }

        /* Ensure table cells don't expand too much */
        table th, table td {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          padding: 0.5rem 0.75rem;
        }

        /* Set specific column widths */
        table th:nth-child(1), table td:nth-child(1) { width: 40px; min-width: 40px; }
        table th:nth-child(2), table td:nth-child(2) { width: 80px; min-width: 80px; }
        table th:nth-child(3), table td:nth-child(3) { width: 60px; min-width: 60px; }
        table th:nth-child(4), table td:nth-child(4) { width: 50px; min-width: 50px; }
        table th:nth-child(5), table td:nth-child(5) { width: 200px; min-width: 200px; }
        table th:nth-child(6), table td:nth-child(6) { width: 120px; min-width: 120px; }
        table th:nth-child(7), table td:nth-child(7) { width: 80px; min-width: 80px; }
        table th:nth-child(8), table td:nth-child(8) { width: 80px; min-width: 80px; }
        table th:nth-child(9), table td:nth-child(9) { width: 80px; min-width: 80px; }
        table th:nth-child(10), table td:nth-child(10) { width: 80px; min-width: 80px; }
        table th:nth-child(11), table td:nth-child(11) { width: 80px; min-width: 80px; }
        table th:nth-child(12), table td:nth-child(12) { width: 80px; min-width: 80px; }
        table th:nth-child(13), table td:nth-child(13) { width: 80px; min-width: 80px; }
        table th:nth-child(14), table td:nth-child(14) { width: 80px; min-width: 80px; }
        table th:nth-child(15), table td:nth-child(15) { width: 100px; min-width: 100px; }

        /* Table styling improvements */
        table thead tr {
          background-color: #f9fafb;
          border-bottom: 2px solid #e5e7eb;
        }

        table tbody tr:nth-child(even):not(.bg-gray-50) {
          background-color: #f9fafb;
        }

        table tbody tr:hover:not(.bg-gray-50) {
          background-color: #f3f4f6;
        }

        /* Allow description cells to wrap */
        .description-cell {
          white-space: normal;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }

        /* Compact stats cards */
        .stats-card {
          height: 100%;
        }

        .stats-card .card-header {
          padding: 0.75rem 1rem 0.5rem;
        }

        .stats-card .card-content {
          padding: 0.5rem 1rem 1rem;
        }
      `}</style>
      <TooltipProvider>
        <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Products</h1>
            <p className="text-muted-foreground">
              Welcome, {userProfile?.name || user?.email || "User"} | {business?.name || "Your Business"}
              {business?.business_type && ` - ${business.business_type}`}
            </p>

            {/* Business Selector for Admin Users */}
            {isAdminUser && availableBusinesses.length > 0 && (
              <div className="flex items-center mt-2">
                <div className="flex items-center bg-gray-50 rounded-md border px-3 py-1">
                  <Building2 className="h-4 w-4 text-gray-500 mr-2" />
                  <Select
                    value={selectedBusinessId?.toString() || ""}
                    onValueChange={(value) => handleBusinessChange(parseInt(value))}
                  >
                    <SelectTrigger className="border-0 bg-transparent p-0 h-auto shadow-none focus:ring-0 w-[160px] truncate">
                      <SelectValue placeholder="Select a business" className="truncate" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableBusinesses.map((business) => (
                        <SelectItem key={business.id} value={business.id.toString()} className="truncate">
                          {business.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              className="h-8"
              onClick={() => router.push('/business-admin/product-upload')}
            >
              <Upload className="mr-2 h-3.5 w-3.5" />
              Import
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="h-8"
              onClick={() => {
                // Create CSV content
                if (products.length === 0) {
                  toast({
                    title: "No Products",
                    description: "There are no products to export",
                    variant: "destructive"
                  })
                  return
                }

                // Create CSV header
                const headers = ["ID", "Name", "Description", "Price", "Category", "Slug", "SKU", "Stock Quantity", "Business Ref", "Unit", "Available", "Featured", "Popular", "Created At", "Updated At"]

                // Create CSV rows
                const rows = products.map(product => [
                  product.id,
                  product.name,
                  product.description || "",
                  product.price,
                  product.categories?.name || "Uncategorized",
                  product.slug || "",
                  "-", // No SKU field in database
                  product.quantity !== undefined ? product.quantity : "",
                  "-", // No business_ref field in database
                  product.unit || "",
                  product.is_available ? "Yes" : "No",
                  product.is_featured ? "Yes" : "No",
                  product.is_popular ? "Yes" : "No",
                  formatDate(product.created_at),
                  formatDate(product.updated_at)
                ])

                // Combine header and rows
                const csvContent = [
                  headers.join(","),
                  ...rows.map(row => row.map(cell =>
                    // Wrap cells with commas in quotes
                    typeof cell === 'string' && (cell.includes(',') || cell.includes('"') || cell.includes('\n'))
                      ? `"${cell.replace(/"/g, '""')}"`
                      : cell
                  ).join(","))
                ].join("\n")

                // Create a blob and download link
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
                const url = URL.createObjectURL(blob)
                const link = document.createElement('a')
                link.setAttribute('href', url)
                link.setAttribute('download', `products_export_${new Date().toISOString().split('T')[0]}.csv`)
                link.style.visibility = 'hidden'
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)

                toast({
                  title: "Export Complete",
                  description: `${products.length} products exported to CSV`,
                  variant: "default"
                })
              }}
            >
              <Download className="mr-2 h-3.5 w-3.5" />
              Export
            </Button>
            <Button
              size="sm"
              className="h-8 bg-emerald-600 hover:bg-emerald-700"
              onClick={() => {
                setCurrentProduct(null)
                setIsAddProductDialogOpen(true)
              }}
            >
              <Plus className="mr-2 h-3.5 w-3.5" />
              Add Product
            </Button>
          </div>
        </div>

        {/* All stats cards in a single row */}
        <div className="grid gap-2 grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-7 mt-3 mb-4">
          <Card className="stats-card">
            <CardHeader className="card-header flex flex-row items-center justify-between space-y-0">
              <CardTitle className="text-xs font-medium">Total Products</CardTitle>
              <Package className="h-3.5 w-3.5 text-emerald-500" />
            </CardHeader>
            <CardContent className="card-content">
              <div className="text-xl font-bold">{productStats.total}</div>
              <p className="text-xs text-muted-foreground truncate">
                {productStats.total > 0 ? `${productStats.total} total products` : "No products yet"}
              </p>
            </CardContent>
          </Card>

          <Card className="stats-card">
            <CardHeader className="card-header flex flex-row items-center justify-between space-y-0">
              <CardTitle className="text-xs font-medium">Product Variants</CardTitle>
              <Layers className="h-3.5 w-3.5 text-blue-500" />
            </CardHeader>
            <CardContent className="card-content">
              <div className="text-xl font-bold">{getTotalVariantsCount()}</div>
              <p className="text-xs text-muted-foreground truncate">
                {getTotalVariantsCount() > 0 ? `${getTotalVariantsCount()} variants` : "No variants"}
              </p>
            </CardContent>
          </Card>

          <Card className="stats-card">
            <CardHeader className="card-header flex flex-row items-center justify-between space-y-0">
              <CardTitle className="text-xs font-medium">Low Stock</CardTitle>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                className="h-3.5 w-3.5 text-yellow-500"
              >
                <path d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </CardHeader>
            <CardContent className="card-content">
              <div className="text-xl font-bold">{productStats.lowStock}</div>
              <p className="text-xs text-muted-foreground truncate">Requires attention</p>
            </CardContent>
          </Card>

          <Card className="stats-card">
            <CardHeader className="card-header flex flex-row items-center justify-between space-y-0">
              <CardTitle className="text-xs font-medium">Out of Stock</CardTitle>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                className="h-3.5 w-3.5 text-red-500"
              >
                <path d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </CardHeader>
            <CardContent className="card-content">
              <div className="text-xl font-bold">{productStats.outOfStock}</div>
              <p className="text-xs text-muted-foreground truncate">Needs restocking</p>
            </CardContent>
          </Card>

          <Card className="stats-card">
            <CardHeader className="card-header flex flex-row items-center justify-between space-y-0">
              <CardTitle className="text-xs font-medium">Total Stock</CardTitle>
              <Package className="h-3.5 w-3.5 text-blue-500" />
            </CardHeader>
            <CardContent className="card-content">
              <div className="text-xl font-bold">{getTotalStockQuantity()}</div>
              <p className="text-xs text-muted-foreground truncate">
                Total inventory
              </p>
            </CardContent>
          </Card>


        </div>

        <Tabs defaultValue="all" className="space-y-3 mt-4" onValueChange={handleTabChange}>
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
            <div className="flex flex-wrap items-center gap-2 w-full sm:w-auto">
              <TabsList className="bg-gray-100 mb-1 sm:mb-0 w-full sm:w-auto overflow-x-auto scrollbar-hide">
                <TabsTrigger value="all" className="text-sm">All Products</TabsTrigger>
                <TabsTrigger value="active" className="text-sm">Active</TabsTrigger>
                <TabsTrigger value="draft" className="text-sm">Draft</TabsTrigger>
                <TabsTrigger value="archived" className="text-sm">Archived</TabsTrigger>
              </TabsList>
              <Select value={selectedCategory} onValueChange={handleCategoryChange}>
                <SelectTrigger className="h-8 w-full sm:w-[180px] bg-white">
                  <SelectValue placeholder="All Categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Categories</SelectLabel>
                    <SelectItem value="all">All Categories</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id.toString()}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center gap-1 flex-wrap sm:flex-nowrap w-full sm:w-auto">
              <Button variant="outline" size="sm" className="h-8 bg-white text-red-600 border-red-200 hover:bg-red-50 hover:text-red-700">
                <Trash2 className="mr-1 h-3.5 w-3.5" />
                Delete Selected
              </Button>
              <div className="flex items-center gap-1 w-full sm:w-auto">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" className="h-8 bg-white px-3">
                      <Filter className="mr-2 h-3.5 w-3.5" />
                      <span className="hidden sm:inline">Filter</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-[200px]">
                    <DropdownMenuLabel>Filter by</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuCheckboxItem checked>In stock</DropdownMenuCheckboxItem>
                    <DropdownMenuCheckboxItem>Out of stock</DropdownMenuCheckboxItem>
                    <DropdownMenuCheckboxItem>Low stock</DropdownMenuCheckboxItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuLabel>Categories</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    {categories.map((category) => (
                      <DropdownMenuCheckboxItem key={category.id} checked>
                        {category.name}
                      </DropdownMenuCheckboxItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" className="h-8 bg-white px-3">
                      <SlidersHorizontal className="mr-2 h-3.5 w-3.5" />
                      <span className="hidden sm:inline">Sort</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-[180px]">
                    <DropdownMenuLabel>Sort by</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>Name (A-Z)</DropdownMenuItem>
                    <DropdownMenuItem>Name (Z-A)</DropdownMenuItem>
                    <DropdownMenuItem>Price (Low to High)</DropdownMenuItem>
                    <DropdownMenuItem>Price (High to Low)</DropdownMenuItem>
                    <DropdownMenuItem>Date Added (Newest)</DropdownMenuItem>
                    <DropdownMenuItem>Date Added (Oldest)</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
              <div className="relative flex-grow w-full sm:w-auto pr-2">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search products..."
                  className="w-full rounded-lg bg-white pl-8 pr-4 h-8"
                  value={searchQuery}
                  onChange={handleSearchInput}
                />
              </div>
            </div>
          </div>

          <TabsContent value="all" className="space-y-4">

            <Card className="shadow-sm border-gray-200 w-full">
              <CardHeader className="p-3 border-b">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">Product Inventory</CardTitle>
                  <CardDescription className="text-xs">
                    Showing {filteredProducts.length} of {productStats.total} products
                  </CardDescription>
                </div>
              </CardHeader>
              <CardContent className="p-0 card-content-wrapper w-full">
                {/* Scroll indicator */}
                <div className="flex flex-col sm:flex-row items-center justify-end px-3 py-1 text-xs text-gray-500 bg-gray-50 border-b">
                  <span className="flex items-center mr-0 sm:mr-4 mb-1 sm:mb-0">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                      <polyline points="15 18 9 12 15 6"></polyline>
                    </svg>
                    Scroll horizontally to see all columns
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="ml-1">
                      <polyline points="9 18 15 12 9 6"></polyline>
                    </svg>
                  </span>
                  <span className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                      <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                    Scroll vertically to see all products
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="ml-1">
                      <polyline points="6 15 12 9 18 15"></polyline>
                    </svg>
                  </span>
                </div>

                {/* Table wrapper with horizontal scrolling */}
                <div
                  className="table-container w-full"
                  style={{
                    overflowX: 'auto',
                    overflowY: 'auto',
                    borderRadius: '0',
                    maxHeight: '500px',
                    height: 'auto',
                    minHeight: '350px',
                    position: 'relative',
                    display: 'block',
                    WebkitOverflowScrolling: 'touch',
                    touchAction: 'pan-x pan-y',
                    msOverflowStyle: 'none',
                    scrollbarWidth: 'auto'
                  }}
                  data-scrollable="true"
                >
                  <TooltipProvider>
                    <table
                      style={{
                        width: 'auto',
                        minWidth: '100%',
                        borderCollapse: 'collapse',
                        fontSize: '0.875rem',
                        tableLayout: 'auto'
                      }}
                    >
                    <TableHeader>
                      <TableRow className="bg-gray-50 border-b-2 border-gray-200">
                        <TableHead className="sticky left-0 bg-gray-50 z-10 font-semibold" style={{ width: 'auto', minWidth: '40px' }}>
                          <Checkbox />
                        </TableHead>
                        <TableHead style={{ width: 'auto', minWidth: '80px' }}>Actions</TableHead>
                        <TableHead style={{ width: 'auto', minWidth: '60px' }}>
                          <div className="flex items-center gap-1">
                            Image
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Info className="h-3 w-3 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p className="w-[200px] text-xs">A URL to an image of the product</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableHead>
                        <TableHead style={{ width: 'auto', minWidth: '50px' }}>
                          <div className="flex items-center gap-1">
                            ID
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Info className="h-3 w-3 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p className="w-[200px] text-xs">The primary key for the product</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableHead>
                        <TableHead style={{ width: 'auto', minWidth: '150px' }}>
                          <div className="flex items-center gap-1">
                            Name
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Info className="h-3 w-3 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p className="w-[200px] text-xs">The name of the product (max 100 characters)</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableHead>
                        <TableHead style={{ width: 'auto', minWidth: '100px' }}>
                          <div className="flex items-center gap-1">
                            Category
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Info className="h-3 w-3 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p className="w-[200px] text-xs">The category of the product</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableHead>
                        <TableHead style={{ width: 'auto', minWidth: '80px' }}>
                          <div className="flex items-center gap-1">
                            Price
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Info className="h-3 w-3 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p className="w-[200px] text-xs">The price of the product</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableHead>
                        <TableHead style={{ width: 'auto', minWidth: '100px' }}>
                          <div className="flex items-center gap-1">
                            Slug
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Info className="h-3 w-3 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p className="w-[200px] text-xs">URL-friendly version of the product name</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableHead>
                        <TableHead style={{ width: 'auto', minWidth: '70px' }}>
                          <div className="flex items-center gap-1">
                            Stock
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Info className="h-3 w-3 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p className="w-[200px] text-xs">Current inventory quantity</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableHead>
                        <TableHead style={{ width: 'auto', minWidth: '80px' }}>
                          <div className="flex items-center gap-1">
                            SKU
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Info className="h-3 w-3 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p className="w-[200px] text-xs">Stock Keeping Unit - unique identifier</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableHead>

                        <TableHead style={{ width: 'auto', minWidth: '70px' }}>
                          <div className="flex items-center gap-1">
                            Unit
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Info className="h-3 w-3 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p className="w-[200px] text-xs">Unit of measurement (e.g., kg, piece, liter)</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableHead>
                        <TableHead style={{ width: 'auto', minWidth: '90px' }}>
                          <div className="flex items-center gap-1">
                            Status
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Info className="h-3 w-3 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p className="w-[200px] text-xs">
                                  Indicates whether the product is available for purchase
                                </p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableHead>
                        <TableHead style={{ width: 'auto', minWidth: '90px' }}>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-1">
                              Variants
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Info className="h-3 w-3 text-muted-foreground cursor-help" />
                                </TooltipTrigger>
                                <TooltipContent side="top">
                                  <p className="w-[200px] text-xs">
                                    Different versions of the product (size, color, etc.)
                                  </p>
                                </TooltipContent>
                              </Tooltip>
                            </div>
                            {getTotalVariantsCount() > 0 && (
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0"
                                onClick={toggleAllProductExpansions}
                                title={hasExpandedProducts() ? "Collapse all variants" : "Expand all variants"}
                              >
                                {hasExpandedProducts() ? (
                                  <ChevronDown className="h-3 w-3" />
                                ) : (
                                  <ChevronRight className="h-3 w-3" />
                                )}
                              </Button>
                            )}
                          </div>
                        </TableHead>
                        <TableHead style={{ width: 'auto', minWidth: '90px' }}>
                          <div className="flex items-center gap-1">
                            Featured
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Info className="h-3 w-3 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p className="w-[200px] text-xs">Indicates whether the product is featured</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableHead>
                        <TableHead style={{ width: 'auto', minWidth: '90px' }}>
                          <div className="flex items-center gap-1">
                            Popular
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Info className="h-3 w-3 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p className="w-[200px] text-xs">Indicates whether the product is popular</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableHead>
                        <TableHead style={{ width: 'auto', minWidth: '100px' }}>
                          <div className="flex items-center gap-1">
                            Created
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Info className="h-3 w-3 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p className="w-[200px] text-xs">The timestamp when the product was created</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableHead>
                        <TableHead style={{ width: 'auto', minWidth: '100px' }}>
                          <div className="flex items-center gap-1">
                            Updated
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Info className="h-3 w-3 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p className="w-[200px] text-xs">The timestamp when the product was last updated</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredProducts.length > 0 ? (
                        filteredProducts.map((product) => (
                          <React.Fragment key={product.id}>
                            <TableRow className="group hover:bg-gray-50 transition-colors">
                              <TableCell className="sticky left-0 bg-white group-hover:bg-gray-50 z-10 w-10">
                                <Checkbox />
                              </TableCell>
                              <TableCell>
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="icon">
                                      <MoreHorizontal className="h-4 w-4" />
                                      <span className="sr-only">Open menu</span>
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="start">
                                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                    <DropdownMenuItem onClick={() => {
                                      setCurrentProduct(product)
                                      setIsAddProductDialogOpen(true)
                                    }}>
                                      <Edit className="mr-2 h-4 w-4" />
                                      Edit Product
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem
                                      className="text-red-600"
                                      onClick={async () => {
                                        if (confirm(`Are you sure you want to delete "${product.name}"? This action cannot be undone.`)) {
                                          try {
                                            const response = await fetch(`/api/business-admin/products/${product.id}`, {
                                              method: "DELETE",
                                              headers: addAuthHeaders()
                                            })

                                            if (!response.ok) {
                                              throw new Error("Failed to delete product")
                                            }

                                            toast({
                                              title: "Product Deleted",
                                              description: `${product.name} has been deleted`,
                                              variant: "default"
                                            })

                                            // Refetch products to update the list
                                            refetchProducts()
                                          } catch (error: any) {
                                            console.error("Error deleting product:", error)
                                            toast({
                                              variant: "destructive",
                                              title: "Error",
                                              description: error.message || "Failed to delete product",
                                              duration: 5000
                                            })
                                          }
                                        }
                                      }}
                                    >
                                      <Trash2 className="mr-2 h-4 w-4" />
                                      Delete Product
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </TableCell>
                              <TableCell>
                                <div className="w-10 h-10 rounded-md overflow-hidden">
                                  <img
                                    src={product.image_url || "/placeholder.svg"}
                                    alt={product.name}
                                    width={40}
                                    height={40}
                                    className="rounded-md object-cover"
                                  />
                                </div>
                              </TableCell>
                              <TableCell className="font-medium">{product.id}</TableCell>
                              <TableCell className="font-medium">
                                <div className="font-medium">
                                  {product.name}
                                </div>
                                <div className="text-xs text-muted-foreground description-cell">
                                  {product.description}
                                </div>
                              </TableCell>
                              <TableCell>
                                {product.categories?.name || "Uncategorized"}
                              </TableCell>
                              <TableCell>
                                <DisplayCell
                                  value={product.price}
                                  formatter={(val) => formatCurrency(val)}
                                />
                              </TableCell>
                              <TableCell>
                                <DisplayCell
                                  value={product.slug}
                                />
                              </TableCell>
                              <TableCell>
                                <DisplayCell
                                  value={product.quantity}
                                />
                              </TableCell>
                              <TableCell>
                                <DisplayCell
                                  value="-"
                                />
                              </TableCell>

                              <TableCell>
                                <DisplayCell
                                  value={product.unit}
                                />
                              </TableCell>
                            <TableCell>
                              <DisplayCell
                                value={product.is_available ? "Available" : "Unavailable"}
                              />
                            </TableCell>
                            <TableCell>
                              {product.variants && product.variants.length > 0 ? (
                                <div className="flex items-center">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0"
                                    onClick={() => toggleProductExpansion(product.id)}
                                    aria-label={expandedProducts[product.id] ? "Collapse variants" : "Expand variants"}
                                  >
                                    {expandedProducts[product.id] ? (
                                      <ChevronDown className="h-4 w-4" />
                                    ) : (
                                      <ChevronRight className="h-4 w-4" />
                                    )}
                                  </Button>
                                  <Badge
                                    variant="outline"
                                    className="bg-blue-50 text-blue-700 hover:bg-blue-100 hover:text-blue-800 ml-1 cursor-pointer"
                                    onClick={() => toggleProductExpansion(product.id)}
                                  >
                                    <Layers className="mr-1 h-3 w-3" /> {product.variants.length}
                                  </Badge>
                                </div>
                              ) : (
                                <span className="text-muted-foreground text-sm">-</span>
                              )}
                            </TableCell>
                            <TableCell>
                              <DisplayCell
                                value={product.is_featured ? "Yes" : "No"}
                              />
                            </TableCell>
                            <TableCell>
                              <DisplayCell
                                value={product.is_popular ? "Yes" : "No"}
                              />
                            </TableCell>
                            <TableCell className="text-sm text-muted-foreground">
                              {formatDate(product.created_at)}
                            </TableCell>
                            <TableCell className="text-sm text-muted-foreground">
                              {formatDate(product.updated_at)}
                            </TableCell>
                          </TableRow>

                          {/* Variant rows - only shown when product is expanded */}
                          {expandedProducts[product.id] && product.variants && product.variants.length > 0 && (
                            product.variants.map((variant, index) => (
                              <TableRow key={`variant-${product.id}-${variant.id || index}`} className="group bg-gray-50 hover:bg-gray-100 transition-colors">
                                <TableCell className="sticky left-0 bg-gray-50 group-hover:bg-gray-100 z-10 w-10"></TableCell>
                                <TableCell>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0"
                                    onClick={() => {
                                      setCurrentProduct(product)
                                      setIsAddProductDialogOpen(true)
                                    }}
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                </TableCell>
                                <TableCell></TableCell>
                                <TableCell className="text-xs text-muted-foreground">
                                  {variant.id || `v${index+1}`}
                                </TableCell>
                                <TableCell>
                                  <div className="flex items-center">
                                    <div className="w-4 h-0 border-t border-gray-300 mr-2"></div>
                                    <div>
                                      <div className="font-medium text-sm">{variant.name}</div>
                                      {variant.is_default && (
                                        <Badge variant="outline" className="text-xs bg-gray-100 text-gray-700 mt-1">
                                          Default
                                        </Badge>
                                      )}
                                    </div>
                                  </div>
                                </TableCell>
                                <TableCell></TableCell>
                                <TableCell>
                                  <DisplayCell
                                    value={variant.price || 0}
                                    formatter={(val) => formatCurrency(val)}
                                  />
                                </TableCell>
                                <TableCell>
                                  <DisplayCell
                                    value="-"
                                  />
                                </TableCell>
                                <TableCell>
                                  <DisplayCell
                                    value="-"
                                  />
                                </TableCell>
                                <TableCell>
                                  <DisplayCell
                                    value="-"
                                  />
                                </TableCell>

                                <TableCell>
                                  <DisplayCell
                                    value="-"
                                  />
                                </TableCell>
                                <TableCell>
                                  <DisplayCell
                                    value="-"
                                  />
                                </TableCell>
                                <TableCell></TableCell>
                                <TableCell>
                                  <DisplayCell
                                    value="-"
                                  />
                                </TableCell>
                                <TableCell className="text-xs text-muted-foreground">
                                  {formatDate(variant.created_at || product.created_at)}
                                </TableCell>
                                <TableCell className="text-xs text-muted-foreground">
                                  {formatDate(variant.updated_at || product.updated_at)}
                                </TableCell>
                              </TableRow>
                            ))
                          )}
                          </React.Fragment>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={18} className="h-24 text-center">
                            <div className="flex flex-col items-center justify-center">
                              <Package className="h-8 w-8 text-gray-300 mb-2" />
                              <p className="text-gray-500">
                                {selectedCategory !== "all"
                                  ? `No products found in the ${categories.find(c => c.id.toString() === selectedCategory)?.name || 'selected'} category.`
                                  : "No products found."}
                              </p>
                              <p className="text-xs text-gray-400 mt-1">
                                {selectedCategory !== "all"
                                  ? "Try selecting a different category or add a new product."
                                  : "Try adjusting your filters or add a new product."}
                              </p>
                              <Button
                                variant="outline"
                                size="sm"
                                className="mt-4"
                                onClick={() => setIsAddProductDialogOpen(true)}
                              >
                                <Plus className="h-4 w-4 mr-1" /> Add Product
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                    </table>
                  </TooltipProvider>
                </div>
              </CardContent>
              <CardFooter className="flex items-center justify-between p-3 border-t bg-gray-50">
                <div className="text-xs text-muted-foreground">
                  Showing {filteredProducts.length} of {productStats.total} products
                </div>
                <div className="flex items-center gap-1">
                  <Button variant="outline" size="sm" className="h-7 text-xs" disabled={filteredProducts.length === 0}>
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-7 w-7 p-0 text-xs"
                    disabled={filteredProducts.length === 0}
                  >
                    1
                  </Button>
                  <Button variant="outline" size="sm" className="h-7 text-xs" disabled={filteredProducts.length === 0}>
                    Next
                  </Button>
                </div>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="active" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Active Products</CardTitle>
                <CardDescription>Products that are currently available for purchase</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  {filteredProducts.length > 0 ? (
                    <p>Showing only active products. Use the filter options above to refine your view.</p>
                  ) : (
                    <p>No active products found.</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="draft" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Draft Products</CardTitle>
                <CardDescription>Products that are not yet published</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  {filteredProducts.length > 0 ? (
                    <p>Showing only draft products. Use the filter options above to refine your view.</p>
                  ) : (
                    <p>No draft products found.</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="archived" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Archived Products</CardTitle>
                <CardDescription>Products that have been archived</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  {filteredProducts.length > 0 ? (
                    <p>Showing only archived products. Use the filter options above to refine your view.</p>
                  ) : (
                    <p>No archived products found.</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Add/Edit Product Dialog */}
      <Dialog open={isAddProductDialogOpen} onOpenChange={setIsAddProductDialogOpen}>
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle>{currentProduct ? "Edit Product" : "Add New Product"}</DialogTitle>
            <DialogDescription>
              {currentProduct
                ? "Update the details of this product"
                : "Fill in the details to add a new product to your catalog"}
            </DialogDescription>
          </DialogHeader>
          <ProductForm
            product={currentProduct || undefined}
            categoryId={selectedCategory !== "all" ? parseInt(selectedCategory) : undefined}
            categories={categories}
            onSubmit={async (productData) => {
              try {
                if (currentProduct) {
                  // Update existing product
                  const response = await fetch(`/api/business-admin/products/${currentProduct.id}`, {
                    method: "PATCH",
                    headers: {
                      "Content-Type": "application/json",
                      ...addAuthHeaders()
                    },
                    body: JSON.stringify(productData)
                  })

                  if (!response.ok) {
                    throw new Error("Failed to update product")
                  }

                  await response.json() // Process response but we don't need the data

                  toast({
                    title: "Product Updated",
                    description: "Product has been updated successfully",
                    duration: 3000
                  })
                } else {
                  // Create new product
                  const response = await fetch("/api/business-admin/products", {
                    method: "POST",
                    headers: {
                      "Content-Type": "application/json",
                      ...addAuthHeaders()
                    },
                    body: JSON.stringify(productData)
                  })

                  if (!response.ok) {
                    throw new Error("Failed to create product")
                  }

                  await response.json() // Process response but we don't need the data

                  toast({
                    title: "Product Added",
                    description: "Product has been added successfully",
                    duration: 3000
                  })
                }

                // Close the dialog
                setIsAddProductDialogOpen(false)

                // Refetch products to update the list
                refetchProducts()
              } catch (error: any) {
                console.error("Error saving product:", error)
                toast({
                  variant: "destructive",
                  title: "Error",
                  description: error.message || "Failed to save product",
                  duration: 5000
                })
              }
            }}
            onCancel={() => {
              setIsAddProductDialogOpen(false)
            }}
          />
        </DialogContent>
      </Dialog>
    </TooltipProvider>
  </div>
  )
}
