import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { createServerSupabase } from "@/lib/supabase-server"

// Create a Supabase client with the service role key
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET() {
  try {
    // First, verify the user is authenticated and has admin permissions
    const authClient = await createServerSupabase()

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession()

    console.log("Session check result:", {
      hasSession: !!session,
      sessionError: sessionError ? sessionError.message : null,
      userEmail: session?.user?.email || null
    })

    if (sessionError || !session) {
      console.error("No authenticated session found:", sessionError)
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Get the user's profile to check their role
    console.log("Checking user profile for email:", session.user.email)

    // First, let's check if the user exists in the users table
    const adminClient = createClient(supabaseUrl, supabaseServiceKey)
    const { data: userExists, error: userExistsError } = await adminClient
      .from("users")
      .select("email")
      .eq("email", session.user.email)

    console.log("User exists check:", {
      found: userExists && userExists.length > 0,
      error: userExistsError ? userExistsError.message : null,
      data: userExists
    })

    // Now get the user profile with role
    let { data: userProfile, error: profileError } = await authClient
      .from("users")
      .select("role")
      .eq("email", session.user.email)
      .single()

    console.log("User profile check:", {
      hasProfile: !!userProfile,
      role: userProfile?.role || null,
      error: profileError ? profileError.message : null
    })

    if (profileError || !userProfile) {
      console.error("Error fetching user profile:", profileError)

      // Instead of returning an error, let's use the admin client as a fallback
      console.log("Trying to get user profile with admin client as fallback")
      const { data: adminUserProfile, error: adminProfileError } = await adminClient
        .from("users")
        .select("role")
        .eq("email", session.user.email)
        .single()

      console.log("Admin client profile check:", {
        hasProfile: !!adminUserProfile,
        role: adminUserProfile?.role || null,
        error: adminProfileError ? adminProfileError.message : null
      })

      if (adminProfileError || !adminUserProfile) {
        console.error("Failed to get user profile with admin client:", adminProfileError)
        return NextResponse.json(
          { error: "User profile not found" },
          { status: 403 }
        )
      }

      // Use the admin client result
      console.log("Using admin client result for user profile")
      userProfile = adminUserProfile
    }

    // Check if the user has admin or super_admin role
    if (userProfile.role !== 'admin' && userProfile.role !== 'super_admin') {
      console.error("Unauthorized access attempt by:", session.user.email, "with role:", userProfile.role)
      return NextResponse.json(
        { error: "You do not have permission to access this resource" },
        { status: 403 }
      )
    }

    console.log("Admin access verified for user:", session.user.email, "with role:", userProfile.role)

    // Now create a Supabase client with the service role key for admin operations
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Fetch users with the service role key
    const { data, error } = await supabase
      .from("users")
      .select("*")
      .order("created_at", { ascending: false })

    if (error) {
      console.error("Error fetching users:", error)
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      )
    }

    return NextResponse.json({ users: data })
  } catch (error: any) {
    console.error("Unexpected error in users API:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
