import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';

// Create Supabase client with service role key (only available server-side)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Helper function to get the current user from request
async function getCurrentUser(request: NextRequest) {
  let user = null;

  // First try to get the user from the Authorization header
  const authHeader = request.headers.get('Authorization');
  console.log("CART-API: Authorization header:", authHeader ? `${authHeader.substring(0, 15)}...` : 'None');

  if (authHeader && authHeader.startsWith('Bearer ')) {
    const token = authHeader.substring(7);
    console.log("CART-API: Found Bearer token, length:", token.length);

    // Create a Supabase client with the token
    const supabaseClientWithToken = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        },
        global: {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      }
    );

    // Get the user from the token
    console.log("CART-API: Attempting to get user from token");
    const { data: userData, error: userError } = await supabaseClientWithToken.auth.getUser();

    if (userError) {
      console.log("CART-API: Error getting user from token:", userError.message);
    }

    if (!userError && userData?.user) {
      console.log("CART-API: Successfully retrieved user from token:", userData.user.email);
      user = userData.user;
    } else {
      console.log("CART-API: No user found from token");
    }
  }

  // If we couldn't get the user from the Authorization header, try cookies
  if (!user) {
    console.log("CART-API: No user from Authorization header, trying cookies");
    try {
      // Get auth token directly from request headers
      const cookieHeader = request.headers.get('cookie') || '';
      const cookiesObj = cookieHeader.split(';').reduce((acc, cookie) => {
        const [key, value] = cookie.trim().split('=');
        if (key) acc[key] = value;
        return acc;
      }, {} as Record<string, string>);

      const authCookie = { value: cookiesObj['loop_jersey_auth_token'] };
      console.log("CART-API: Auth cookie present:", authCookie.value ? "Yes" : "No");

      // If we have an auth cookie, try to use it directly
      if (authCookie && authCookie.value) {
        console.log("CART-API: Using auth cookie token directly");
        const token = authCookie.value;

        // Create a Supabase client with the token
        const supabaseClientWithToken = createClient(
          process.env.NEXT_PUBLIC_SUPABASE_URL || '',
          process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
          {
            auth: {
              autoRefreshToken: false,
              persistSession: false
            },
            global: {
              headers: {
                Authorization: `Bearer ${token}`
              }
            }
          }
        );

        // Get the user from the token
        const { data: tokenUserData, error: tokenUserError } = await supabaseClientWithToken.auth.getUser();

        if (!tokenUserError && tokenUserData?.user) {
          console.log("CART-API: Successfully retrieved user from cookie token:", tokenUserData.user.email);
          user = tokenUserData.user;
          return user;
        }
      }

      // If direct token approach failed, try the standard cookie approach
      console.log("CART-API: Trying standard cookie approach");
      const supabaseClient = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL || '',
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
        {
          auth: {
            autoRefreshToken: false,
            persistSession: false
          }
        }
      );

      const { data: userData, error: userError } = await supabaseClient.auth.getUser();

      if (userError) {
        console.log("CART-API: Error getting user from standard cookies:", userError.message);
      }

      if (userData?.user) {
        console.log("CART-API: Successfully retrieved user from standard cookies:", userData.user.email);
        user = userData.user;
      } else {
        console.log("CART-API: No user found from standard cookies");
      }
    } catch (error) {
      console.error("CART-API: Error getting user from cookies:", error);
    }
  }

  return user;
}

// Handle real-time cart actions
async function handleRealtimeAction(request: NextRequest, body: any) {
  const { action, userId, sessionId } = body;

  // Determine if this is a user or guest cart
  const isUserCart = !!userId;
  const cartIdentifier = userId || sessionId;

  if (!cartIdentifier) {
    return NextResponse.json(
      { error: 'User ID or session ID required' },
      { status: 400 }
    );
  }

  try {
    switch (action) {
      case 'add':
        return await handleAddItem(body, isUserCart, cartIdentifier);
      case 'remove':
        return await handleRemoveItem(body, isUserCart, cartIdentifier);
      case 'update':
        return await handleUpdateQuantity(body, isUserCart, cartIdentifier);
      case 'clear':
        return await handleClearCart(body, isUserCart, cartIdentifier);
      case 'updateDeliveryMethod':
        return await handleUpdateDeliveryMethod(body, isUserCart, cartIdentifier);
      case 'updateDeliveryFee':
        return await handleUpdateDeliveryFee(body, isUserCart, cartIdentifier);
      case 'updateDeliveryType':
        return await handleUpdateDeliveryType(body, isUserCart, cartIdentifier);
      case 'updateScheduledTime':
        return await handleUpdateScheduledTime(body, isUserCart, cartIdentifier);
      case 'updatePreparationTime':
        return await handleUpdatePreparationTime(body, isUserCart, cartIdentifier);
      case 'updateDeliveryTime':
        return await handleUpdateDeliveryTime(body, isUserCart, cartIdentifier);
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error: any) {
    console.error('Error handling real-time action:', error);
    return NextResponse.json(
      { error: 'Failed to process action', details: error.message },
      { status: 500 }
    );
  }
}

// Add item to cart
async function handleAddItem(body: any, isUserCart: boolean, cartIdentifier: string) {
  const { item } = body;

  // Validate required item fields
  if (!item) {
    throw new Error('Item data is required');
  }
  if (!item.id) {
    throw new Error('Item ID is required');
  }
  if (!item.businessId) {
    throw new Error('Business ID is required');
  }
  if (!item.name) {
    throw new Error('Item name is required');
  }
  if (typeof item.price !== 'number') {
    throw new Error('Item price must be a number');
  }
  if (typeof item.quantity !== 'number' || item.quantity <= 0) {
    throw new Error('Item quantity must be a positive number');
  }

  // Extract cart context values from body (if provided)
  const cartContextValues = {
    deliveryMethods: body.deliveryMethods || {},
    deliveryType: body.deliveryType || {},
    scheduledTimes: body.scheduledTimes || {},
    deliveryFees: body.deliveryFees || {},
    preparationTimes: body.preparationTimes || {},
    deliveryTimes: body.deliveryTimes || {}
  };

  // Get or create cart with context values
  const cartId = await getOrCreateCartWithContext(isUserCart, cartIdentifier, cartContextValues);

  // Check if item already exists with same variant
  // Build the query conditionally based on whether variantId exists
  let query = supabase
    .from('cart_items')
    .select('*')
    .eq('cart_id', cartId)
    .eq('product_id', item.id)
    .eq('business_id', item.businessId);

  // Handle variant_id properly - if it's undefined, null, or "null", query for IS NULL
  if (item.variantId && item.variantId !== 'null' && item.variantId !== null) {
    query = query.eq('variant_id', item.variantId);
  } else {
    query = query.is('variant_id', null);
  }

  const { data: existingItem, error: findError } = await query.single();

  if (findError && findError.code !== 'PGRST116') {
    throw findError;
  }

  if (existingItem) {
    // Update quantity
    const { error: updateError } = await supabase
      .from('cart_items')
      .update({
        quantity: existingItem.quantity + item.quantity,
        updated_at: new Date().toISOString()
      })
      .eq('id', existingItem.id);

    if (updateError) {
      throw updateError;
    }
  } else {
    // Insert new item
    const insertData = {
      cart_id: cartId,
      product_id: item.id,
      variant_id: (item.variantId && item.variantId !== 'null' && item.variantId !== null) ? item.variantId : null,
      business_id: item.businessId, // Primary key for business identification
      business_slug: item.businessSlug, // Additional metadata for routing
      business_type: item.businessType,
      name: item.name,
      price: item.price,
      quantity: item.quantity,
      image_url: item.imageUrl,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data: newCartItem, error: insertError } = await supabase
      .from('cart_items')
      .insert(insertData)
      .select('id')
      .single();

    if (insertError) {
      throw insertError;
    }

    // Add customizations if present
    if (item.customizations && item.customizations.length > 0 && newCartItem) {
      const customizationInserts = [];

      for (const group of item.customizations) {
        for (const option of group.options) {
          customizationInserts.push({
            cart_item_id: newCartItem.id,
            customization_group_id: group.groupId,
            customization_group_name: group.groupName,
            customization_option_id: option.id,
            customization_option_name: option.name,
            price: option.price,
            created_at: new Date().toISOString()
          });
        }
      }

      if (customizationInserts.length > 0) {
        const { error: customizationError } = await supabase
          .from('cart_item_customizations')
          .insert(customizationInserts);

        if (customizationError) throw customizationError;
      }
    }
  }

  // Update cart timestamp
  await updateCartTimestamp(isUserCart, cartId);

  return NextResponse.json({ success: true });
}

// Remove item from cart
async function handleRemoveItem(body: any, isUserCart: boolean, cartIdentifier: string) {
  const { itemId, variantId } = body;

  const cartId = await getOrCreateCart(isUserCart, cartIdentifier);

  // Find the cart item to delete (including customizations)
  let deleteQuery = supabase
    .from('cart_items')
    .select('id')
    .eq('cart_id', cartId)
    .eq('product_id', itemId);

  // Handle variant_id properly
  if (variantId && variantId !== 'null' && variantId !== null) {
    deleteQuery = deleteQuery.eq('variant_id', variantId);
  } else {
    deleteQuery = deleteQuery.is('variant_id', null);
  }

  const { data: cartItem, error: findError } = await deleteQuery.single();

  if (findError && findError.code !== 'PGRST116') throw findError;

  if (cartItem) {
    // Delete customizations first (due to foreign key constraint)
    const { error: customizationError } = await supabase
      .from('cart_item_customizations')
      .delete()
      .eq('cart_item_id', cartItem.id);

    if (customizationError) throw customizationError;

    // Delete the cart item
    const { error: deleteError } = await supabase
      .from('cart_items')
      .delete()
      .eq('id', cartItem.id);

    if (deleteError) throw deleteError;
  }

  await updateCartTimestamp(isUserCart, cartId);

  return NextResponse.json({ success: true });
}

// Update item quantity
async function handleUpdateQuantity(body: any, isUserCart: boolean, cartIdentifier: string) {
  const { itemId, quantity, variantId } = body;

  const cartId = await getOrCreateCart(isUserCart, cartIdentifier);

  // Build the update query conditionally
  let updateQuery = supabase
    .from('cart_items')
    .update({
      quantity: quantity,
      updated_at: new Date().toISOString()
    })
    .eq('cart_id', cartId)
    .eq('product_id', itemId);

  // Handle variant_id properly
  if (variantId && variantId !== 'null' && variantId !== null) {
    updateQuery = updateQuery.eq('variant_id', variantId);
  } else {
    updateQuery = updateQuery.is('variant_id', null);
  }

  const { error } = await updateQuery;

  if (error) throw error;

  await updateCartTimestamp(isUserCart, cartId);

  return NextResponse.json({ success: true });
}

// Clear cart
async function handleClearCart(body: any, isUserCart: boolean, cartIdentifier: string) {
  const cartId = await getOrCreateCart(isUserCart, cartIdentifier);

  // Delete all cart items
  const { error: deleteItemsError } = await supabase
    .from('cart_items')
    .delete()
    .eq('cart_id', cartId);

  if (deleteItemsError) throw deleteItemsError;

  // Clear delivery methods and fees
  const { error: updateError } = await supabase
    .from('user_carts')
    .update({
      delivery_type: '{}',
      delivery_fees: {},
      updated_at: new Date().toISOString()
    })
    .eq('id', cartId);

  if (updateError) throw updateError;

  return NextResponse.json({ success: true });
}

// Update delivery method (pickup/delivery choice)
async function handleUpdateDeliveryMethod(body: any, isUserCart: boolean, cartIdentifier: string) {
  const { businessId, method } = body;

  const cartId = await getOrCreateCart(isUserCart, cartIdentifier);

  // Get current delivery_method (which stores pickup/delivery choices)
  const { data: cart, error: fetchError } = await supabase
    .from('user_carts')
    .select('delivery_method')
    .eq('id', cartId)
    .single();

  if (fetchError) throw fetchError;

  // Parse existing delivery methods from delivery_method column
  const deliveryMethods = cart?.delivery_method || {};
  deliveryMethods[businessId] = method;

  const { error: updateError } = await supabase
    .from('user_carts')
    .update({
      delivery_method: deliveryMethods,
      updated_at: new Date().toISOString()
    })
    .eq('id', cartId);

  if (updateError) throw updateError;

  return NextResponse.json({ success: true });
}

// Update delivery fee
async function handleUpdateDeliveryFee(body: any, isUserCart: boolean, cartIdentifier: string) {
  const { businessId, fee } = body;

  const cartId = await getOrCreateCart(isUserCart, cartIdentifier);

  // Get current delivery fees
  const { data: cart, error: fetchError } = await supabase
    .from('user_carts')
    .select('delivery_fees')
    .eq('id', cartId)
    .single();

  if (fetchError) throw fetchError;

  const deliveryFees = cart?.delivery_fees || {};
  deliveryFees[businessId] = fee;

  const { error: updateError } = await supabase
    .from('user_carts')
    .update({
      delivery_fees: deliveryFees,
      updated_at: new Date().toISOString()
    })
    .eq('id', cartId);

  if (updateError) throw updateError;

  return NextResponse.json({ success: true });
}

// Update delivery type (asap/scheduled timing)
async function handleUpdateDeliveryType(body: any, isUserCart: boolean, cartIdentifier: string) {
  const { businessId, type } = body;

  const cartId = await getOrCreateCart(isUserCart, cartIdentifier);

  // Get current delivery_type (which stores asap/scheduled timing)
  const { data: cart, error: fetchError } = await supabase
    .from('user_carts')
    .select('delivery_type')
    .eq('id', cartId)
    .single();

  if (fetchError) throw fetchError;

  // Parse existing delivery types from delivery_type column
  const deliveryTypes = cart?.delivery_type || {};
  deliveryTypes[businessId] = type;

  const { error: updateError } = await supabase
    .from('user_carts')
    .update({
      delivery_type: deliveryTypes,
      updated_at: new Date().toISOString()
    })
    .eq('id', cartId);

  if (updateError) throw updateError;

  return NextResponse.json({ success: true });
}

// Update scheduled time
async function handleUpdateScheduledTime(body: any, isUserCart: boolean, cartIdentifier: string) {
  const { businessId, scheduledTime } = body;

  const cartId = await getOrCreateCart(isUserCart, cartIdentifier);

  // Get current scheduled_times
  const { data: cart, error: fetchError } = await supabase
    .from('user_carts')
    .select('scheduled_times')
    .eq('id', cartId)
    .single();

  if (fetchError) throw fetchError;

  // Parse existing scheduled times from scheduled_times column
  const scheduledTimes = cart?.scheduled_times || {};

  if (scheduledTime) {
    scheduledTimes[businessId] = scheduledTime;
  } else {
    // Remove scheduled time if null/undefined (switching to asap)
    delete scheduledTimes[businessId];
  }

  const { error: updateError } = await supabase
    .from('user_carts')
    .update({
      scheduled_times: scheduledTimes,
      updated_at: new Date().toISOString()
    })
    .eq('id', cartId);

  if (updateError) throw updateError;

  return NextResponse.json({ success: true });
}

// Update preparation time
async function handleUpdatePreparationTime(body: any, isUserCart: boolean, cartIdentifier: string) {
  const { businessId, time } = body;

  const cartId = await getOrCreateCart(isUserCart, cartIdentifier);

  // Get current preparation_times
  const { data: cart, error: fetchError } = await supabase
    .from('user_carts')
    .select('preparation_times')
    .eq('id', cartId)
    .single();

  if (fetchError) throw fetchError;

  // Parse existing preparation times from preparation_times column
  const preparationTimes = cart?.preparation_times || {};

  if (time !== null && time !== undefined) {
    preparationTimes[businessId] = time;
  } else {
    // Remove preparation time if null/undefined
    delete preparationTimes[businessId];
  }

  const { error: updateError } = await supabase
    .from('user_carts')
    .update({
      preparation_times: preparationTimes,
      updated_at: new Date().toISOString()
    })
    .eq('id', cartId);

  if (updateError) throw updateError;

  return NextResponse.json({ success: true });
}

// Update delivery time
async function handleUpdateDeliveryTime(body: any, isUserCart: boolean, cartIdentifier: string) {
  const { businessId, time } = body;

  const cartId = await getOrCreateCart(isUserCart, cartIdentifier);

  // Get current delivery_times
  const { data: cart, error: fetchError } = await supabase
    .from('user_carts')
    .select('delivery_times')
    .eq('id', cartId)
    .single();

  if (fetchError) throw fetchError;

  // Parse existing delivery times from delivery_times column
  const deliveryTimes = cart?.delivery_times || {};

  if (time !== null && time !== undefined) {
    deliveryTimes[businessId] = time;
  } else {
    // Remove delivery time if null/undefined
    delete deliveryTimes[businessId];
  }

  const { error: updateError } = await supabase
    .from('user_carts')
    .update({
      delivery_times: deliveryTimes,
      updated_at: new Date().toISOString()
    })
    .eq('id', cartId);

  if (updateError) throw updateError;

  return NextResponse.json({ success: true });
}

// Get or create cart
async function getOrCreateCart(isUserCart: boolean, identifier: string): Promise<string> {
  return getOrCreateCartWithContext(isUserCart, identifier, {});
}

// Get or create cart with context values
async function getOrCreateCartWithContext(
  isUserCart: boolean,
  identifier: string,
  contextValues: any = {}
): Promise<string> {
  const idColumn = isUserCart ? 'user_id' : 'session_id';

  // Try to find existing cart (get the most recent one if multiple exist)
  const { data: existingCarts, error: findError } = await supabase
    .from('user_carts')
    .select('id')
    .eq(idColumn, identifier)
    .order('created_at', { ascending: false })
    .limit(1);

  if (findError) {
    throw findError;
  }

  if (existingCarts && existingCarts.length > 0) {
    const existingCartId = existingCarts[0].id;

    // If we have context values to set, update the existing cart with them
    if (Object.keys(contextValues).length > 0) {
      const updateData: any = {};

      if (contextValues.deliveryFees && Object.keys(contextValues.deliveryFees).length > 0) {
        updateData.delivery_fees = contextValues.deliveryFees;
      }
      if (contextValues.deliveryMethods && Object.keys(contextValues.deliveryMethods).length > 0) {
        updateData.delivery_method = contextValues.deliveryMethods;
      }
      if (contextValues.deliveryType && Object.keys(contextValues.deliveryType).length > 0) {
        updateData.delivery_type = contextValues.deliveryType;
      }
      if (contextValues.scheduledTimes && Object.keys(contextValues.scheduledTimes).length > 0) {
        updateData.scheduled_times = contextValues.scheduledTimes;
      }
      if (contextValues.preparationTimes && Object.keys(contextValues.preparationTimes).length > 0) {
        updateData.preparation_times = contextValues.preparationTimes;
      }
      if (contextValues.deliveryTimes && Object.keys(contextValues.deliveryTimes).length > 0) {
        updateData.delivery_times = contextValues.deliveryTimes;
      }

      if (Object.keys(updateData).length > 0) {
        updateData.updated_at = new Date().toISOString();
        const { error: updateError } = await supabase
          .from('user_carts')
          .update(updateData)
          .eq('id', existingCartId);

        if (updateError) {
          console.error('Error updating existing cart with context values:', updateError);
        }
      }
    }

    return existingCartId;
  }

  // Create new cart with context values
  const cartData: any = {
    [idColumn]: identifier,
    is_anonymous: !isUserCart,
    delivery_fees: contextValues.deliveryFees || {},
    delivery_method: contextValues.deliveryMethods || {}, // pickup/delivery choices per business
    delivery_type: contextValues.deliveryType || {}, // asap/scheduled timing per business
    scheduled_times: contextValues.scheduledTimes || {}, // scheduled times per business
    preparation_times: contextValues.preparationTimes || {},
    delivery_times: contextValues.deliveryTimes || {},
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };



  const { data: newCart, error: createError } = await supabase
    .from('user_carts')
    .insert(cartData)
    .select('id')
    .single();

  if (createError) {
    throw createError;
  }

  return newCart.id;
}

// Update cart timestamp
async function updateCartTimestamp(isUserCart: boolean, cartId: string) {
  const { error } = await supabase
    .from('user_carts')
    .update({
      updated_at: new Date().toISOString()
    })
    .eq('id', cartId);

  if (error) {
    console.error('Error updating cart timestamp:', error);
  }
}

// GET endpoint to fetch a user's cart
export async function GET(request: NextRequest) {
  try {
    // Get the current user
    let user = await getCurrentUser(request);

    if (!user) {
      // In development mode, use a mock user
      if (process.env.NODE_ENV === 'development') {
        console.log("CART-API: Development mode - using mock user");
        user = {
          id: 'e96a586b-3aa2-44b0-b6d9-af3c24e03683',
          email: '<EMAIL>',
          role: 'customer'
        };
      } else {
        return NextResponse.json(
          { error: 'User not authenticated' },
          { status: 401 }
        );
      }
    }

    // Get the user's ID from the users table using auth_id
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id, auth_id')
      .eq('auth_id', user.id)
      .single();

    // If user not found, try to create a new user record
    if (userError || !userData) {
      console.log('User not found in database or error fetching user ID. Attempting to create user record.');

      try {
        // Create a new user record
        const { data: newUser, error: createError } = await supabase
          .from('users')
          .insert([{
            auth_id: user.id,
            email: user.email,
            name: user.email ? user.email.split('@')[0] : 'User',
            role: 'customer',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }])
          .select('id, auth_id')
          .single();

        if (createError) {
          console.error('Error creating user record:', createError);
          // Return empty cart instead of error
          return NextResponse.json({
            cart: { items: [] },
            businessNames: {},
            preparationTimes: {},
            deliveryTimes: {},
            deliveryFees: {},
            deliveryMethods: {},
            deliveryTypes: {},
            scheduledTimes: {}
          });
        }

        if (newUser) {
          console.log('Created new user record:', newUser.id);
          return NextResponse.json({
            cart: { items: [] },
            businessNames: {},
            preparationTimes: {},
            deliveryTimes: {},
            deliveryFees: {},
            deliveryMethods: {},
            deliveryTypes: {},
            scheduledTimes: {},
            userCreated: true
          });
        }
      } catch (createUserError) {
        console.error('Exception creating user record:', createUserError);
        // Return empty cart instead of error
        return NextResponse.json({
          cart: { items: [] },
          businessNames: {},
          preparationTimes: {},
          deliveryTimes: {},
          deliveryFees: {},
          deliveryMethods: {},
          deliveryTypes: {},
          scheduledTimes: {}
        });
      }

      // If we couldn't create a user, return empty cart
      return NextResponse.json({
        cart: { items: [] },
        businessNames: {},
        preparationTimes: {},
        deliveryTimes: {},
        deliveryFees: {},
        deliveryMethods: {},
        deliveryTypes: {},
        scheduledTimes: {}
      });
    }

    // Get or create the user's cart
    let cartId;
    let preparationTimes = {};
    let deliveryTimes = {};
    let deliveryFees = {};
    let deliveryMethods = {};

    // The user_id field in user_carts should reference auth.users(id)
    // This is the auth.uid() value, which is user.id from getCurrentUser
    const userId = user.id;
    console.log('Using user ID for cart lookup in GET:', userId);

    const { data: existingCarts, error: cartError } = await supabase
      .from('user_carts')
      .select('id, preparation_times, delivery_times, delivery_fees, delivery_type, delivery_method, scheduled_times')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(1);

    const existingCart = existingCarts && existingCarts.length > 0 ? existingCarts[0] : null;


    if (cartError && cartError.code !== 'PGRST116') { // PGRST116 is "Results contain 0 rows"
      console.error('Error fetching user cart:', cartError);
      // Continue with creating a new cart instead of returning an error
      console.log('Will attempt to create a new cart instead');
    }

    if (existingCart) {
      cartId = existingCart.id;
      preparationTimes = existingCart.preparation_times || {};
      deliveryTimes = existingCart.delivery_times || {};
      deliveryFees = existingCart.delivery_fees || {};
      // Use delivery_method for pickup/delivery choices (new structure)
      deliveryMethods = existingCart.delivery_method || {};
      console.log('Found existing cart with ID:', cartId);
    } else {
      console.log('No existing cart found, creating new cart for user:', userId);

      // Create a new cart for the user
      const { data: newCart, error: createCartError } = await supabase
        .from('user_carts')
        .insert([{
          user_id: userId,
          updated_at: new Date().toISOString()
        }])
        .select('id')
        .single();

      if (createCartError) {
        console.error('Error creating user cart:', createCartError);

        // Try a different approach if the first one fails
        console.log('Trying alternative approach to create cart...');

        const { data: retryCart, error: retryError } = await supabase
          .from('user_carts')
          .insert([{
            user_id: userId,
            updated_at: new Date().toISOString()
          }])
          .select('id')
          .single();

        if (retryError) {
          console.error('Retry also failed:', retryError);
          return NextResponse.json(
            { error: 'Failed to create user cart', details: retryError.message },
            { status: 500 }
          );
        }

        cartId = retryCart.id;
        console.log('Successfully created cart on retry with ID:', cartId);
      } else {
        cartId = newCart.id;
        console.log('Successfully created new cart with ID:', cartId);
      }
    }

    // Get the cart items
    const { data: cartItems, error: itemsError } = await supabase
      .from('cart_items')
      .select('*')
      .eq('cart_id', cartId);

    if (itemsError) {
      console.error('Error fetching cart items:', itemsError);
      return NextResponse.json(
        { error: 'Failed to fetch cart items', details: itemsError.message },
        { status: 500 }
      );
    }

    // Format the cart items for the response
    const formattedItems = cartItems.map(item => ({
      id: item.product_id.toString(),
      productId: item.product_id,
      variantId: item.variant_id,
      quantity: item.quantity,
      businessId: item.business_id, // Now a numeric ID
      businessSlug: item.business_slug || '', // Include the slug
      businessType: item.business_type,
      name: item.name,
      price: item.price,
      imageUrl: item.image_url,
      deliveryMethod: item.delivery_method || 'delivery',
      deliveryType: item.delivery_type || 'asap',
      scheduledTime: item.scheduled_time || null
    }));

    // Get business names
    const businessIds = [...new Set(cartItems.map(item => item.business_id))];
    const businessNames = {};

    for (const businessId of businessIds) {
      // Look up the business by ID (not slug)
      const { data: business, error: businessError } = await supabase
        .from('businesses')
        .select('name, slug')
        .eq('id', businessId)
        .single();

      if (!businessError && business) {
        // Store the business name keyed by the numeric ID
        businessNames[businessId] = business.name;
        console.log(`Found business name ${business.name} for ID ${businessId}`);
      } else {
        // If we can't find the business, use a generic name
        const formattedName = `Business ${businessId}`;
        businessNames[businessId] = formattedName;
        console.log(`Could not find business with ID ${businessId}, using generic name ${formattedName}`);
      }
    }

    // Return the cart data
    const data = {
      cart: {
        items: formattedItems,
        meta: {
          lastUpdated: new Date().toISOString(),
          itemCount: formattedItems.length
        }
      },
      businessNames,
      preparationTimes,
      deliveryTimes,
      deliveryFees,
      deliveryMethods
    };

    // If no data returned, return empty cart
    if (!data) {
      return NextResponse.json({
        cart: { items: [] },
        businessNames: {},
        preparationTimes: {},
        deliveryTimes: {},
        deliveryFees: {},
        deliveryMethods: {}
      });
    }

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}

// POST endpoint to update a user's cart
export async function POST(request: NextRequest) {
  try {
    // Parse the request body first to check for action
    const body = await request.json();

    // Log the action to identify what's causing continuous calls
    console.log('🔍 CART-API: POST request with action:', body.action, 'body:', JSON.stringify(body, null, 2));

    // Handle different actions for real-time cart operations
    if (body.action) {
      return handleRealtimeAction(request, body);
    }

    // Get the current user for legacy cart operations
    let user = await getCurrentUser(request);

    if (!user) {
      // In development mode, use a mock user
      if (process.env.NODE_ENV === 'development') {
        console.log("CART-API: Development mode - using mock user for POST");
        user = {
          id: 'e96a586b-3aa2-44b0-b6d9-af3c24e03683',
          email: '<EMAIL>',
          role: 'customer'
        };
      } else {
        return NextResponse.json(
          { error: 'User not authenticated' },
          { status: 401 }
        );
      }
    }

    // Handle both formats: body.cart.items (old format) and body.cart (new format)
    let items;
    if (body.cart && Array.isArray(body.cart)) {
      // New format: body.cart is directly an array of items
      items = body.cart;
    } else if (body.cart && body.cart.items && Array.isArray(body.cart.items)) {
      // Old format: body.cart.items is the array
      items = body.cart.items;
    } else {
      return NextResponse.json(
        { error: 'Valid cart data with items array is required' },
        { status: 400 }
      );
    }

    // Process each item in the cart
    const results = [];

    // If the cart is empty, handle it gracefully
    if (items.length === 0) {
      console.log('Empty cart submitted - will clear existing cart if any');
      // Just return success with empty cart
      return NextResponse.json({
        success: true,
        cart: {
          items: [],
          meta: {
            lastUpdated: new Date().toISOString(),
            itemCount: 0
          }
        },
        businessNames: {},
        preparationTimes: {},
        deliveryTimes: {},
        deliveryFees: {},
        deliveryMethods: {},
        deliveryType: {},
        scheduledTimes: {}
      });
    }

    // Get the user's ID from the users table using auth_id
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id, auth_id')
      .eq('auth_id', user.id)
      .single();

    if (userError) {
      console.error('Error fetching user ID:', userError);
      return NextResponse.json(
        { error: 'Failed to fetch user ID', details: userError.message },
        { status: 500 }
      );
    }

    if (!userData) {
      console.error('User not found in database');
      return NextResponse.json(
        { error: 'User not found in database' },
        { status: 404 }
      );
    }

    // Get or create the user's cart
    let cartId;

    // Log the user data for debugging
    console.log('User data from database:', JSON.stringify(userData));

    // The user_id field in user_carts should reference auth.users(id)
    // This is the auth.uid() value, which is user.id from getCurrentUser
    const userId = user.id;
    console.log('Using user ID for cart lookup:', userId);

    const { data: existingCarts, error: cartError } = await supabase
      .from('user_carts')
      .select('id, preparation_times, delivery_times, delivery_fees')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(1);

    const existingCart = existingCarts && existingCarts.length > 0 ? existingCarts[0] : null;

    if (cartError && cartError.code !== 'PGRST116') { // PGRST116 is "Results contain 0 rows"
      console.error('Error fetching user cart:', cartError);
      // Continue with creating a new cart instead of returning an error
      console.log('Will attempt to create a new cart instead');
    }

    if (existingCart) {
      cartId = existingCart.id;
      console.log('Found existing cart with ID:', cartId);
    } else {
      console.log('No existing cart found, creating new cart for user:', userId);

      // Create a new cart for the user
      const { data: newCart, error: createCartError } = await supabase
        .from('user_carts')
        .insert([{
          user_id: userId,
          meta: body.businessNames ? { businessNames: body.businessNames } : {},
          preparation_times: body.preparationTimes || {},
          delivery_times: body.deliveryTimes || {},
          delivery_fees: body.deliveryFees || {},
          delivery_type: body.deliveryMethods ? JSON.stringify(body.deliveryMethods) : null,
          updated_at: new Date().toISOString()
        }])
        .select('id')
        .single();

      if (createCartError) {
        console.error('Error creating user cart:', createCartError);

        // Try a different approach if the first one fails
        console.log('Trying alternative approach to create cart...');

        const { data: retryCart, error: retryError } = await supabase
          .from('user_carts')
          .insert([{
            user_id: userId,
            updated_at: new Date().toISOString()
          }])
          .select('id')
          .single();

        if (retryError) {
          console.error('Retry also failed:', retryError);
          return NextResponse.json(
            { error: 'Failed to create user cart', details: retryError.message },
            { status: 500 }
          );
        }

        cartId = retryCart.id;
        console.log('Successfully created cart on retry with ID:', cartId);
      } else {
        cartId = newCart.id;
        console.log('Successfully created new cart with ID:', cartId);
      }
    }

    // First, clear existing cart if requested
    if (body.clearExisting) {
      const { error: clearError } = await supabase
        .from('cart_items')
        .delete()
        .eq('cart_id', cartId);

      if (clearError) {
        console.error('Error clearing cart:', clearError);
        return NextResponse.json(
          { error: 'Failed to clear cart', details: clearError.message },
          { status: 500 }
        );
      }
    }

    // Update preparation, delivery times, delivery fees, delivery methods, delivery type, and scheduled times if provided
    if (body.preparationTimes || body.deliveryTimes || body.deliveryFees || body.deliveryMethods || body.deliveryType || body.scheduledTimes) {
      const updateData: any = {};

      if (body.preparationTimes) {
        updateData.preparation_times = body.preparationTimes;
      }

      if (body.deliveryTimes) {
        updateData.delivery_times = body.deliveryTimes;
      }

      if (body.deliveryFees) {
        updateData.delivery_fees = body.deliveryFees;
      }

      if (body.deliveryMethods) {
        updateData.delivery_method = body.deliveryMethods; // pickup/delivery choice
      }

      if (body.deliveryType) {
        updateData.delivery_type = body.deliveryType; // asap/scheduled timing
      }

      if (body.scheduledTimes) {
        updateData.scheduled_times = body.scheduledTimes;
      }

      if (Object.keys(updateData).length > 0) {
        const { error: updateTimesError } = await supabase
          .from('user_carts')
          .update(updateData)
          .eq('id', cartId);

        if (updateTimesError) {
          console.error('Error updating cart metadata:', updateTimesError);
          // Continue with the rest of the operation, don't return an error
        }
      }
    }

    // Add or update each item
    for (const item of items) {
      // Check if the item already exists in the cart
      const { data: existingItems, error: existingItemError } = await supabase
        .from('cart_items')
        .select('id')
        .eq('cart_id', cartId)
        .eq('product_id', parseInt(item.id, 10) || 0)
        .is('variant_id', item.variantId || null);

      if (existingItemError) {
        console.error('Error checking for existing item:', existingItemError);
        results.push({
          item: item.id,
          success: false,
          error: `Failed to check for existing item: ${existingItemError.message}`
        });
        continue; // Skip to the next item
      }

      let itemResult;

      // If the item exists, update it
      if (existingItems && existingItems.length > 0) {
        const existingItemId = existingItems[0].id;

        const { data: updateResult, error: updateError } = await supabase
          .from('cart_items')
          .update({
            quantity: item.quantity,
            price: item.price,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingItemId)
          .select('id')
          .single();

        if (updateError) {
          console.error('Error updating cart item:', updateError);
          results.push({
            item: item.id,
            success: false,
            error: `Failed to update item: ${updateError.message}`
          });
          continue; // Skip to the next item
        }

        itemResult = {
          id: updateResult.id,
          action: 'updated'
        };
      } else {
        // If the item doesn't exist, insert it
        // Get the business ID and slug directly from the item
        // The business ID should always be a number from the products.business_id
        let businessId = item.businessId;
        const businessSlug = item.businessSlug || '';

        console.log(`CART-DIRECT-API: Processing item with business ID: ${businessId} (type: ${typeof businessId})`);

        // Try to convert to a number if it's a string that looks like a number
        if (typeof businessId === 'string' && !isNaN(Number(businessId))) {
          console.log(`CART-DIRECT-API: Converting business ID from string to number: ${businessId} -> ${Number(businessId)}`);
          businessId = Number(businessId);
        }

        // Validate that we have a numeric business ID
        if (typeof businessId !== 'number' || isNaN(businessId)) {
          console.error(`CART-DIRECT-API: Invalid business ID for item ${item.name || item.id}: ${businessId} (type: ${typeof businessId})`);
          console.error('Business ID must be a number. Skipping item.');
          continue; // Skip this item
        }

        console.log(`CART-DIRECT-API: Using business ID: ${businessId} (type: ${typeof businessId}) for item: ${item.name || item.id}`);

        const { data: insertResult, error: insertError } = await supabase
          .from('cart_items')
          .insert([{
            cart_id: cartId,
            product_id: parseInt(item.id, 10) || 0,
            variant_id: item.variantId || null,
            quantity: item.quantity,
            business_id: businessId,
            business_slug: businessSlug,
            business_type: item.businessType,
            name: item.name,
            price: item.price,
            image_url: item.imageUrl || null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }])
          .select('id')
          .single();

        if (insertError) {
          console.error('Error inserting cart item:', insertError);
          results.push({
            item: item.id,
            success: false,
            error: `Failed to insert item: ${insertError.message}`
          });
          continue; // Skip to the next item
        }

        itemResult = {
          id: insertResult.id,
          action: 'inserted'
        };
      }

      // Add the result to the results array
      results.push({
        item: item.id,
        success: true,
        result: itemResult
      });
    }

    // Get the updated cart items
    const { data: updatedCartItems, error: updatedItemsError } = await supabase
      .from('cart_items')
      .select('*')
      .eq('cart_id', cartId);

    if (updatedItemsError) {
      console.error('Error fetching updated cart items:', updatedItemsError);
      return NextResponse.json({
        success: results.every(r => r.success),
        results,
        error: 'Failed to fetch updated cart items'
      });
    }

    // Format the cart items for the response
    const formattedItems = updatedCartItems.map(item => ({
      id: item.product_id.toString(),
      productId: item.product_id,
      variantId: item.variant_id,
      quantity: item.quantity,
      businessId: item.business_id, // Now a numeric ID
      businessSlug: item.business_slug || '', // Include the slug
      businessType: item.business_type,
      name: item.name,
      price: item.price,
      imageUrl: item.image_url
    }));

    // Get business names
    const businessIds = [...new Set(updatedCartItems.map(item => item.business_id))];
    const updatedBusinessNames = {};

    for (const businessId of businessIds) {
      // Look up the business by ID (not slug)
      const { data: business, error: businessError } = await supabase
        .from('businesses')
        .select('name, slug')
        .eq('id', businessId)
        .single();

      if (!businessError && business) {
        // Store the business name keyed by the numeric ID
        updatedBusinessNames[businessId] = business.name;
        console.log(`Found business name ${business.name} for ID ${businessId}`);
      } else {
        // If we can't find the business, use a generic name
        const formattedName = `Business ${businessId}`;
        updatedBusinessNames[businessId] = formattedName;
        console.log(`Could not find business with ID ${businessId}, using generic name ${formattedName}`);
      }
    }

    // Create the updated cart data
    const updatedCart = {
      cart: {
        items: formattedItems,
        meta: {
          lastUpdated: new Date().toISOString(),
          itemCount: formattedItems.length
        }
      },
      businessNames: updatedBusinessNames,
      preparationTimes: body.preparationTimes || {},
      deliveryTimes: body.deliveryTimes || {},
      deliveryFees: body.deliveryFees || {},
      deliveryMethods: body.deliveryMethods || {},
      deliveryType: body.deliveryType || {}
    };

    return NextResponse.json({
      success: results.every(r => r.success),
      results,
      cart: updatedCart.cart,
      businessNames: updatedCart.businessNames,
      preparationTimes: updatedCart.preparationTimes,
      deliveryTimes: updatedCart.deliveryTimes,
      deliveryFees: updatedCart.deliveryFees,
      deliveryMethods: updatedCart.deliveryMethods,
      deliveryType: updatedCart.deliveryType
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}

// DELETE endpoint to clear a user's cart
export async function DELETE(request: NextRequest) {
  try {
    // Get the current user
    let user = await getCurrentUser(request);

    if (!user) {
      // In development mode, use a mock user
      if (process.env.NODE_ENV === 'development') {
        console.log("CART-API: Development mode - using mock user for DELETE");
        user = {
          id: 'e96a586b-3aa2-44b0-b6d9-af3c24e03683',
          email: '<EMAIL>',
          role: 'customer'
        };
      } else {
        return NextResponse.json(
          { error: 'User not authenticated' },
          { status: 401 }
        );
      }
    }

    // Get the user's ID from the users table using auth_id
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id, auth_id')
      .eq('auth_id', user.id)
      .single();

    if (userError) {
      console.error('Error fetching user ID:', userError);
      return NextResponse.json(
        { error: 'Failed to fetch user ID', details: userError.message },
        { status: 500 }
      );
    }

    if (!userData) {
      console.error('User not found in database');
      return NextResponse.json(
        { error: 'User not found in database' },
        { status: 404 }
      );
    }

    // Get the user's cart ID
    // The user_id field in user_carts should reference auth.users(id)
    const userId = user.id;
    console.log('Using user ID for cart lookup in DELETE:', userId);

    const { data: userCarts, error: cartError } = await supabase
      .from('user_carts')
      .select('id')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(1);

    if (cartError) {
      console.error('Error fetching user cart:', cartError);
      return NextResponse.json(
        { error: 'Failed to fetch user cart', details: cartError.message },
        { status: 500 }
      );
    }

    // If no cart exists, return success (nothing to clear)
    if (!userCarts || userCarts.length === 0) {
      return NextResponse.json({
        success: true,
        cleared: 0
      });
    }

    const userCart = userCarts[0];

    // Clear the cart items
    const { data, error } = await supabase
      .from('cart_items')
      .delete()
      .eq('cart_id', userCart.id)
      .select('id');

    if (error) {
      console.error('Error clearing cart items:', error);
      return NextResponse.json(
        { error: 'Failed to clear cart items', details: error.message },
        { status: 500 }
      );
    }

    // Also clear the cart context values in user_carts
    const { error: updateError } = await supabase
      .from('user_carts')
      .update({
        delivery_method: {},
        delivery_type: {},
        delivery_fees: {},
        preparation_times: {},
        delivery_times: {},
        scheduled_times: {},
        updated_at: new Date().toISOString()
      })
      .eq('id', userCart.id);

    if (updateError) {
      console.error('Error clearing cart context values:', updateError);
      // Don't return error, just log it since cart items were cleared successfully
    }

    return NextResponse.json({
      success: true,
      cleared: data ? data.length : 0
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}
