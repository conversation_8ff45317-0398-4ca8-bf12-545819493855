"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { useAuthDirect } from "@/context/auth-context-direct"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import { Download, RefreshCcw, Bell } from "lucide-react"

// Import our custom components
import { DateRange } from "@/components/date-range-picker"
import { BusinessSelector } from "@/components/orders/business-selector"
import { Pagination } from "@/components/orders/pagination"
import { EnhancedOrdersTable, Order } from "@/components/orders/enhanced-orders-table"
import { EnhancedOrderStats } from "@/components/orders/enhanced-order-stats"
import { EnhancedOrderFilters, OrderFilters } from "@/components/orders/enhanced-order-filters"

// Import our custom styles
import "@/styles/business-admin.css"

// Define types for our data
interface OrdersResponse {
  orders: Order[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

interface BusinessData {
  id: number
  name: string
  business_type_id: number
  business_type?: string
  logo_url?: string | null
}

interface BusinessOption {
  id: number
  name: string
  business_type?: string
}

export default function BusinessAdminOrdersEnhanced() {
  const router = useRouter()
  const { user, userProfile, isAdmin, isSuperAdmin } = useAuthDirect()
  const { toast } = useToast()

  // Business state
  const [business, setBusiness] = useState<BusinessData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isPendingApproval, setIsPendingApproval] = useState(false)
  const [availableBusinesses, setAvailableBusinesses] = useState<BusinessOption[]>([])
  const [selectedBusinessId, setSelectedBusinessId] = useState<number | null>(null)
  const [isAdminUser, setIsAdminUser] = useState(false)

  // Orders state
  const [orders, setOrders] = useState<Order[]>([])
  const [totalOrders, setTotalOrders] = useState(0)
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [totalPages, setTotalPages] = useState(1)
  const [activeTab, setActiveTab] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(new Date().getFullYear(), new Date().getMonth(), 1), // First day of current month
    to: new Date(),
  })

  // Notification state
  const [newOrderCount, setNewOrderCount] = useState(0)
  const [realtimeEnabled, setRealtimeEnabled] = useState(true)

  // Enhanced filters state
  const [activeFilters, setActiveFilters] = useState<OrderFilters>({
    status: [],
    priority: [],
    deliveryType: [],
    paymentStatus: 'all',
    overdueOnly: false,
    notifiedStatus: 'all'
  })

  // Order stats
  const [orderStats, setOrderStats] = useState({
    total: 0,
    pending: 0,
    confirmed: 0,
    preparing: 0,
    ready: 0,
    outForDelivery: 0,
    completed: 0,
    cancelled: 0,
    averageDeliveryTime: "0 min",
    overdueOrders: 0,
    highPriorityOrders: 0
  })

  // Check if user is admin or super admin
  useEffect(() => {
    if (isAdmin || isSuperAdmin) {
      setIsAdminUser(true)
    }
  }, [isAdmin, isSuperAdmin])

  // Initial data loading
  useEffect(() => {
    if (user) {
      // Try to load the selected business ID from localStorage
      if (isAdminUser) {
        try {
          const savedBusinessId = localStorage.getItem('loop_jersey_selected_business_id')
          if (savedBusinessId) {
            console.log("Loaded selected business ID from localStorage:", savedBusinessId)
            const businessId = parseInt(savedBusinessId)
            setSelectedBusinessId(businessId)
          }
        } catch (e) {
          console.error("Error loading selected business ID from localStorage:", e)
        }

        // Fetch available businesses
        fetchAvailableBusinesses()
      }

      // Fetch business data
      fetchBusinessData()
    }
  }, [user, isAdminUser])

  // Fetch orders when dependencies change
  useEffect(() => {
    if (user) {
      // Reset new order count when any of these dependencies change
      setNewOrderCount(0)

      fetchOrders()
    }
  }, [user, page, pageSize, selectedBusinessId, activeFilters, dateRange])

  // Initial fetch when component mounts
  useEffect(() => {
    if (user) {
      fetchOrders()
    }
  }, [])

  // Define fetchBusinessData outside of useEffect so it can be called from multiple places
  const fetchBusinessData = async () => {
    try {
      console.log("Fetching business data...")
      setIsLoading(true)

      // Get the authentication token from localStorage
      const token = localStorage.getItem('loop_jersey_auth_token') || '';

      // Build the URL based on whether we're an admin user with a selected business
      let url = '/api/business-admin/business-data'

      // If admin user and a business is selected, add the business ID as a query parameter
      if (isAdminUser && selectedBusinessId) {
        url = `/api/business-admin/business-data?businessId=${selectedBusinessId}`
        console.log(`Admin user fetching data for business ID: ${selectedBusinessId}`)
      }

      const response = await fetch(url, {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      })

      if (!response.ok) {
        // If we get a 401 or 403, redirect to login
        if (response.status === 401 || response.status === 403) {
          console.log("Authentication error, redirecting to login")
          router.push("/login?redirectTo=/business-admin/orders-enhanced")
          return
        }

        throw new Error(`API error: ${response.status}`)
      }

      const data = await response.json()
      console.log("Business data received:", data)

      if (data.business) {
        setBusiness({
          id: data.business.id,
          name: data.business.name,
          business_type_id: data.business.business_type_id,
          business_type: data.business.business_type
        })

        // Check if the business is pending approval
        setIsPendingApproval(data.business.is_approved === false)
      } else {
        setError("No business data found")
      }
    } catch (err) {
      console.error("Error fetching business data:", err)
      setError("An unexpected error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  // For admin users, fetch available businesses
  const fetchAvailableBusinesses = async () => {
    if (!isAdminUser) return

    try {
      console.log("Admin user detected, fetching available businesses")

      // Get the authentication token from localStorage
      const token = localStorage.getItem('loop_jersey_auth_token') || '';

      const response = await fetch('/api/admin/businesses-direct', {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      })

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`)
      }

      const data = await response.json()
      console.log("Available businesses:", data)

      if (data && Array.isArray(data)) {
        setAvailableBusinesses(data.map((b: any) => ({
          id: b.id,
          name: b.name,
          business_type: b.business_type || b.business_types?.name || "Business"
        })))
      }
    } catch (err) {
      console.error("Error fetching available businesses:", err)
    }
  }

  // Handle business selection change for admin users
  const handleBusinessChange = async (businessId: number) => {
    console.log("Selected business changed to:", businessId)

    // Reset new order count when changing business
    setNewOrderCount(0)

    // Update the selected business ID
    setSelectedBusinessId(businessId)

    // Store the selected business ID in localStorage for persistence
    try {
      localStorage.setItem('loop_jersey_selected_business_id', businessId.toString())
    } catch (e) {
      console.error("Error storing selected business ID:", e)
    }

    // Refetch data with the new business ID
    fetchBusinessData()

    // Force a manual fetch of orders to ensure immediate update
    fetchOrders()
  }

  // Fetch orders
  const fetchOrders = async () => {
    try {
      setIsLoading(true)
      // Reset new order count when manually fetching
      setNewOrderCount(0)

      // Check if user is authenticated
      if (!user) {
        console.log("User not authenticated, skipping fetch")
        setIsLoading(false)
        return
      }

      // Build the query parameters
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
        sortBy: 'created_at',
        sortOrder: 'desc'
      })

      // Add search query if provided
      if (searchQuery) {
        params.append("search", searchQuery)
      }

      // Add date range parameters
      if (dateRange?.from) {
        const startDateStr = dateRange.from.toISOString()
        params.append("startDate", startDateStr)
      }

      if (dateRange?.to) {
        // Set the end of day for the to date to include the entire day
        const endDate = new Date(dateRange.to)
        endDate.setHours(23, 59, 59, 999)
        const endDateStr = endDate.toISOString()
        params.append("endDate", endDateStr)
      }

      // Add status filters if provided
      if (activeFilters.status.length > 0) {
        params.append("statuses", activeFilters.status.join(','))
      }

      // Add priority filters if provided
      if (activeFilters.priority.length > 0) {
        params.append("priorities", activeFilters.priority.join(','))
      }

      // Add delivery type filters if provided
      if (activeFilters.deliveryType.length > 0) {
        params.append("deliveryTypes", activeFilters.deliveryType.join(','))
      }

      // Add payment status filter if not 'all'
      if (activeFilters.paymentStatus !== 'all') {
        params.append("paymentStatus", activeFilters.paymentStatus)
      }

      // Add overdue filter if enabled
      if (activeFilters.overdueOnly) {
        params.append("overdueOnly", "true")
      }

      // Add notification status filter if not 'all'
      if (activeFilters.notifiedStatus !== 'all') {
        params.append("notificationStatus", activeFilters.notifiedStatus)
      }

      // If admin user, add the business ID
      if (isAdminUser && selectedBusinessId) {
        params.append("businessId", selectedBusinessId.toString())
      }

      // Get the authentication token from localStorage
      const token = localStorage.getItem('loop_jersey_auth_token') || '';

      // Log the full URL for debugging
      const requestUrl = `/api/business-admin/orders?${params.toString()}`
      console.log("Fetching orders with URL:", requestUrl)

      // Fetch orders from the API
      const response = await fetch(requestUrl, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          'Authorization': token ? `Bearer ${token}` : '',
        },
        credentials: "include"
      })

      if (!response.ok) {
        // If we get a 401 or 403, redirect to login
        if (response.status === 401 || response.status === 403) {
          console.log("Authentication error, redirecting to login")
          router.push("/login?redirectTo=/business-admin/orders-enhanced")
          return
        }

        throw new Error(`API error: ${response.status}`)
      }

      const data: OrdersResponse = await response.json()
      console.log("Orders data:", data)

      setOrders(data.orders || [])
      setTotalOrders(data.total || 0)
      setTotalPages(data.totalPages || 1)

      // Calculate order stats
      const pendingCount = data.orders.filter(order => order.status === "pending").length
      const confirmedCount = data.orders.filter(order => order.status === "confirmed").length
      const preparingCount = data.orders.filter(order => order.status === "preparing").length
      const readyCount = data.orders.filter(order => order.status === "ready").length
      const outForDeliveryCount = data.orders.filter(order => order.status === "out_for_delivery").length
      const completedCount = data.orders.filter(order => order.status === "completed" || order.status === "delivered").length
      const cancelledCount = data.orders.filter(order => order.status === "cancelled").length

      // Calculate overdue and high priority orders
      const overdueOrders = data.orders.filter(order => {
        if (!order.scheduled_for) return false
        const scheduledTime = new Date(order.scheduled_for)
        return scheduledTime < new Date() &&
               !["completed", "delivered", "cancelled"].includes(order.status)
      }).length

      const highPriorityOrders = data.orders.filter(order =>
        order.priority_level === 1 || order.priority_level === 2
      ).length

      setOrderStats({
        total: data.total || 0,
        pending: pendingCount,
        confirmed: confirmedCount,
        preparing: preparingCount,
        ready: readyCount,
        outForDelivery: outForDeliveryCount,
        completed: completedCount,
        cancelled: cancelledCount,
        averageDeliveryTime: "24 min", // This would ideally come from the API
        overdueOrders,
        highPriorityOrders
      })
    } catch (err) {
      console.error("Error fetching orders:", err)
      setError("Failed to load orders")
    } finally {
      setIsLoading(false)
    }
  }

  // Handle tab change
  const handleTabChange = (value: string) => {
    console.log("Tab changed to:", value)
    setActiveTab(value)

    // Update status filter based on tab
    let newStatusFilter: string[] = []

    switch (value) {
      case "pending":
        newStatusFilter = ["pending", "confirmed"]
        break
      case "processing":
        newStatusFilter = ["preparing", "ready", "out_for_delivery"]
        break
      case "completed":
        newStatusFilter = ["completed", "delivered"]
        break
      case "cancelled":
        newStatusFilter = ["cancelled"]
        break
      default: // "all"
        newStatusFilter = []
    }

    setActiveFilters({
      ...activeFilters,
      status: newStatusFilter
    })
  }

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query)
    setPage(1) // Reset to first page when searching
  }

  // Handle filter change
  const handleFilterChange = (filters: OrderFilters) => {
    setActiveFilters(filters)
    setPage(1) // Reset to first page when changing filters
  }

  // Handle date range change
  const handleDateRangeChange = (range: DateRange | undefined) => {
    setDateRange(range)
    setPage(1) // Reset to first page when changing date range
  }

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    setPage(newPage)
  }

  // Handle view order
  const handleViewOrder = (order: Order) => {
    router.push(`/business-admin/orders/${order.id}`)
  }

  // Handle update order status
  const handleUpdateStatus = async (order: Order, newStatus: string) => {
    try {
      // Get the authentication token from localStorage
      const token = localStorage.getItem('loop_jersey_auth_token') || '';

      const response = await fetch("/api/business-admin/orders", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          'Authorization': token ? `Bearer ${token}` : '',
        },
        body: JSON.stringify({
          orderId: order.id,
          status: newStatus,
          notes: `Status updated to ${newStatus} by business admin`
        }),
        credentials: "include"
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to update order status")
      }

      // Format the status for display
      const formatStatusForMessage = (status: string) => {
        switch (status.toLowerCase()) {
          case 'confirmed':
            return 'confirmed'
          case 'cancelled':
            return 'cancelled'
          case 'preparing':
            return 'preparing'
          case 'ready':
            return 'ready for pickup/delivery'
          case 'out_for_delivery':
            return 'out for delivery'
          case 'delivered':
            return 'delivered'
          default:
            return status
        }
      }

      toast({
        title: "Order Status Updated",
        description: `Order #${order.order_number || order.id} has been ${formatStatusForMessage(newStatus)}`,
        duration: 3000
      })

      // Refresh orders after a short delay to allow the server to process the update
      setTimeout(() => {
        fetchOrders()
      }, 1000)
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Error updating status",
        description: error.message || "An unexpected error occurred",
      })
    }
  }

  // Handle print order
  const handlePrintOrder = (order: Order) => {
    // Open print view in a new window
    const printWindow = window.open(`/business-admin/orders/${order.id}/print`, '_blank')
    if (printWindow) {
      printWindow.focus()
    } else {
      toast({
        variant: "destructive",
        title: "Popup Blocked",
        description: "Please allow popups to print orders",
      })
    }
  }

  // Loading state
  if (isLoading && orders.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading orders...</p>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-red-500 text-5xl mb-4">⚠️</div>
          <h2 className="text-2xl font-bold mb-2">Error Loading Orders</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button onClick={() => fetchOrders()}>Retry</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 business-admin-container bg-white">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between bg-white p-4 rounded-lg shadow-sm">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Orders</h1>
          <p className="text-muted-foreground">
            Manage and track your customer orders
          </p>
        </div>
        <div className="flex flex-col md:flex-row items-end md:items-center gap-2 mt-4 md:mt-0">
          {/* Business Selector for Admin Users */}
          {isAdminUser && availableBusinesses.length > 0 && (
            <BusinessSelector
              businesses={availableBusinesses}
              selectedBusinessId={selectedBusinessId}
              onBusinessChange={handleBusinessChange}
            />
          )}
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" className="h-8 admin-button">
              <Download className="mr-2 h-3.5 w-3.5" />
              Export
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="h-8 relative hover:bg-slate-100 transition-colors admin-button"
              onClick={() => fetchOrders()}
            >
              <RefreshCcw className="h-3.5 w-3.5 mr-2 group-hover:rotate-180 transition-transform duration-300" />
              Refresh
              {newOrderCount > 0 && (
                <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse shadow-sm">
                  {newOrderCount}
                </span>
              )}
            </Button>
            <Button
              variant={realtimeEnabled ? "default" : "outline"}
              size="sm"
              className={`h-8 relative admin-button ${realtimeEnabled ? "admin-button-primary bg-emerald-600 hover:bg-emerald-700" : ""}`}
              onClick={() => {
                setRealtimeEnabled(!realtimeEnabled);
                // Reset new order count when toggling live mode
                if (!realtimeEnabled) {
                  setNewOrderCount(0);
                }
              }}
            >
              {realtimeEnabled && newOrderCount === 0 ? (
                <>
                  <div className="absolute left-2 top-1/2 transform -translate-y-1/2 flex items-center">
                    <span className="h-1.5 w-1.5 rounded-full bg-white mr-1 animate-pulse"></span>
                  </div>
                  <Bell className="h-3.5 w-3.5 mr-2 ml-1" />
                </>
              ) : realtimeEnabled ? (
                <>
                  <div className="absolute left-2 top-1/2 transform -translate-y-1/2 flex items-center">
                    <span className="h-1.5 w-1.5 rounded-full bg-white mr-1 animate-pulse"></span>
                  </div>
                  <Bell className="h-3.5 w-3.5 mr-2 ml-1" />
                </>
              ) : (
                <Bell className="h-3.5 w-3.5 mr-2 text-muted-foreground" />
              )}
              {realtimeEnabled ? "Live" : "Paused"}
            </Button>
          </div>
        </div>
      </div>

      {/* Order Stats */}
      <EnhancedOrderStats stats={orderStats} />

      {/* Order Filters */}
      <EnhancedOrderFilters
        onSearch={handleSearch}
        onFilterChange={handleFilterChange}
        onDateRangeChange={handleDateRangeChange}
        dateRange={dateRange}
        activeFilters={activeFilters}
      />

      {/* Tabs */}
      <Tabs defaultValue="all" className="space-y-4" onValueChange={handleTabChange} value={activeTab}>
        <div className="flex items-center justify-between">
          <TabsList className="bg-white p-1 rounded-lg admin-tabs shadow-sm">
            <TabsTrigger
              value="all"
              className={`admin-tab ${activeTab === "all"
                ? "bg-background text-foreground font-medium shadow-sm border-b-2 border-emerald-500"
                : "text-muted-foreground hover:text-foreground hover:bg-background/50 transition-colors"}`}
            >
              All Orders
            </TabsTrigger>
            <TabsTrigger
              value="pending"
              className={activeTab === "pending"
                ? "bg-background text-foreground font-medium shadow-sm border-b-2 border-yellow-500"
                : "text-muted-foreground hover:text-foreground hover:bg-background/50 transition-colors"}
            >
              <div className="flex items-center">
                <span className={`mr-1.5 h-2 w-2 rounded-full ${activeTab === "pending" ? "bg-yellow-500" : "bg-yellow-400/50"}`}></span>
                Pending
              </div>
            </TabsTrigger>
            <TabsTrigger
              value="processing"
              className={activeTab === "processing"
                ? "bg-background text-foreground font-medium shadow-sm border-b-2 border-blue-500"
                : "text-muted-foreground hover:text-foreground hover:bg-background/50 transition-colors"}
            >
              <div className="flex items-center">
                <span className={`mr-1.5 h-2 w-2 rounded-full ${activeTab === "processing" ? "bg-blue-500" : "bg-blue-400/50"}`}></span>
                Processing
              </div>
            </TabsTrigger>
            <TabsTrigger
              value="completed"
              className={activeTab === "completed"
                ? "bg-background text-foreground font-medium shadow-sm border-b-2 border-emerald-500"
                : "text-muted-foreground hover:text-foreground hover:bg-background/50 transition-colors"}
            >
              <div className="flex items-center">
                <span className={`mr-1.5 h-2 w-2 rounded-full ${activeTab === "completed" ? "bg-emerald-500" : "bg-emerald-400/50"}`}></span>
                Completed
              </div>
            </TabsTrigger>
            <TabsTrigger
              value="cancelled"
              className={activeTab === "cancelled"
                ? "bg-background text-foreground font-medium shadow-sm border-b-2 border-red-500"
                : "text-muted-foreground hover:text-foreground hover:bg-background/50 transition-colors"}
            >
              <div className="flex items-center">
                <span className={`mr-1.5 h-2 w-2 rounded-full ${activeTab === "cancelled" ? "bg-red-500" : "bg-red-400/50"}`}></span>
                Cancelled
              </div>
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="all" className="space-y-4">
          <EnhancedOrdersTable
            orders={orders}
            onViewOrder={handleViewOrder}
            onUpdateStatus={handleUpdateStatus}
            onPrintOrder={handlePrintOrder}
            onRefreshOrders={fetchOrders}
            isLoading={isLoading}
          />

          {totalPages > 1 && (
            <Pagination
              currentPage={page}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          )}
        </TabsContent>

        <TabsContent value="pending" className="space-y-4">
          <EnhancedOrdersTable
            orders={orders}
            onViewOrder={handleViewOrder}
            onUpdateStatus={handleUpdateStatus}
            onPrintOrder={handlePrintOrder}
            onRefreshOrders={fetchOrders}
            isLoading={isLoading}
          />

          {totalPages > 1 && (
            <Pagination
              currentPage={page}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          )}
        </TabsContent>

        <TabsContent value="processing" className="space-y-4">
          <EnhancedOrdersTable
            orders={orders}
            onViewOrder={handleViewOrder}
            onUpdateStatus={handleUpdateStatus}
            onPrintOrder={handlePrintOrder}
            onRefreshOrders={fetchOrders}
            isLoading={isLoading}
          />

          {totalPages > 1 && (
            <Pagination
              currentPage={page}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          )}
        </TabsContent>

        <TabsContent value="completed" className="space-y-4">
          <EnhancedOrdersTable
            orders={orders}
            onViewOrder={handleViewOrder}
            onUpdateStatus={handleUpdateStatus}
            onPrintOrder={handlePrintOrder}
            onRefreshOrders={fetchOrders}
            isLoading={isLoading}
          />

          {totalPages > 1 && (
            <Pagination
              currentPage={page}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          )}
        </TabsContent>

        <TabsContent value="cancelled" className="space-y-4">
          <EnhancedOrdersTable
            orders={orders}
            onViewOrder={handleViewOrder}
            onUpdateStatus={handleUpdateStatus}
            onPrintOrder={handlePrintOrder}
            onRefreshOrders={fetchOrders}
            isLoading={isLoading}
          />

          {totalPages > 1 && (
            <Pagination
              currentPage={page}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
