import { cookies } from "next/headers";
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs";
import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

// Create a Supabase client with admin privileges
const adminClient = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || "",
  process.env.SUPABASE_SERVICE_ROLE_KEY || ""
);

// Helper function to create a server-side Supabase client
async function createServerSupabase() {
  const cookieStore = cookies();
  return createServerComponentClient({ cookies: () => cookieStore });
}

// GET handler to fetch user's default address
export async function GET(request: Request) {
  try {
    console.log("GET /api/user/addresses/default: Starting request");

    // Get the user's session using the server client
    const cookieStore = cookies();
    const supabase = createServerComponentClient({ cookies: () => cookieStore });

    console.log("GET /api/user/addresses/default: Getting session");
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError) {
      console.error("GET /api/user/addresses/default: Session error:", sessionError);
      return NextResponse.json(
        { error: `Session error: ${sessionError.message}` },
        { status: 500 }
      );
    }

    if (!session?.user) {
      console.log("GET /api/user/addresses/default: No authenticated session found");
      return NextResponse.json(
        { error: "Not authenticated" },
        { status: 401 }
      );
    }

    const email = session.user.email;
    const authUserId = session.user.id;

    if (!email) {
      console.log("GET /api/user/addresses/default: User email not found in session");
      return NextResponse.json(
        { error: "User email not found" },
        { status: 400 }
      );
    }

    console.log("GET /api/user/addresses/default: Authenticated user:", email);

    // Get user ID from the users table using admin client
    try {
      console.log("GET /api/user/addresses/default: Fetching user ID for email:", email);

      const { data: userData, error: userError } = await adminClient
        .from("users")
        .select("id")
        .eq("email", email)
        .single();

      // If user doesn't exist yet, try to find addresses by auth user ID or email
      if (userError && userError.code === 'PGRST116') {
        console.log("GET /api/user/addresses/default: User not found in database, trying to find address by auth ID or email");

        // Try to find address by auth user ID
        const { data: addressByAuthId, error: authIdError } = await adminClient
          .from("user_addresses")
          .select("*")
          .eq("auth_user_id", authUserId)
          .eq("is_default", true)
          .single();

        if (!authIdError && addressByAuthId) {
          console.log("GET /api/user/addresses/default: Found default address by auth ID");
          return NextResponse.json({ address: addressByAuthId });
        }

        // Try to find address by email
        const { data: addressByEmail, error: emailError } = await adminClient
          .from("user_addresses")
          .select("*")
          .eq("email", email)
          .eq("is_default", true)
          .single();

        if (!emailError && addressByEmail) {
          console.log("GET /api/user/addresses/default: Found default address by email");
          return NextResponse.json({ address: addressByEmail });
        }

        // No address found
        console.log("GET /api/user/addresses/default: No default address found");
        return NextResponse.json({ address: null });
      }

      if (userError) {
        console.error("GET /api/user/addresses/default: Error fetching user ID:", userError);
        return NextResponse.json(
          { error: "Error fetching user data: " + userError.message },
          { status: 500 }
        );
      }

      if (!userData) {
        console.log("GET /api/user/addresses/default: User not found in database");
        return NextResponse.json({ address: null });
      }

      console.log("GET /api/user/addresses/default: Found user ID:", userData.id);

      // Fetch user's default address using admin client
      console.log("GET /api/user/addresses/default: Fetching default address for user ID:", userData.id);

      const { data: address, error: addressError } = await adminClient
        .from("user_addresses")
        .select("*")
        .eq("user_id", userData.id)
        .eq("is_default", true)
        .single();

      if (addressError && addressError.code !== 'PGRST116') {
        console.error("GET /api/user/addresses/default: Error fetching default address:", addressError);
        return NextResponse.json(
          { error: "Error fetching address: " + addressError.message },
          { status: 500 }
        );
      }

      // If no default address found, return null
      if (addressError && addressError.code === 'PGRST116') {
        console.log("GET /api/user/addresses/default: No default address found");
        return NextResponse.json({ address: null });
      }

      console.log("GET /api/user/addresses/default: Found default address");
      return NextResponse.json({ address });
    } catch (error) {
      console.error("GET /api/user/addresses/default: Unexpected error:", error);
      return NextResponse.json(
        { error: "An unexpected error occurred" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("GET /api/user/addresses/default: Unexpected error:", error);
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
