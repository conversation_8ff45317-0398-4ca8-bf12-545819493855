'use client'

import { useEffect, useState, useCallback } from 'react'
import { createClient } from '@supabase/supabase-js'
import { RealtimeChannel } from '@supabase/supabase-js'
import { addAuthHeaders } from '@/utils/auth-token'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

export interface RealtimeMessage {
  id: string
  sender_id: string
  recipient_id: string
  thread_id: string
  channel_type: string
  message_type: string
  subject?: string
  content: string
  audio_data?: string
  is_read: boolean
  is_urgent: boolean
  is_automated: boolean
  priority: number
  created_at: string
  updated_at?: string
}

export interface UseRealtimeMessagesOptions {
  userId?: string
  threadId?: string
  onNewMessage?: (message: RealtimeMessage) => void
  onMessageUpdate?: (message: RealtimeMessage) => void
  onMessageDelete?: (messageId: string) => void
}

export function useRealtimeMessages({
  userId,
  threadId,
  onNewMessage,
  onMessageUpdate,
  onMessageDelete
}: UseRealtimeMessagesOptions = {}) {
  const [isConnected, setIsConnected] = useState(false)
  const [channel, setChannel] = useState<RealtimeChannel | null>(null)
  const [error, setError] = useState<string | null>(null)

  // Handle new message insertions
  const handleInsert = useCallback((payload: any) => {
    const newMessage = payload.new as RealtimeMessage
    console.log('New message received:', newMessage)

    // Only process messages for the current user
    if (userId && (newMessage.sender_id === userId || newMessage.recipient_id === userId)) {
      onNewMessage?.(newMessage)
    }
  }, [userId, onNewMessage])

  // Handle message updates (read status, edits, etc.)
  const handleUpdate = useCallback((payload: any) => {
    const updatedMessage = payload.new as RealtimeMessage
    console.log('Message updated:', updatedMessage)

    // Only process messages for the current user
    if (userId && (updatedMessage.sender_id === userId || updatedMessage.recipient_id === userId)) {
      onMessageUpdate?.(updatedMessage)
    }
  }, [userId, onMessageUpdate])

  // Handle message deletions
  const handleDelete = useCallback((payload: any) => {
    const deletedMessage = payload.old as RealtimeMessage
    console.log('Message deleted:', deletedMessage)

    // Only process messages for the current user
    if (userId && (deletedMessage.sender_id === userId || deletedMessage.recipient_id === userId)) {
      onMessageDelete?.(deletedMessage.id)
    }
  }, [userId, onMessageDelete])

  // Set up real-time subscription
  useEffect(() => {
    if (!userId) {
      return
    }

    setError(null)

    // Create channel name based on whether we're listening to a specific thread or all messages
    const channelName = threadId ? `messages-thread-${threadId}` : `messages-user-${userId}`

    console.log(`Setting up real-time subscription for channel: ${channelName}`)

    const newChannel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'communications',
          filter: threadId
            ? `thread_id=eq.${threadId}`
            : `or(sender_id.eq.${userId},recipient_id.eq.${userId})`
        },
        handleInsert
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'communications',
          filter: threadId
            ? `thread_id=eq.${threadId}`
            : `or(sender_id.eq.${userId},recipient_id.eq.${userId})`
        },
        handleUpdate
      )
      .on(
        'postgres_changes',
        {
          event: 'DELETE',
          schema: 'public',
          table: 'communications',
          filter: threadId
            ? `thread_id=eq.${threadId}`
            : `or(sender_id.eq.${userId},recipient_id.eq.${userId})`
        },
        handleDelete
      )
      .subscribe((status) => {
        console.log('Realtime subscription status:', status)

        if (status === 'SUBSCRIBED') {
          setIsConnected(true)
          setError(null)
        } else if (status === 'CHANNEL_ERROR') {
          setIsConnected(false)
          setError('Failed to connect to real-time updates')
        } else if (status === 'TIMED_OUT') {
          setIsConnected(false)
          setError('Real-time connection timed out')
        } else if (status === 'CLOSED') {
          setIsConnected(false)
        }
      })

    setChannel(newChannel)

    // Cleanup function
    return () => {
      console.log(`Cleaning up real-time subscription for channel: ${channelName}`)
      newChannel.unsubscribe()
      setIsConnected(false)
      setChannel(null)
    }
  }, [userId, threadId, handleInsert, handleUpdate, handleDelete])

  // Function to manually reconnect
  const reconnect = useCallback(() => {
    if (channel) {
      channel.unsubscribe()
    }
    // The useEffect will automatically create a new subscription
  }, [channel])

  // Function to send a message (convenience method)
  const sendMessage = useCallback(async (messageData: {
    recipient_id: string
    content: string
    channel_type: string
    message_type?: string
    subject?: string
    thread_id?: string
    audio_data?: string
    audio_file_name?: string
    audio_file_size?: number
    audio_duration?: number
    is_urgent?: boolean
    priority?: number
  }) => {
    try {
      const response = await fetch('/api/connections-hub/messages', {
        method: 'POST',
        headers: addAuthHeaders({
          'Content-Type': 'application/json',
        }),
        body: JSON.stringify({
          ...messageData,
          message_type: messageData.message_type || 'chat',
          priority: messageData.priority || 0
        })
      })

      if (!response.ok) {
        throw new Error(`Failed to send message: ${response.status}`)
      }

      const result = await response.json()
      return result
    } catch (error) {
      console.error('Error sending message:', error)
      throw error
    }
  }, [])

  // Function to mark message as read
  const markAsRead = useCallback(async (messageId: string) => {
    try {
      const response = await fetch(`/api/connections-hub/messages/${messageId}`, {
        method: 'PUT',
        headers: addAuthHeaders({
          'Content-Type': 'application/json',
        }),
        body: JSON.stringify({
          is_read: true
        })
      })

      if (!response.ok) {
        throw new Error(`Failed to mark message as read: ${response.status}`)
      }

      const result = await response.json()
      return result
    } catch (error) {
      console.error('Error marking message as read:', error)
      throw error
    }
  }, [])

  // Function to mark all messages in thread as read
  const markThreadAsRead = useCallback(async (threadId: string) => {
    try {
      const response = await fetch(`/api/connections-hub/threads/${threadId}`, {
        method: 'PUT',
        headers: addAuthHeaders({
          'Content-Type': 'application/json',
        }),
        body: JSON.stringify({
          action: 'mark_all_read'
        })
      })

      if (!response.ok) {
        throw new Error(`Failed to mark thread as read: ${response.status}`)
      }

      const result = await response.json()
      return result
    } catch (error) {
      console.error('Error marking thread as read:', error)
      throw error
    }
  }, [])

  return {
    isConnected,
    error,
    reconnect,
    sendMessage,
    markAsRead,
    markThreadAsRead
  }
}
