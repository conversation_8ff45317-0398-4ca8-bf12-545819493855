#!/usr/bin/env node

/**
 * Script to check existing users and related data for connections hub
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkExistingData() {
  console.log('👥 Checking Existing Users and Related Data\n');

  // Check users table
  await checkUsers();
  
  // Check businesses table
  await checkBusinesses();
  
  // Check orders table
  await checkOrders();
  
  // Check if there are any existing connection profiles
  await checkExistingConnectionProfiles();
}

async function checkUsers() {
  console.log('👤 USERS TABLE');
  console.log('='.repeat(50));

  try {
    const { data: users, error } = await supabase
      .from('users')
      .select('*')
      .limit(10);

    if (error) {
      console.log(`❌ Error querying users: ${error.message}`);
      return;
    }

    console.log(`📈 Total sample users: ${users?.length || 0}`);
    
    if (users && users.length > 0) {
      console.log('\n📋 Sample users:');
      users.forEach((user, index) => {
        console.log(`  ${index + 1}. ${user.name || user.first_name + ' ' + user.last_name || 'Unnamed'} (${user.email}) - Role: ${user.role || 'N/A'}`);
      });

      // Analyze user roles
      const roleCounts = users.reduce((acc, user) => {
        const role = user.role || 'unknown';
        acc[role] = (acc[role] || 0) + 1;
        return acc;
      }, {});
      
      console.log('\n📊 User roles distribution:');
      Object.entries(roleCounts).forEach(([role, count]) => {
        console.log(`  ${role}: ${count}`);
      });
    } else {
      console.log('📭 No users found in database');
    }

  } catch (error) {
    console.log(`❌ Error examining users table: ${error.message}`);
  }
  
  console.log('\n');
}

async function checkBusinesses() {
  console.log('🏢 BUSINESSES TABLE');
  console.log('='.repeat(50));

  try {
    const { data: businesses, error } = await supabase
      .from('businesses')
      .select('id, name, business_type, is_approved, created_at')
      .limit(10);

    if (error) {
      console.log(`❌ Error querying businesses: ${error.message}`);
      return;
    }

    console.log(`📈 Total sample businesses: ${businesses?.length || 0}`);
    
    if (businesses && businesses.length > 0) {
      console.log('\n📋 Sample businesses:');
      businesses.forEach((business, index) => {
        console.log(`  ${index + 1}. ${business.name} (${business.business_type}) - Approved: ${business.is_approved}`);
      });

      // Analyze business types
      const typeCounts = businesses.reduce((acc, business) => {
        const type = business.business_type || 'unknown';
        acc[type] = (acc[type] || 0) + 1;
        return acc;
      }, {});
      
      console.log('\n📊 Business types distribution:');
      Object.entries(typeCounts).forEach(([type, count]) => {
        console.log(`  ${type}: ${count}`);
      });

      // Check approval status
      const approvedCount = businesses.filter(b => b.is_approved).length;
      console.log(`\n📊 Approval status: ${approvedCount}/${businesses.length} approved`);
    } else {
      console.log('📭 No businesses found in database');
    }

  } catch (error) {
    console.log(`❌ Error examining businesses table: ${error.message}`);
  }
  
  console.log('\n');
}

async function checkOrders() {
  console.log('📦 ORDERS TABLE');
  console.log('='.repeat(50));

  try {
    const { data: orders, error } = await supabase
      .from('orders')
      .select('id, user_id, business_id, status, created_at')
      .limit(10);

    if (error) {
      console.log(`❌ Error querying orders: ${error.message}`);
      return;
    }

    console.log(`📈 Total sample orders: ${orders?.length || 0}`);
    
    if (orders && orders.length > 0) {
      console.log('\n📋 Sample orders:');
      orders.forEach((order, index) => {
        console.log(`  ${index + 1}. Order ${order.id} - User: ${order.user_id} - Business: ${order.business_id} - Status: ${order.status}`);
      });

      // Analyze order statuses
      const statusCounts = orders.reduce((acc, order) => {
        const status = order.status || 'unknown';
        acc[status] = (acc[status] || 0) + 1;
        return acc;
      }, {});
      
      console.log('\n📊 Order status distribution:');
      Object.entries(statusCounts).forEach(([status, count]) => {
        console.log(`  ${status}: ${count}`);
      });
    } else {
      console.log('📭 No orders found in database');
    }

  } catch (error) {
    console.log(`❌ Error examining orders table: ${error.message}`);
  }
  
  console.log('\n');
}

async function checkExistingConnectionProfiles() {
  console.log('👤 EXISTING CONNECTION PROFILES');
  console.log('='.repeat(50));

  try {
    const { data: profiles, error } = await supabase
      .from('connection_profiles')
      .select('*')
      .limit(10);

    if (error) {
      console.log(`❌ Error querying connection_profiles: ${error.message}`);
      return;
    }

    console.log(`📈 Total existing profiles: ${profiles?.length || 0}`);
    
    if (profiles && profiles.length > 0) {
      console.log('\n📋 Existing connection profiles:');
      profiles.forEach((profile, index) => {
        console.log(`  ${index + 1}. ${profile.display_name} - User: ${profile.user_id} - Public: ${profile.is_public}`);
      });
    } else {
      console.log('📭 No connection profiles found - this is expected for a new installation');
    }

  } catch (error) {
    console.log(`❌ Error examining connection_profiles table: ${error.message}`);
  }
  
  console.log('\n');
}

// Run the check
checkExistingData()
  .then(() => {
    console.log('✅ Data examination completed');
    console.log('\n💡 Next steps for Connections Hub:');
    console.log('1. Create connection profiles for existing users');
    console.log('2. Set up initial connections between users and businesses');
    console.log('3. Test the connections hub functionality');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Fatal error:', error);
    process.exit(1);
  });
