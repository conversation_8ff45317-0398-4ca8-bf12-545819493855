'use client';

import React, { useEffect, useState } from 'react';

interface AnimatedBackgroundProps {
  children: React.ReactNode;
}

const AnimatedBackground: React.FC<AnimatedBackgroundProps> = ({ children }) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <div className="relative min-h-[90vh] overflow-hidden bg-white">
      {/* Category icons floating in background */}
      {mounted && (
        <>
          {/* Food icons floating */}
          <div
            className="absolute top-[15%] right-[20%] text-5xl opacity-20 animate-float"
            style={{ animationDelay: '-1s' }}
          >
            🍔
          </div>
          <div
            className="absolute top-[40%] left-[10%] text-5xl opacity-20 animate-float"
            style={{ animationDelay: '-3s' }}
          >
            🍕
          </div>
          <div
            className="absolute bottom-[30%] right-[15%] text-5xl opacity-20 animate-float"
            style={{ animationDelay: '-5s' }}
          >
            🥗
          </div>
          <div
            className="absolute bottom-[10%] left-[30%] text-5xl opacity-20 animate-float"
            style={{ animationDelay: '-2.5s' }}
          >
            🛒
          </div>
          <div
            className="absolute top-[60%] right-[30%] text-5xl opacity-20 animate-float"
            style={{ animationDelay: '-4.5s' }}
          >
            🍰
          </div>
          <div
            className="absolute top-[25%] left-[25%] text-5xl opacity-20 animate-float"
            style={{ animationDelay: '-1.5s' }}
          >
            ☕
          </div>
          <div
            className="absolute bottom-[50%] left-[40%] text-5xl opacity-20 animate-float"
            style={{ animationDelay: '-3.5s' }}
          >
            🥑
          </div>
          <div
            className="absolute bottom-[15%] right-[35%] text-5xl opacity-20 animate-float"
            style={{ animationDelay: '-6s' }}
          >
            🍦
          </div>

          {/* Additional category icons */}
          <div
            className="absolute top-[10%] left-[15%] text-5xl opacity-20 animate-float"
            style={{ animationDelay: '-2.2s' }}
          >
            🍝
          </div>
          <div
            className="absolute top-[75%] right-[10%] text-5xl opacity-20 animate-float"
            style={{ animationDelay: '-4.2s' }}
          >
            🧁
          </div>
          <div
            className="absolute top-[35%] right-[40%] text-5xl opacity-20 animate-float"
            style={{ animationDelay: '-1.8s' }}
          >
            🥐
          </div>
          <div
            className="absolute bottom-[40%] left-[20%] text-5xl opacity-20 animate-float"
            style={{ animationDelay: '-3.2s' }}
          >
            🍣
          </div>
          <div
            className="absolute top-[50%] left-[5%] text-5xl opacity-20 animate-float"
            style={{ animationDelay: '-5.5s' }}
          >
            🌮
          </div>
          <div
            className="absolute bottom-[25%] right-[25%] text-5xl opacity-20 animate-float"
            style={{ animationDelay: '-2.8s' }}
          >
            🥪
          </div>
          <div
            className="absolute top-[20%] right-[5%] text-5xl opacity-20 animate-float"
            style={{ animationDelay: '-4.8s' }}
          >
            🍜
          </div>
          <div
            className="absolute bottom-[60%] right-[45%] text-5xl opacity-20 animate-float"
            style={{ animationDelay: '-1.3s' }}
          >
            🥤
          </div>
          <div
            className="absolute top-[85%] left-[45%] text-5xl opacity-20 animate-float"
            style={{ animationDelay: '-3.7s' }}
          >
            🧆
          </div>
          <div
            className="absolute top-[5%] right-[35%] text-5xl opacity-20 animate-float"
            style={{ animationDelay: '-5.2s' }}
          >
            🍩
          </div>
          <div
            className="absolute bottom-[5%] right-[5%] text-5xl opacity-20 animate-float"
            style={{ animationDelay: '-2.3s' }}
          >
            🍲
          </div>

          {/* Clothing/Jumper icon */}
          <div
            className="absolute top-[45%] right-[25%] text-5xl opacity-20 animate-float"
            style={{ animationDelay: '-2.7s' }}
          >
            👕
          </div>

          {/* Box/Errands icon */}
          <div
            className="absolute bottom-[35%] left-[35%] text-5xl opacity-20 animate-float"
            style={{ animationDelay: '-4.3s' }}
          >
            📦
          </div>

          {/* Additional clothing items */}
          <div
            className="absolute top-[15%] left-[45%] text-5xl opacity-20 animate-float"
            style={{ animationDelay: '-3.9s' }}
          >
            👖
          </div>

          {/* Additional box/package */}
          <div
            className="absolute bottom-[70%] right-[15%] text-5xl opacity-20 animate-float"
            style={{ animationDelay: '-1.7s' }}
          >
            🧳
          </div>
        </>
      )}

      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>

      {/* Add new animations for sliding */}
      <style jsx global>{`
        @keyframes slide-right {
          0% {
            transform: translateX(-150%);
          }
          100% {
            transform: translateX(150vw);
          }
        }

        @keyframes slide-left {
          0% {
            transform: translateX(150vw);
          }
          100% {
            transform: translateX(-150%);
          }
        }

        .animate-slide-right {
          animation: slide-right linear infinite;
        }

        .animate-slide-left {
          animation: slide-left linear infinite;
        }
      `}</style>
    </div>
  );
};

export default AnimatedBackground;
