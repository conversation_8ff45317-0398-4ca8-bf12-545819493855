"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>eader, CardTitle, CardContent, CardFooter, CardDescription } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger, <PERSON><PERSON>Content } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { useAuth } from "@/context/unified-auth-context"
import { useRouter } from "next/navigation"
import {
  MessageSquare,
  Star,
  UserCircle,
  Building2,
  Users,
  ShieldCheck,
  ArrowRight,
  CheckCircle2,
  Clock,
  MapPin,
  Coffee,
  Truck,
  ShoppingBag,
  ArrowLeft,
  Home
} from "lucide-react"

export default function ConnectionsHubHelp() {
  const router = useRouter()
  const { user, userProfile, isAdmin, isSuperAdmin } = useAuth()
  const [activeTab, setActiveTab] = useState("overview")

  const userRole = isAdmin || isSuperAdmin ? "admin" :
                  userProfile?.role === "business_manager" ? "business" :
                  userProfile?.role === "rider" ? "rider" : "customer"

  const getRoleIcon = () => {
    switch (userRole) {
      case "business":
        return <Building2 className="h-5 w-5 text-blue-600" />
      case "rider":
        return <Truck className="h-5 w-5 text-green-600" />
      case "admin":
        return <ShieldCheck className="h-5 w-5 text-purple-600" />
      default:
        return <ShoppingBag className="h-5 w-5 text-orange-600" />
    }
  }

  const getRoleSpecificTabs = () => {
    const baseTabs = [
      { value: "overview", label: "Overview" },
      { value: "getting-started", label: "Getting Started" }
    ]

    if (userRole === "customer" || userRole === "admin") {
      baseTabs.push({ value: "for-customers", label: "For Customers" })
    }
    if (userRole === "business" || userRole === "admin") {
      baseTabs.push({ value: "for-businesses", label: "For Businesses" })
    }
    if (userRole === "rider" || userRole === "admin") {
      baseTabs.push({ value: "for-riders", label: "For Riders" })
    }

    return baseTabs
  }

  const tabs = getRoleSpecificTabs()

  return (
    <div className="min-h-screen bg-white">
      <main className="container-fluid py-6">
        <div className="space-y-8">
          {/* Header */}
          <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <div>
              <div className="flex items-center gap-2 mb-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => router.push('/connections-hub')}
                  className="text-muted-foreground hover:text-foreground"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Connections Hub
                </Button>
              </div>
              <h1 className="text-2xl font-bold flex items-center gap-2">
                Connections Hub Help & Information
                {getRoleIcon()}
              </h1>
              <p className="text-gray-500">
                Learn how to use the Connections Hub effectively
              </p>
              <Badge variant="outline" className="mt-2 capitalize">
                {userRole} Account
              </Badge>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/connections-hub')}
              >
                <Home className="h-4 w-4 mr-2" />
                Go to Hub
              </Button>
            </div>
          </div>

          {/* Main Navigation */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className={`grid w-full max-w-4xl grid-cols-${Math.min(tabs.length, 4)}`}>
              {tabs.map((tab) => (
                <TabsTrigger key={tab.value} value={tab.value}>
                  {tab.label}
                </TabsTrigger>
              ))}
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-6 mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="flex items-center gap-2">
                      <MessageSquare className="h-5 w-5 text-blue-500" />
                      Order Chat
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">
                      Secure, direct communication between all parties during active orders. Resolve issues faster and
                      improve delivery accuracy.
                    </p>
                  </CardContent>
                  <CardFooter className="bg-muted/50 p-3 text-xs">
                    <div className="flex items-start gap-2">
                      <CheckCircle2 className="h-4 w-4 text-green-500 mt-0.5" />
                      <p>Available during active orders only, for delivery-related communication</p>
                    </div>
                  </CardFooter>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="flex items-center gap-2">
                      <Star className="h-5 w-5 text-amber-500" />
                      Favorite Connections
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">
                      Save your preferred partners to build ongoing relationships. Continue communication even after
                      orders are completed.
                    </p>
                  </CardContent>
                  <CardFooter className="bg-muted/50 p-3 text-xs">
                    <div className="flex items-start gap-2">
                      <CheckCircle2 className="h-4 w-4 text-green-500 mt-0.5" />
                      <p>Add up to 20 favorite connections to your network</p>
                    </div>
                  </CardFooter>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="flex items-center gap-2">
                      <UserCircle className="h-5 w-5 text-purple-500" />
                      Profile Management
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">
                      Create your professional profile to share your information, preferences, and special requirements.
                      Build trust within the community.
                    </p>
                  </CardContent>
                  <CardFooter className="bg-muted/50 p-3 text-xs">
                    <div className="flex items-start gap-2">
                      <CheckCircle2 className="h-4 w-4 text-green-500 mt-0.5" />
                      <p>Customize your profile to highlight your strengths and preferences</p>
                    </div>
                  </CardFooter>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="flex items-center gap-2">
                      <Users className="h-5 w-5 text-teal-500" />
                      Connection Requests
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">
                      Send and receive connection requests to establish trusted partnerships. Build a reliable network
                      within the Loop Jersey ecosystem.
                    </p>
                  </CardContent>
                  <CardFooter className="bg-muted/50 p-3 text-xs">
                    <div className="flex items-start gap-2">
                      <CheckCircle2 className="h-4 w-4 text-green-500 mt-0.5" />
                      <p>All connections are mutual and require acceptance from both parties</p>
                    </div>
                  </CardFooter>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>How It Works</CardTitle>
                  <CardDescription>
                    The Connections Hub creates a professional network within our delivery ecosystem
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                    <div className="space-y-2">
                      <div className="bg-muted rounded-full w-12 h-12 flex items-center justify-center mx-auto">
                        <UserCircle className="h-6 w-6" />
                      </div>
                      <h3 className="font-medium">1. Create Your Profile</h3>
                      <p className="text-sm text-muted-foreground">
                        Set up your profile with your preferences and requirements
                      </p>
                    </div>
                    <div className="space-y-2">
                      <div className="bg-muted rounded-full w-12 h-12 flex items-center justify-center mx-auto">
                        <Users className="h-6 w-6" />
                      </div>
                      <h3 className="font-medium">2. Make Connections</h3>
                      <p className="text-sm text-muted-foreground">
                        Send connection requests to partners you work with regularly
                      </p>
                    </div>
                    <div className="space-y-2">
                      <div className="bg-muted rounded-full w-12 h-12 flex items-center justify-center mx-auto">
                        <MessageSquare className="h-6 w-6" />
                      </div>
                      <h3 className="font-medium">3. Communicate</h3>
                      <p className="text-sm text-muted-foreground">
                        Chat during orders and maintain relationships with your connections
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Getting Started Tab */}
            <TabsContent value="getting-started" className="space-y-6 mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Getting Started</CardTitle>
                  <CardDescription>Set up your profile and start making connections</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    Follow these steps to get the most out of the Connections Hub:
                  </p>

                  <Accordion type="single" collapsible className="w-full">
                    <AccordionItem value="step-1">
                      <AccordionTrigger>Step 1: Complete Your Profile</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-2">
                          <p className="text-sm">Add your display name, bio, and profile picture to help others recognize you.</p>
                          <p className="text-sm">Set your communication preferences and availability information.</p>
                          <p className="text-sm">Add any specialties or preferences that help partners understand your needs.</p>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="step-2">
                      <AccordionTrigger>Step 2: Discover and Connect</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-2">
                          <p className="text-sm">Use the Discover tab to find businesses, riders, or customers you want to connect with.</p>
                          <p className="text-sm">Send connection requests to people you work with regularly or want to build relationships with.</p>
                          <p className="text-sm">Accept connection requests from others who want to connect with you.</p>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="step-3">
                      <AccordionTrigger>Step 3: Start Communicating</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-2">
                          <p className="text-sm">Use the Messages tab to communicate with your connections.</p>
                          <p className="text-sm">During active orders, you can chat directly with all parties involved.</p>
                          <p className="text-sm">Mark your favorite connections for easy access and ongoing relationships.</p>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>

                  <div className="mt-6">
                    <Button
                      className="bg-emerald-600 hover:bg-emerald-700"
                      onClick={() => router.push('/connections-hub')}
                    >
                      Start Using Connections Hub
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* For Customers Tab */}
            {(userRole === "customer" || userRole === "admin") && (
              <TabsContent value="for-customers" className="space-y-6 mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Benefits for Customers</CardTitle>
                    <CardDescription>How the Connections Hub improves your ordering experience</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="border rounded-lg p-4 space-y-2">
                        <div className="flex items-center gap-2">
                          <MessageSquare className="h-5 w-5 text-blue-500" />
                          <h3 className="font-medium">Direct Communication</h3>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Communicate directly with businesses about allergens, special requests, and order modifications.
                        </p>
                      </div>

                      <div className="border rounded-lg p-4 space-y-2">
                        <div className="flex items-center gap-2">
                          <Truck className="h-5 w-5 text-green-500" />
                          <h3 className="font-medium">Delivery Updates</h3>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Get real-time updates from your delivery rider about arrival times and any delays.
                        </p>
                      </div>

                      <div className="border rounded-lg p-4 space-y-2">
                        <div className="flex items-center gap-2">
                          <Star className="h-5 w-5 text-amber-500" />
                          <h3 className="font-medium">Favorite Partners</h3>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Save your favorite businesses and riders for faster ordering and better service.
                        </p>
                      </div>

                      <div className="border rounded-lg p-4 space-y-2">
                        <div className="flex items-center gap-2">
                          <ShieldCheck className="h-5 w-5 text-red-500" />
                          <h3 className="font-medium">Issue Resolution</h3>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Quickly resolve any issues with your order through direct communication channels.
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            )}

            {/* For Businesses Tab */}
            {(userRole === "business" || userRole === "admin") && (
              <TabsContent value="for-businesses" className="space-y-6 mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Benefits for Your Business</CardTitle>
                    <CardDescription>How the Connections Hub helps your business operations</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="border rounded-lg p-4 space-y-2">
                        <div className="flex items-center gap-2">
                          <Clock className="h-5 w-5 text-green-500" />
                          <h3 className="font-medium">Improved Timing</h3>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Coordinate with riders to ensure food is prepared just in time for pickup, improving food quality
                          and customer satisfaction.
                        </p>
                      </div>

                      <div className="border rounded-lg p-4 space-y-2">
                        <div className="flex items-center gap-2">
                          <Users className="h-5 w-5 text-purple-500" />
                          <h3 className="font-medium">Reliable Riders</h3>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Build relationships with trusted riders who understand your business and can represent your brand
                          well to customers.
                        </p>
                      </div>

                      <div className="border rounded-lg p-4 space-y-2">
                        <div className="flex items-center gap-2">
                          <MessageSquare className="h-5 w-5 text-blue-500" />
                          <h3 className="font-medium">Issue Resolution</h3>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Quickly resolve delivery issues through direct communication, reducing customer complaints and
                          refunds.
                        </p>
                      </div>

                      <div className="border rounded-lg p-4 space-y-2">
                        <div className="flex items-center gap-2">
                          <Star className="h-5 w-5 text-amber-500" />
                          <h3 className="font-medium">Rush Hour Support</h3>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Connect with riders who are available during your peak hours to ensure smooth operations during busy
                          periods.
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            )}

            {/* For Riders Tab */}
            {(userRole === "rider" || userRole === "admin") && (
              <TabsContent value="for-riders" className="space-y-6 mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Benefits for Riders</CardTitle>
                    <CardDescription>How the Connections Hub helps delivery riders</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="border rounded-lg p-4 space-y-2">
                        <div className="flex items-center gap-2">
                          <Building2 className="h-5 w-5 text-blue-500" />
                          <h3 className="font-medium">Preferred Businesses</h3>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Build relationships with businesses that value your service and offer consistent work opportunities.
                        </p>
                      </div>

                      <div className="border rounded-lg p-4 space-y-2">
                        <div className="flex items-center gap-2">
                          <Clock className="h-5 w-5 text-green-500" />
                          <h3 className="font-medium">Better Coordination</h3>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Communicate directly with businesses about pickup times and special requirements.
                        </p>
                      </div>

                      <div className="border rounded-lg p-4 space-y-2">
                        <div className="flex items-center gap-2">
                          <Star className="h-5 w-5 text-amber-500" />
                          <h3 className="font-medium">Higher Ratings</h3>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Better communication leads to smoother deliveries and higher customer satisfaction ratings.
                        </p>
                      </div>

                      <div className="border rounded-lg p-4 space-y-2">
                        <div className="flex items-center gap-2">
                          <MessageSquare className="h-5 w-5 text-blue-500" />
                          <h3 className="font-medium">Customer Communication</h3>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Communicate with customers about delivery updates and special instructions.
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            )}
          </Tabs>
        </div>
      </main>
    </div>
  )
}