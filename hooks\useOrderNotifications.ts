"use client"

import { useEffect, useState } from 'react'
import { createClient } from '@supabase/supabase-js'
import { useAuth } from '@/contexts/auth-context'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

interface OrderNotification {
  id: string
  title: string
  body: string
  type: string
  order_id?: number
  created_at: string
  is_read: boolean
}

interface UseOrderNotificationsReturn {
  notifications: OrderNotification[]
  unreadCount: number
  markAsRead: (notificationId: string) => Promise<void>
  markAllAsRead: () => Promise<void>
  isLoading: boolean
  error: string | null
}

export function useOrderNotifications(): UseOrderNotificationsReturn {
  const { user } = useAuth()
  const [notifications, setNotifications] = useState<OrderNotification[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Calculate unread count
  const unreadCount = notifications.filter(n => !n.is_read).length

  // Fetch initial notifications
  useEffect(() => {
    if (!user) {
      setNotifications([])
      setIsLoading(false)
      return
    }

    fetchNotifications()
  }, [user])

  // Set up real-time subscription
  useEffect(() => {
    if (!user) return

    const channel = supabase
      .channel('order-notifications')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'communications',
          filter: `recipient_id=eq.${user.id}`
        },
        (payload) => {
          console.log('New notification received:', payload)
          handleNewNotification(payload.new as any)
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'communications',
          filter: `recipient_id=eq.${user.id}`
        },
        (payload) => {
          console.log('Notification updated:', payload)
          handleNotificationUpdate(payload.new as any)
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [user])

  const fetchNotifications = async () => {
    if (!user) return

    try {
      setIsLoading(true)
      setError(null)

      // Fetch order-related communications
      const { data, error: fetchError } = await supabase
        .from('communications')
        .select(`
          id,
          subject,
          content,
          message_type,
          order_id,
          created_at,
          is_read,
          is_urgent,
          priority
        `)
        .eq('recipient_id', user.id)
        .eq('channel_type', 'active_order_delivery')
        .eq('is_automated', true)
        .order('created_at', { ascending: false })
        .limit(50)

      if (fetchError) {
        throw new Error(fetchError.message)
      }

      // Transform to notification format
      const transformedNotifications: OrderNotification[] = (data || []).map(comm => ({
        id: comm.id,
        title: comm.subject || 'Order Update',
        body: comm.content,
        type: comm.message_type || 'order_update',
        order_id: comm.order_id,
        created_at: comm.created_at,
        is_read: comm.is_read
      }))

      setNotifications(transformedNotifications)
    } catch (err) {
      console.error('Error fetching notifications:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch notifications')
    } finally {
      setIsLoading(false)
    }
  }

  const handleNewNotification = (newCommunication: any) => {
    // Only handle order-related automated notifications
    if (
      newCommunication.channel_type === 'active_order_delivery' &&
      newCommunication.is_automated
    ) {
      const notification: OrderNotification = {
        id: newCommunication.id,
        title: newCommunication.subject || 'Order Update',
        body: newCommunication.content,
        type: newCommunication.message_type || 'order_update',
        order_id: newCommunication.order_id,
        created_at: newCommunication.created_at,
        is_read: newCommunication.is_read
      }

      setNotifications(prev => [notification, ...prev])

      // Show browser notification if permission granted
      if (Notification.permission === 'granted') {
        new Notification(notification.title, {
          body: notification.body,
          icon: '/android-chrome-192x192.png',
          tag: `order-${notification.order_id}`,
          data: {
            orderId: notification.order_id,
            notificationId: notification.id
          }
        })
      }
    }
  }

  const handleNotificationUpdate = (updatedCommunication: any) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === updatedCommunication.id
          ? {
              ...notification,
              is_read: updatedCommunication.is_read,
              title: updatedCommunication.subject || notification.title,
              body: updatedCommunication.content
            }
          : notification
      )
    )
  }

  const markAsRead = async (notificationId: string) => {
    try {
      const { error } = await supabase
        .from('communications')
        .update({ 
          is_read: true,
          read_at: new Date().toISOString()
        })
        .eq('id', notificationId)
        .eq('recipient_id', user?.id)

      if (error) {
        throw new Error(error.message)
      }

      // Update local state
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === notificationId
            ? { ...notification, is_read: true }
            : notification
        )
      )
    } catch (err) {
      console.error('Error marking notification as read:', err)
    }
  }

  const markAllAsRead = async () => {
    if (!user) return

    try {
      const unreadIds = notifications
        .filter(n => !n.is_read)
        .map(n => n.id)

      if (unreadIds.length === 0) return

      const { error } = await supabase
        .from('communications')
        .update({ 
          is_read: true,
          read_at: new Date().toISOString()
        })
        .in('id', unreadIds)
        .eq('recipient_id', user.id)

      if (error) {
        throw new Error(error.message)
      }

      // Update local state
      setNotifications(prev =>
        prev.map(notification => ({ ...notification, is_read: true }))
      )
    } catch (err) {
      console.error('Error marking all notifications as read:', err)
    }
  }

  return {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    isLoading,
    error
  }
}
