import { NextResponse } from "next/server";
import { createServerSupabase } from "@/lib/supabase-server";
import { createClient } from "@supabase/supabase-js";

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const adminClient = createClient(supabaseUrl, supabaseServiceKey);

// DELETE handler to remove an address
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const addressId = params.id;

    // Get the user's session
    const supabase = await createServerSupabase();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Get user ID from the users table using admin client
    const { data: userData, error: userError } = await adminClient
      .from("users")
      .select("id")
      .eq("email", session.user.email)
      .single();

    if (userError || !userData) {
      console.error("Error fetching user ID:", userError);
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Verify the address belongs to the user using admin client
    const { data: addressData, error: addressError } = await adminClient
      .from("user_addresses")
      .select("id, is_default")
      .eq("id", addressId)
      .eq("user_id", userData.id)
      .single();

    if (addressError || !addressData) {
      return NextResponse.json(
        { error: "Address not found or not authorized" },
        { status: 404 }
      );
    }

    // Delete the address using admin client
    const { error: deleteError } = await adminClient
      .from("user_addresses")
      .delete()
      .eq("id", addressId)
      .eq("user_id", userData.id);

    if (deleteError) {
      console.error("Error deleting address:", deleteError);
      return NextResponse.json(
        { error: "Failed to delete address" },
        { status: 500 }
      );
    }

    // If the deleted address was the default, set another address as default
    if (addressData.is_default) {
      const { data: otherAddresses, error: fetchError } = await adminClient
        .from("user_addresses")
        .select("id")
        .eq("user_id", userData.id)
        .order("created_at", { ascending: false })
        .limit(1);

      if (!fetchError && otherAddresses && otherAddresses.length > 0) {
        await adminClient
          .from("user_addresses")
          .update({ is_default: true })
          .eq("id", otherAddresses[0].id);
      }
    }

    return NextResponse.json({
      message: "Address deleted successfully"
    });
  } catch (error: any) {
    console.error(`Unexpected error in DELETE /api/user/addresses/${params.id}:`, error);
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}

// PATCH handler to update an address
export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const addressId = params.id;
    const body = await request.json();
    const { address_name, address_line1, address_line2, parish, postcode } = body;

    // Get the user's session
    const supabase = await createServerSupabase();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Get user ID from the users table using admin client
    const { data: userData, error: userError } = await adminClient
      .from("users")
      .select("id")
      .eq("email", session.user.email)
      .single();

    if (userError || !userData) {
      console.error("Error fetching user ID:", userError);
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Check if an address with the same name already exists for this user (excluding the current address)
    if (address_name) {
      const { data: existingAddresses, error: checkAddressError } = await adminClient
        .from("user_addresses")
        .select("id, address_name")
        .eq("user_id", userData.id)
        .eq("address_name", address_name)
        .neq("id", addressId);

      if (checkAddressError) {
        console.error("Error checking existing addresses:", checkAddressError);
      } else if (existingAddresses && existingAddresses.length > 0) {
        return NextResponse.json(
          { error: `You already have an address named "${address_name}". Please use a different name.` },
          { status: 400 }
        );
      }
    }

    // Verify the address belongs to the user using admin client
    const { data: addressData, error: addressError } = await adminClient
      .from("user_addresses")
      .select("id")
      .eq("id", addressId)
      .eq("user_id", userData.id)
      .single();

    if (addressError || !addressData) {
      return NextResponse.json(
        { error: "Address not found or not authorized" },
        { status: 404 }
      );
    }

    // Update the address
    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    if (address_name) updateData.address_name = address_name;
    if (address_line1) updateData.address_line1 = address_line1;
    if (address_line2 !== undefined) updateData.address_line2 = address_line2;
    if (parish) updateData.parish = parish;
    if (postcode) updateData.postcode = postcode;

    const { data: updatedAddress, error: updateError } = await adminClient
      .from("user_addresses")
      .update(updateData)
      .eq("id", addressId)
      .eq("user_id", userData.id)
      .select()
      .single();

    if (updateError) {
      console.error("Error updating address:", updateError);
      return NextResponse.json(
        { error: "Failed to update address" },
        { status: 500 }
      );
    }

    // If address components were updated, update the coordinates
    if (address_line1 || address_line2 !== undefined || parish || postcode) {
      try {
        // Get the current address data to ensure we have all fields
        const { data: currentAddress } = await adminClient
          .from("user_addresses")
          .select("*")
          .eq("id", addressId)
          .single();

        if (currentAddress) {
          const { geocodeAndUpdateUserAddressCoordinates } = await import('@/lib/address-utils');
          await geocodeAndUpdateUserAddressCoordinates(
            addressId,
            currentAddress.address_line1,
            currentAddress.address_line2,
            currentAddress.parish,
            currentAddress.postcode
          );

          // Get the updated address with coordinates
          const { data: addressWithCoords } = await adminClient
            .from("user_addresses")
            .select("*")
            .eq("id", addressId)
            .single();

          if (addressWithCoords) {
            return NextResponse.json({
              message: "Address updated successfully with coordinates",
              address: addressWithCoords
            });
          }
        }
      } catch (geocodeError) {
        console.error("Error geocoding updated address:", geocodeError);
        // Continue anyway, as the address was updated successfully
      }
    }

    return NextResponse.json({
      message: "Address updated successfully",
      address: updatedAddress
    });
  } catch (error: any) {
    console.error(`Unexpected error in PATCH /api/user/addresses/${params.id}:`, error);
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
