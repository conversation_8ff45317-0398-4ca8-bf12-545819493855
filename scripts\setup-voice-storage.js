// Setup script for Supabase Storage bucket for voice messages
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY // Use service role for admin operations
);

async function setupVoiceStorage() {
  console.log('🎤 Setting up voice message storage...');

  try {
    // 1. Create the voice-messages bucket
    console.log('📁 Creating voice-messages bucket...');
    
    const { data: bucketData, error: bucketError } = await supabase.storage.createBucket('voice-messages', {
      public: false, // Private bucket for security
      fileSizeLimit: 10 * 1024 * 1024, // 10MB limit per file
      allowedMimeTypes: ['audio/webm', 'audio/mp4', 'audio/wav', 'audio/ogg']
    });

    if (bucketError) {
      if (bucketError.message.includes('already exists')) {
        console.log('✅ Bucket already exists, continuing...');
      } else {
        throw bucketError;
      }
    } else {
      console.log('✅ Bucket created successfully');
    }

    // 2. Set up RLS (Row Level Security) policies
    console.log('🔒 Setting up security policies...');
    
    // Policy to allow authenticated users to upload their own voice messages
    const uploadPolicy = `
      CREATE POLICY "Users can upload voice messages" ON storage.objects
      FOR INSERT WITH CHECK (
        bucket_id = 'voice-messages' AND
        auth.uid()::text = (storage.foldername(name))[1]
      );
    `;

    // Policy to allow users to read voice messages they're involved in
    const readPolicy = `
      CREATE POLICY "Users can read voice messages" ON storage.objects
      FOR SELECT USING (
        bucket_id = 'voice-messages' AND (
          auth.uid()::text = (storage.foldername(name))[1] OR
          EXISTS (
            SELECT 1 FROM communications 
            WHERE audio_file_name = name 
            AND (sender_id = auth.uid() OR recipient_id = auth.uid())
          )
        )
      );
    `;

    // Policy to allow users to delete their own voice messages
    const deletePolicy = `
      CREATE POLICY "Users can delete their voice messages" ON storage.objects
      FOR DELETE USING (
        bucket_id = 'voice-messages' AND
        auth.uid()::text = (storage.foldername(name))[1]
      );
    `;

    // Execute policies (ignore errors if they already exist)
    try {
      await supabase.rpc('exec_sql', { sql_query: uploadPolicy });
      console.log('✅ Upload policy created');
    } catch (e) {
      console.log('ℹ️ Upload policy may already exist');
    }

    try {
      await supabase.rpc('exec_sql', { sql_query: readPolicy });
      console.log('✅ Read policy created');
    } catch (e) {
      console.log('ℹ️ Read policy may already exist');
    }

    try {
      await supabase.rpc('exec_sql', { sql_query: deletePolicy });
      console.log('✅ Delete policy created');
    } catch (e) {
      console.log('ℹ️ Delete policy may already exist');
    }

    // 3. Test the bucket
    console.log('🧪 Testing bucket access...');
    
    const { data: buckets, error: listError } = await supabase.storage.listBuckets();
    if (listError) throw listError;
    
    const voiceBucket = buckets.find(b => b.name === 'voice-messages');
    if (voiceBucket) {
      console.log('✅ Bucket accessible:', voiceBucket);
    } else {
      throw new Error('Bucket not found after creation');
    }

    console.log('🎉 Voice storage setup complete!');
    console.log('');
    console.log('📋 Summary:');
    console.log('- Bucket: voice-messages (private)');
    console.log('- Max file size: 10MB');
    console.log('- Allowed types: audio/webm, audio/mp4, audio/wav, audio/ogg');
    console.log('- Security: RLS policies enabled');
    
    return true;

  } catch (error) {
    console.error('❌ Setup failed:', error);
    return false;
  }
}

// Run the setup
setupVoiceStorage()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('❌ Unexpected error:', error);
    process.exit(1);
  });
