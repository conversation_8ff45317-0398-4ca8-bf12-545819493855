const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function createDummyMessagingData() {
  console.log('🗄️ Creating Dummy Messaging Data\n');

  try {
    // Step 1: Create test users
    console.log('1️⃣ Creating test users...');
    
    const testUsers = [
      {
        id: '11111111-1111-1111-1111-111111111111',
        email: '<EMAIL>',
        name: '<PERSON>'
      },
      {
        id: '22222222-2222-2222-2222-222222222222', 
        email: '<EMAIL>',
        name: 'Pizza Palace'
      },
      {
        id: '33333333-3333-3333-3333-333333333333',
        email: '<EMAIL>', 
        name: '<PERSON>'
      },
      {
        id: '44444444-4444-4444-4444-444444444444',
        email: '<EMAIL>',
        name: 'Burger Junction'
      }
    ];

    // Insert users into auth.users table (this might fail if users exist, that's OK)
    for (const user of testUsers) {
      try {
        const { error } = await supabase.auth.admin.createUser({
          user_id: user.id,
          email: user.email,
          password: 'test-password-123',
          email_confirm: true,
          user_metadata: {
            name: user.name
          }
        });
        
        if (error && !error.message.includes('already registered')) {
          console.log(`   ⚠️  Could not create user ${user.name}: ${error.message}`);
        } else {
          console.log(`   ✅ User created/exists: ${user.name}`);
        }
      } catch (err) {
        console.log(`   ⚠️  User creation error for ${user.name}: ${err.message}`);
      }
    }

    // Step 2: Create connections between users
    console.log('\n2️⃣ Creating connections...');
    
    const connections = [
      {
        user1_id: testUsers[0].id, // Customer
        user2_id: testUsers[1].id, // Pizza Palace
        connection_type: 'customer-business',
        status: 'active'
      },
      {
        user1_id: testUsers[0].id, // Customer  
        user2_id: testUsers[2].id, // Rider
        connection_type: 'customer-rider',
        status: 'active'
      },
      {
        user1_id: testUsers[0].id, // Customer
        user2_id: testUsers[3].id, // Burger Junction
        connection_type: 'customer-business', 
        status: 'active'
      }
    ];

    for (const connection of connections) {
      const { error } = await supabase
        .from('connections')
        .upsert(connection, { onConflict: 'user1_id,user2_id' });
      
      if (error) {
        console.log(`   ⚠️  Connection error: ${error.message}`);
      } else {
        console.log(`   ✅ Connection created between users`);
      }
    }

    // Step 3: Create realistic conversations with messages
    console.log('\n3️⃣ Creating conversations and messages...');

    const conversations = [
      {
        thread_id: 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
        participants: [testUsers[0].id, testUsers[1].id], // Customer & Pizza Palace
        channel_type: 'order',
        messages: [
          {
            sender_id: testUsers[0].id,
            content: 'Hi! Do you have any vegan cheese options for your pizzas?',
            subject: 'Vegan Options Inquiry',
            message_type: 'chat',
            created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString() // 2 hours ago
          },
          {
            sender_id: testUsers[1].id,
            content: 'Yes! We have excellent vegan mozzarella and cheddar. Both are very popular. There\'s a £2 surcharge for vegan cheese.',
            message_type: 'chat',
            created_at: new Date(Date.now() - 1.5 * 60 * 60 * 1000).toISOString() // 1.5 hours ago
          },
          {
            sender_id: testUsers[0].id,
            content: 'Perfect! I\'ll place an order for a large margherita with vegan mozzarella.',
            message_type: 'chat',
            created_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString() // 1 hour ago
          }
        ]
      },
      {
        thread_id: 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb',
        participants: [testUsers[0].id, testUsers[2].id], // Customer & Rider
        channel_type: 'order',
        messages: [
          {
            sender_id: testUsers[2].id,
            content: 'Hi! I\'m Mike, your delivery rider. I\'ve picked up your pizza from Pizza Palace and I\'m on my way!',
            subject: 'Delivery Update',
            message_type: 'status_update',
            is_automated: false,
            created_at: new Date(Date.now() - 15 * 60 * 1000).toISOString() // 15 minutes ago
          },
          {
            sender_id: testUsers[0].id,
            content: 'Great! I\'m at home. How long do you think it will take?',
            message_type: 'chat',
            created_at: new Date(Date.now() - 12 * 60 * 1000).toISOString() // 12 minutes ago
          },
          {
            sender_id: testUsers[2].id,
            content: 'About 8 minutes! I\'m just around the corner from your street.',
            message_type: 'chat',
            created_at: new Date(Date.now() - 5 * 60 * 1000).toISOString() // 5 minutes ago
          }
        ]
      },
      {
        thread_id: 'cccccccc-cccc-cccc-cccc-cccccccccccc',
        participants: [testUsers[1].id, testUsers[3].id], // Pizza Palace & Burger Junction
        channel_type: 'general',
        messages: [
          {
            sender_id: testUsers[3].id,
            content: 'Hey! We\'re thinking about doing a joint promotion for lunch deliveries. Would you be interested in partnering?',
            subject: 'Partnership Opportunity',
            message_type: 'chat',
            created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() // 1 day ago
          },
          {
            sender_id: testUsers[1].id,
            content: 'That sounds interesting! What did you have in mind? Maybe a pizza + burger combo deal?',
            message_type: 'chat',
            created_at: new Date(Date.now() - 20 * 60 * 60 * 1000).toISOString() // 20 hours ago
          }
        ]
      },
      {
        thread_id: 'dddddddd-dddd-dddd-dddd-dddddddddddd',
        participants: [testUsers[0].id, testUsers[3].id], // Customer & Burger Junction
        channel_type: 'pre-order',
        messages: [
          {
            sender_id: testUsers[0].id,
            content: 'Hi! I\'m planning a small party for next weekend. Do you do catering orders? I\'d need about 15 burgers.',
            subject: 'Catering Inquiry',
            message_type: 'chat',
            created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString() // 3 days ago
          },
          {
            sender_id: testUsers[3].id,
            content: 'Absolutely! We love catering orders. For 15 burgers, we can offer a 10% discount. Would you like a mix of our signature burgers?',
            message_type: 'chat',
            created_at: new Date(Date.now() - 2.5 * 24 * 60 * 60 * 1000).toISOString() // 2.5 days ago
          }
        ]
      }
    ];

    // Insert all messages
    for (const conversation of conversations) {
      console.log(`   📝 Creating conversation: ${conversation.thread_id}`);
      
      for (let i = 0; i < conversation.messages.length; i++) {
        const message = conversation.messages[i];
        const recipient_id = conversation.participants.find(p => p !== message.sender_id);
        
        const messageData = {
          sender_id: message.sender_id,
          recipient_id: recipient_id,
          thread_id: conversation.thread_id,
          channel_type: conversation.channel_type,
          message_type: message.message_type,
          subject: message.subject,
          content: message.content,
          is_automated: message.is_automated || false,
          is_urgent: message.is_urgent || false,
          is_read: i < conversation.messages.length - 1, // Mark all but last message as read
          priority: message.priority || 0,
          created_at: message.created_at
        };

        const { error } = await supabase
          .from('communications')
          .insert(messageData);
        
        if (error) {
          console.log(`     ❌ Message error: ${error.message}`);
        } else {
          console.log(`     ✅ Message created: "${message.content.substring(0, 30)}..."`);
        }
      }
    }

    // Step 4: Create some automated order status messages
    console.log('\n4️⃣ Creating automated order status messages...');
    
    const statusMessages = [
      {
        sender_id: testUsers[1].id, // Pizza Palace
        recipient_id: testUsers[0].id, // Customer
        thread_id: 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee',
        channel_type: 'order',
        message_type: 'status_update',
        subject: 'Order Confirmed',
        content: 'Your order has been confirmed by Pizza Palace (Order #12345)',
        is_automated: true,
        is_urgent: false,
        priority: 1,
        created_at: new Date(Date.now() - 30 * 60 * 1000).toISOString() // 30 minutes ago
      },
      {
        sender_id: testUsers[1].id, // Pizza Palace
        recipient_id: testUsers[0].id, // Customer
        thread_id: 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee',
        channel_type: 'order',
        message_type: 'status_update',
        subject: 'Order Ready',
        content: 'Your order is ready for pickup/delivery from Pizza Palace (Order #12345)',
        is_automated: true,
        is_urgent: true,
        priority: 1,
        created_at: new Date(Date.now() - 20 * 60 * 1000).toISOString() // 20 minutes ago
      }
    ];

    for (const message of statusMessages) {
      const { error } = await supabase
        .from('communications')
        .insert(message);
      
      if (error) {
        console.log(`   ❌ Status message error: ${error.message}`);
      } else {
        console.log(`   ✅ Status message created: ${message.subject}`);
      }
    }

    console.log('\n🎉 Dummy data creation completed!');
    console.log('\n📊 Summary:');
    console.log('   👥 4 test users created');
    console.log('   🔗 3 connections established');
    console.log('   💬 5 conversation threads created');
    console.log('   📝 Multiple messages per conversation');
    console.log('   🤖 Automated order status messages');
    console.log('\n🎯 You can now test the messaging system with realistic data!');

  } catch (error) {
    console.error('❌ Error creating dummy data:', error);
  }
}

// Run the script
createDummyMessagingData();
