"use client"

import { useState, useEffect } from "react"
import { useAuthDirect } from "@/context/auth-context-direct"
import { supabase } from "@/lib/supabase"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, CheckCircle, Clock } from "lucide-react"
import PendingApprovalMessage from "@/components/business/pending-approval"
import Link from "next/link"

export default function DebugPage() {
  const { user, userProfile, refreshUserProfile } = useAuthDirect()
  const [userDetails, setUserDetails] = useState<any>(null)
  const [businessManagerDetails, setBusinessManagerDetails] = useState<any>(null)
  const [businessDetails, setBusinessDetails] = useState<any>(null)
  const [pendingRegistration, setPendingRegistration] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [fixResult, setFixResult] = useState<string | null>(null)

  useEffect(() => {
    const fetchDebugInfo = async () => {
      if (!user) {
        setError("No user is logged in")
        setIsLoading(false)
        return
      }

      try {
        // 1. Get user details from the database
        const { data: userData, error: userError } = await supabase
          .from("users")
          .select("*")
          .eq("email", user.email)
          .single()

        if (userError) {
          setError(`Error fetching user data: ${userError.message}`)
          setIsLoading(false)
          return
        }

        setUserDetails(userData)

        // Check if there's a pending business registration
        const { data: pendingData, error: pendingError } = await supabase
          .from("business_registrations")
          .select("*")
          .eq("user_id", userData.id)
          .maybeSingle()

        if (!pendingError && pendingData) {
          setPendingRegistration(pendingData)
        }

        // 2. Check if there's a business manager entry
        const { data: managerData, error: managerError } = await supabase
          .from("business_managers")
          .select("*")
          .eq("user_id", userData.id)

        // This might not be an error if the user is not a business manager
        if (!managerError) {
          setBusinessManagerDetails(managerData)

          // 3. If there's a business manager entry, get the business details
          if (managerData && managerData.length > 0) {
            const { data: businessData, error: businessError } = await supabase
              .from("businesses")
              .select("*, business_types(name)")
              .eq("id", managerData[0].business_id)
              .single()

            if (!businessError) {
              setBusinessDetails(businessData)
            }
          }
        }
      } catch (err) {
        console.error("Error fetching debug info:", err)
        setError("An unexpected error occurred")
      } finally {
        setIsLoading(false)
      }
    }

    fetchDebugInfo()
  }, [user])

  const setUserRole = async (role: 'customer' | 'business_staff' | 'business_manager' | 'admin' | 'super_admin') => {
    if (!userDetails) return

    try {
      // Update the user's role
      const { error } = await supabase
        .from("users")
        .update({ role })
        .eq("id", userDetails.id)

      if (error) {
        setFixResult(`Error updating user role: ${error.message}`)
        return
      }

      setFixResult(`User role updated to ${role}. Refreshing profile...`)

      // Refresh the user profile in the auth context
      await refreshUserProfile()

      // Reload the page after a short delay
      setTimeout(() => {
        window.location.reload()
      }, 1500)
    } catch (err) {
      console.error("Error setting user role:", err)
      setFixResult("An unexpected error occurred")
    }
  }

  // Legacy function for backward compatibility
  const fixUserRole = () => setUserRole('business_manager')

  const createBusinessManagerEntry = async () => {
    if (!userDetails || !businessDetails) return

    try {
      // Create a business manager entry
      const { error } = await supabase
        .from("business_managers")
        .insert([
          {
            user_id: userDetails.id,
            business_id: businessDetails.id,
            is_primary: true
          }
        ])

      if (error) {
        setFixResult(`Error creating business manager entry: ${error.message}`)
        return
      }

      setFixResult("Business manager entry created. Refreshing page...")

      // Reload the page after a short delay
      setTimeout(() => {
        window.location.reload()
      }, 1500)
    } catch (err) {
      console.error("Error creating business manager entry:", err)
      setFixResult("An unexpected error occurred")
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading debug information...</p>
        </div>
      </div>
    )
  }

  // If there's a pending registration, show the pending approval message
  if (pendingRegistration) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold">Business Registration Status</h1>
            <p className="text-gray-500">Your business registration is pending approval</p>
          </div>
        </div>

        <PendingApprovalMessage />

        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Registration Details</CardTitle>
            <CardDescription>Technical details about your pending registration</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">Registration ID</p>
                  <p>{pendingRegistration.id}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Business Name</p>
                  <p>{pendingRegistration.name}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Status</p>
                  <p className="text-amber-600 font-medium">{pendingRegistration.status || "pending"}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Submitted On</p>
                  <p>{new Date(pendingRegistration.created_at).toLocaleString()}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">Debug Information</h1>
          <p className="text-gray-500">Troubleshooting business admin access</p>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {fixResult && (
        <Alert variant={fixResult.includes("Error") ? "destructive" : "default"} className="bg-green-50 border-green-200">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertTitle>Fix Result</AlertTitle>
          <AlertDescription>{fixResult}</AlertDescription>
        </Alert>
      )}

      <div className="flex flex-wrap gap-4 mb-6">
        <Link href="/business-admin/debug/approval">
          <Button variant="outline" className="flex items-center">
            <CheckCircle className="h-4 w-4 mr-2" />
            Business Approval Debug
          </Button>
        </Link>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>User Information</CardTitle>
          <CardDescription>Details about the current user</CardDescription>
        </CardHeader>
        <CardContent>
          {userDetails ? (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">Email</p>
                  <p>{userDetails.email}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Name</p>
                  <p>{userDetails.name}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Role</p>
                  <p className={
                    userDetails.role === "super_admin" ? "text-purple-600 font-medium" :
                    userDetails.role === "admin" ? "text-red-600 font-medium" :
                    userDetails.role === "business_manager" ? "text-blue-600 font-medium" :
                    userDetails.role === "business_staff" ? "text-green-600 font-medium" :
                    userDetails.role === "customer" ? "text-gray-600 font-medium" :
                    "text-orange-600 font-medium"
                  }>
                    {userDetails.role || "No role set"}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">User ID</p>
                  <p>{userDetails.id}</p>
                </div>
              </div>

              <div className="mt-4">
                <h3 className="text-sm font-medium text-gray-700 mb-2">Set User Role</h3>
                <div className="flex flex-wrap gap-2">
                  <Button
                    onClick={() => setUserRole('customer')}
                    variant={userDetails.role === 'customer' ? 'default' : 'outline'}
                    className={userDetails.role === 'customer' ? 'bg-emerald-600 hover:bg-emerald-700' : ''}
                  >
                    Customer
                  </Button>
                  <Button
                    onClick={() => setUserRole('business_staff')}
                    variant={userDetails.role === 'business_staff' ? 'default' : 'outline'}
                    className={userDetails.role === 'business_staff' ? 'bg-emerald-600 hover:bg-emerald-700' : ''}
                  >
                    Business Staff
                  </Button>
                  <Button
                    onClick={() => setUserRole('business_manager')}
                    variant={userDetails.role === 'business_manager' ? 'default' : 'outline'}
                    className={userDetails.role === 'business_manager' ? 'bg-emerald-600 hover:bg-emerald-700' : ''}
                  >
                    Business Manager
                  </Button>
                  <Button
                    onClick={() => setUserRole('admin')}
                    variant={userDetails.role === 'admin' ? 'default' : 'outline'}
                    className={userDetails.role === 'admin' ? 'bg-emerald-600 hover:bg-emerald-700' : ''}
                  >
                    Admin
                  </Button>
                  <Button
                    onClick={() => setUserRole('super_admin')}
                    variant={userDetails.role === 'super_admin' ? 'default' : 'outline'}
                    className={userDetails.role === 'super_admin' ? 'bg-emerald-600 hover:bg-emerald-700' : ''}
                  >
                    Super Admin
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            <p className="text-gray-500">No user information found</p>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Business Manager Information</CardTitle>
          <CardDescription>Details about the business manager relationship</CardDescription>
        </CardHeader>
        <CardContent>
          {businessManagerDetails && businessManagerDetails.length > 0 ? (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">Business Manager ID</p>
                  <p>{businessManagerDetails[0].id}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">User ID</p>
                  <p>{businessManagerDetails[0].user_id}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Business ID</p>
                  <p>{businessManagerDetails[0].business_id}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Is Primary</p>
                  <p>{businessManagerDetails[0].is_primary ? "Yes" : "No"}</p>
                </div>
              </div>
            </div>
          ) : (
            <div>
              <p className="text-red-600 mb-4">No business manager relationship found</p>

              {businessDetails && (
                <Button onClick={createBusinessManagerEntry} className="bg-emerald-600 hover:bg-emerald-700">
                  Create Business Manager Entry
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Business Information</CardTitle>
          <CardDescription>Details about the managed business</CardDescription>
        </CardHeader>
        <CardContent>
          {businessDetails ? (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">Business ID</p>
                  <p>{businessDetails.id}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Name</p>
                  <p>{businessDetails.name}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Business Type</p>
                  <p>{businessDetails.business_types?.name || "Unknown"}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Address</p>
                  <p>{businessDetails.address}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Approval Status</p>
                  <p className={businessDetails.is_approved ? "text-green-600 font-medium" : "text-amber-600 font-medium"}>
                    {businessDetails.is_approved ? "Approved" : "Pending Approval"}
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <p className="text-gray-500">No business information found</p>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
