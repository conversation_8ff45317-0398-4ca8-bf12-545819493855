import { NextRequest, NextResponse } from 'next/server';
import { calculateBusinessDeliveryFee } from '@/lib/delivery-fee-calculator';

export async function POST(request: NextRequest) {
  try {
    const {
      businessId,
      businessCoordinates,
      customerCoordinates,
      postcode,
      deliveryFeeModel,
      deliveryFee,
      deliveryFeePerKm
    } = await request.json();

    // Validate required parameters
    if (!businessId || !businessCoordinates || !postcode) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // Calculate delivery fee
    const result = await calculateBusinessDeliveryFee(
      {
        coordinates: businessCoordinates,
        delivery_fee_model: deliveryFeeModel || 'fixed',
        delivery_fee: deliveryFee || 2.50,
        delivery_fee_per_km: deliveryFeePerKm || 0.50
      },
      postcode,
      customerCoordinates,
      true // isCheckout = true
    );

    return NextResponse.json({
      fee: result.fee,
      distance: result.routeDistance || result.distance,
      routeDistance: result.routeDistance,
      calculationMethod: result.calculationMethod,
      mapboxError: result.mapboxError
    });
  } catch (error) {
    console.error('Error calculating delivery fee:', error);
    return NextResponse.json(
      { error: 'Failed to calculate delivery fee' },
      { status: 500 }
    );
  }
}
