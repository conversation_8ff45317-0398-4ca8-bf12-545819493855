"use client"

import UnifiedBusinessAdminLayout from "./unified-layout"
import BusinessA<PERSON>rovalCheck from "@/components/business/approval-check"
import { AuthProviderDirect } from "@/context/auth-context-direct"
import { SupabaseProvider } from "@/components/providers/supabase-provider"

export default function BusinessAdminLayout({ children }: { children: React.ReactNode }) {
  return (
    <SupabaseProvider>
      <AuthProviderDirect>
        <BusinessApprovalCheck>
          <UnifiedBusinessAdminLayout>{children}</UnifiedBusinessAdminLayout>
        </BusinessApprovalCheck>
      </AuthProviderDirect>
    </SupabaseProvider>
  )
}
