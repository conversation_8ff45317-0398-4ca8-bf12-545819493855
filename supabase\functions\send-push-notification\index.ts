import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface PushNotificationPayload {
  userId: string
  title: string
  body: string
  data?: {
    orderId?: string
    type?: string
    url?: string
    [key: string]: any
  }
  icon?: string
  badge?: string
  actions?: Array<{
    action: string
    title: string
    icon?: string
  }>
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Parse request body
    const payload: PushNotificationPayload = await req.json()
    
    if (!payload.userId || !payload.title || !payload.body) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields: userId, title, body' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get user's active push subscriptions
    const { data: subscriptions, error: subscriptionsError } = await supabase
      .from('push_subscriptions')
      .select('*')
      .eq('user_id', payload.userId)
      .eq('is_active', true)

    if (subscriptionsError) {
      console.error('Error fetching subscriptions:', subscriptionsError)
      return new Response(
        JSON.stringify({ error: 'Failed to fetch user subscriptions' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    if (!subscriptions || subscriptions.length === 0) {
      console.log(`No active subscriptions found for user ${payload.userId}`)
      return new Response(
        JSON.stringify({ message: 'No active subscriptions found', sent: 0 }),
        { 
          status: 200, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Prepare notification data
    const notificationData = {
      title: payload.title,
      body: payload.body,
      icon: payload.icon || '/android-chrome-192x192.png',
      badge: payload.badge || '/favicon-32x32.png',
      data: payload.data || {},
      actions: payload.actions || [],
      tag: payload.data?.orderId ? `order-${payload.data.orderId}` : undefined,
      renotify: true,
      requireInteraction: payload.data?.type === 'order_update'
    }

    let successCount = 0
    let failureCount = 0
    const results = []

    // Send push notification to each subscription
    for (const subscription of subscriptions) {
      try {
        // Use web-push library (you'll need to import this)
        // For now, we'll simulate the push notification
        const pushResult = await sendWebPushNotification(
          subscription.subscription_data,
          notificationData
        )

        if (pushResult.success) {
          successCount++
          
          // Log successful notification
          await supabase
            .from('notification_log')
            .insert({
              user_id: payload.userId,
              title: payload.title,
              body: payload.body,
              type: payload.data?.type || 'general',
              order_id: payload.data?.orderId ? parseInt(payload.data.orderId) : null,
              status: 'sent',
              sent_at: new Date().toISOString(),
              push_data: notificationData
            })

          results.push({ subscription_id: subscription.id, status: 'sent' })
        } else {
          failureCount++
          
          // Log failed notification
          await supabase
            .from('notification_log')
            .insert({
              user_id: payload.userId,
              title: payload.title,
              body: payload.body,
              type: payload.data?.type || 'general',
              order_id: payload.data?.orderId ? parseInt(payload.data.orderId) : null,
              status: 'failed',
              error_message: pushResult.error,
              push_data: notificationData
            })

          results.push({ 
            subscription_id: subscription.id, 
            status: 'failed', 
            error: pushResult.error 
          })
        }
      } catch (error) {
        failureCount++
        console.error(`Error sending to subscription ${subscription.id}:`, error)
        
        results.push({ 
          subscription_id: subscription.id, 
          status: 'failed', 
          error: error.message 
        })
      }
    }

    return new Response(
      JSON.stringify({
        message: 'Push notifications processed',
        sent: successCount,
        failed: failureCount,
        total: subscriptions.length,
        results
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error in push notification function:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

// Simulated web push function - replace with actual web-push implementation
async function sendWebPushNotification(subscription: any, payload: any) {
  try {
    // In a real implementation, you would use the web-push library here
    // For now, we'll simulate success
    console.log('Sending push notification:', { subscription, payload })
    
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 100))
    
    // Simulate 95% success rate
    if (Math.random() > 0.05) {
      return { success: true }
    } else {
      return { success: false, error: 'Simulated network error' }
    }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

/* 
To implement actual web-push, you would:

1. Install web-push in your Edge Function:
   import webpush from 'https://esm.sh/web-push@3.6.6'

2. Configure VAPID keys:
   webpush.setVapidDetails(
     'mailto:<EMAIL>',
     Deno.env.get('VAPID_PUBLIC_KEY')!,
     Deno.env.get('VAPID_PRIVATE_KEY')!
   )

3. Replace sendWebPushNotification with:
   async function sendWebPushNotification(subscription: any, payload: any) {
     try {
       await webpush.sendNotification(subscription, JSON.stringify(payload))
       return { success: true }
     } catch (error) {
       return { success: false, error: error.message }
     }
   }
*/
