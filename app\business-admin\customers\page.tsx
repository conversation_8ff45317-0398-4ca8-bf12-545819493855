"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import Link from "next/link"
import { format } from "date-fns"
import { useAuthDirect } from "@/context/auth-context-direct"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Calendar,
  ChevronDown,
  Download,
  Edit,
  ExternalLink,
  Filter,
  HelpCircle,
  Mail,
  MoreHorizontal,
  Phone,
  Plus,
  Search,
  ShoppingBag,
  SlidersHorizontal,
  UserCog,
  Users,
} from "lucide-react"

// Define types for our data
interface Customer {
  id: number
  name: string
  email: string
  phone: string
  address?: string
  avatar?: string
  customerSince: string
  totalOrders: number
  totalSpent: number
  lastOrder: string
  status: "active" | "inactive" | "new" | "at-risk"
  favoriteItems?: string[]
  notes?: string
}

interface BusinessData {
  id: number
  name: string
  business_type_id: number
  business_type?: string
  logo_url?: string | null
}

interface BusinessOption {
  id: number
  name: string
  business_type?: string
}

export default function BusinessAdminCustomersNew() {
  const router = useRouter()
  const { user, userProfile, isAdmin, isSuperAdmin } = useAuthDirect()
  const [business, setBusiness] = useState<BusinessData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isPendingApproval, setIsPendingApproval] = useState(false)
  const [activeTab, setActiveTab] = useState("all")
  const [availableBusinesses, setAvailableBusinesses] = useState<BusinessOption[]>([])
  const [selectedBusinessId, setSelectedBusinessId] = useState<number | null>(null)
  const [isAdminUser, setIsAdminUser] = useState(false)

  // Customers state
  const [customers, setCustomers] = useState<Customer[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [sortBy, setSortBy] = useState("recent")
  const [filteredCustomers, setFilteredCustomers] = useState<Customer[]>([])

  // Customer stats
  const [customerStats, setCustomerStats] = useState({
    total: 0,
    active: 0,
    averageOrderValue: 0,
    retentionRate: 0
  })

  // Check if user is admin or super admin
  useEffect(() => {
    if (isAdmin || isSuperAdmin) {
      setIsAdminUser(true)
    }
  }, [isAdmin, isSuperAdmin])

  // Define fetchBusinessData outside of useEffect so it can be called from multiple places
  const fetchBusinessData = async () => {
    try {
      console.log("Fetching business data...")
      setIsLoading(true)

      // Get the authentication token from localStorage
      const token = localStorage.getItem('loop_jersey_auth_token') || '';

      // Build the URL based on whether we're an admin user with a selected business
      let url = '/api/business-admin/business-data'

      // If admin user and a business is selected, add the business ID as a query parameter
      if (isAdminUser && selectedBusinessId) {
        url = `/api/business-admin/business-data?businessId=${selectedBusinessId}`
        console.log(`Admin user fetching data for business ID: ${selectedBusinessId}`)
      }

      const response = await fetch(url, {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      })

      if (!response.ok) {
        // If we get a 401 or 403, redirect to login
        if (response.status === 401 || response.status === 403) {
          console.log("Authentication error, redirecting to login")
          router.push("/login?redirectTo=/business-admin/customers-new")
          return
        }

        throw new Error(`API error: ${response.status}`)
      }

      const data = await response.json()
      console.log("Business data received:", data)

      if (data.business) {
        setBusiness({
          id: data.business.id,
          name: data.business.name,
          business_type_id: data.business.business_type_id,
          business_type: data.business.business_type
        })

        // Check if the business is pending approval
        setIsPendingApproval(data.business.is_approved === false)
      } else {
        setError("No business data found")
      }
    } catch (err) {
      console.error("Error fetching business data:", err)
      setError("An unexpected error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  // For admin users, fetch available businesses
  const fetchAvailableBusinesses = async () => {
    if (!isAdminUser) return

    try {
      console.log("Admin user detected, fetching available businesses")

      // Get the authentication token from localStorage
      const token = localStorage.getItem('loop_jersey_auth_token') || '';

      const response = await fetch('/api/admin/businesses-direct', {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      })

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`)
      }

      const data = await response.json()
      console.log("Available businesses:", data)

      if (data && Array.isArray(data)) {
        setAvailableBusinesses(data.map((b: any) => ({
          id: b.id,
          name: b.name,
          business_type: b.business_type || b.business_types?.name || "Business"
        })))
      }
    } catch (err) {
      console.error("Error fetching available businesses:", err)
    }
  }

  // Handle business selection change for admin users
  const handleBusinessChange = (businessId: number) => {
    console.log("Selected business changed to:", businessId)
    setSelectedBusinessId(businessId)

    // Refetch data with the new business ID
    fetchBusinessData()
    fetchCustomers()
  }

  // Fetch customers
  const fetchCustomers = async () => {
    try {
      setIsLoading(true)

      // In a real implementation, we would fetch customers from the API
      // For now, we'll use mock data

      // Mock customer data
      const mockCustomers: Customer[] = [
        {
          id: 1,
          name: "Sarah Johnson",
          email: "<EMAIL>",
          phone: "+44 7911 123456",
          address: "15 Beachfront, St Helier, JE2 3NG",
          avatar: "/thoughtful-brunette.png",
          customerSince: "June 2023",
          totalOrders: 12,
          totalSpent: 245.80,
          lastOrder: "2023-06-12T10:30:00",
          status: "active",
          favoriteItems: ["Jersey Crab Cakes", "Grilled Sea Bass"],
          notes: "Prefers extra sauce with crab cakes",
        },
        {
          id: 2,
          name: "Michael Brown",
          email: "<EMAIL>",
          phone: "+44 7911 234567",
          address: "42 Colomberie, St Helier, JE2 4QA",
          avatar: "/placeholder.svg",
          customerSince: "April 2023",
          totalOrders: 8,
          totalSpent: 178.50,
          lastOrder: "2023-06-10T12:45:00",
          status: "active",
          favoriteItems: ["Beef Wellington", "Chocolate Fondant"],
          notes: "Allergic to nuts",
        },
        {
          id: 3,
          name: "Emma Wilson",
          email: "<EMAIL>",
          phone: "+44 7911 345678",
          address: "7 Esplanade, St Helier, JE2 3QA",
          avatar: "/placeholder.svg",
          customerSince: "May 2023",
          totalOrders: 5,
          totalSpent: 120.25,
          lastOrder: "2023-06-05T18:20:00",
          status: "inactive",
          favoriteItems: ["Caesar Salad", "Margherita Pizza"],
          notes: "",
        },
        {
          id: 4,
          name: "David Taylor",
          email: "<EMAIL>",
          phone: "+44 7911 456789",
          address: "28 Halkett Place, St Helier, JE2 4WG",
          avatar: "/placeholder.svg",
          customerSince: "March 2023",
          totalOrders: 15,
          totalSpent: 320.75,
          lastOrder: "2023-06-11T19:15:00",
          status: "active",
          favoriteItems: ["Ribeye Steak", "Sticky Toffee Pudding"],
          notes: "Prefers medium-rare steak",
        },
        {
          id: 5,
          name: "Olivia Martin",
          email: "<EMAIL>",
          phone: "+44 7911 567890",
          address: "12 King Street, St Helier, JE2 4WE",
          avatar: "/placeholder.svg",
          customerSince: "June 2023",
          totalOrders: 3,
          totalSpent: 75.40,
          lastOrder: "2023-06-09T13:10:00",
          status: "new",
          favoriteItems: ["Vegetable Curry", "Naan Bread"],
          notes: "Vegetarian",
        },
        {
          id: 6,
          name: "James Anderson",
          email: "<EMAIL>",
          phone: "+44 7911 678901",
          address: "5 Broad Street, St Helier, JE2 3RR",
          avatar: "/placeholder.svg",
          customerSince: "February 2023",
          totalOrders: 10,
          totalSpent: 215.60,
          lastOrder: "2023-06-08T20:30:00",
          status: "active",
          favoriteItems: ["Fish & Chips", "Apple Crumble"],
          notes: "",
        },
        {
          id: 7,
          name: "Sophia White",
          email: "<EMAIL>",
          phone: "+44 7911 789012",
          address: "33 New Street, St Helier, JE2 3RA",
          avatar: "/placeholder.svg",
          customerSince: "April 2023",
          totalOrders: 7,
          totalSpent: 165.30,
          lastOrder: "2023-06-07T14:25:00",
          status: "active",
          favoriteItems: ["Sushi Platter", "Green Tea Ice Cream"],
          notes: "No wasabi",
        },
        {
          id: 8,
          name: "Daniel Clark",
          email: "<EMAIL>",
          phone: "+44 7911 890123",
          address: "19 Bath Street, St Helier, JE2 4SU",
          avatar: "/placeholder.svg",
          customerSince: "May 2023",
          totalOrders: 4,
          totalSpent: 95.20,
          lastOrder: "2023-05-28T17:40:00",
          status: "inactive",
          favoriteItems: ["Chicken Tikka Masala", "Garlic Naan"],
          notes: "Likes spicy food",
        },
        {
          id: 9,
          name: "Isabella Lewis",
          email: "<EMAIL>",
          phone: "+44 7911 901234",
          address: "8 Mulcaster Street, St Helier, JE2 3NJ",
          avatar: "/placeholder.svg",
          customerSince: "January 2023",
          totalOrders: 20,
          totalSpent: 450.15,
          lastOrder: "2023-06-12T12:15:00",
          status: "active",
          favoriteItems: ["Lobster Thermidor", "Crème Brûlée"],
          notes: "VIP customer",
        },
        {
          id: 10,
          name: "Thomas Wilson",
          email: "<EMAIL>",
          phone: "+44 7911 012345",
          address: "31 La Rue des Pres Trading Estate, St Saviour, JE2 7QN",
          avatar: "/placeholder.svg",
          customerSince: "March 2023",
          totalOrders: 1,
          totalSpent: 18.90,
          lastOrder: "2023-04-15T11:50:00",
          status: "at-risk",
          favoriteItems: ["Margherita Pizza"],
          notes: "",
        },
      ]

      setCustomers(mockCustomers)

      // Calculate customer stats
      const activeCustomers = mockCustomers.filter(c => c.status === "active").length
      const totalSpent = mockCustomers.reduce((sum, c) => sum + c.totalSpent, 0)
      const totalOrders = mockCustomers.reduce((sum, c) => sum + c.totalOrders, 0)
      const averageOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0

      setCustomerStats({
        total: mockCustomers.length,
        active: activeCustomers,
        averageOrderValue: averageOrderValue,
        retentionRate: 76 // Mock retention rate
      })

      // Apply initial filtering
      applyFilters(mockCustomers, searchQuery, statusFilter, sortBy)
    } catch (err) {
      console.error("Error fetching customers:", err)
      setError("Failed to load customers")
    } finally {
      setIsLoading(false)
    }
  }

  // Apply filters to customers
  const applyFilters = (customers: Customer[], search: string, status: string, sort: string) => {
    let filtered = [...customers]

    // Apply search filter
    if (search) {
      const searchLower = search.toLowerCase()
      filtered = filtered.filter(
        (customer) =>
          customer.name.toLowerCase().includes(searchLower) ||
          customer.email.toLowerCase().includes(searchLower) ||
          customer.phone.includes(searchLower) ||
          (customer.address && customer.address.toLowerCase().includes(searchLower))
      )
    }

    // Apply status filter
    if (status !== "all") {
      filtered = filtered.filter((customer) => customer.status === status)
    }

    // Apply sorting
    if (sort === "name-asc") {
      filtered.sort((a, b) => a.name.localeCompare(b.name))
    } else if (sort === "name-desc") {
      filtered.sort((a, b) => b.name.localeCompare(a.name))
    } else if (sort === "recent") {
      filtered.sort((a, b) => new Date(b.lastOrder).getTime() - new Date(a.lastOrder).getTime())
    } else if (sort === "oldest") {
      filtered.sort((a, b) => new Date(a.lastOrder).getTime() - new Date(b.lastOrder).getTime())
    } else if (sort === "spent-high") {
      filtered.sort((a, b) => b.totalSpent - a.totalSpent)
    } else if (sort === "spent-low") {
      filtered.sort((a, b) => a.totalSpent - b.totalSpent)
    } else if (sort === "orders-high") {
      filtered.sort((a, b) => b.totalOrders - a.totalOrders)
    } else if (sort === "orders-low") {
      filtered.sort((a, b) => a.totalOrders - b.totalOrders)
    }

    setFilteredCustomers(filtered)
  }

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value)
    setStatusFilter(value)
    applyFilters(customers, searchQuery, value, sortBy)
  }

  // Handle search
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value
    setSearchQuery(query)
    applyFilters(customers, query, statusFilter, sortBy)
  }

  // Handle sort change
  const handleSortChange = (value: string) => {
    setSortBy(value)
    applyFilters(customers, searchQuery, statusFilter, value)
  }

  // Initial data loading
  useEffect(() => {
    if (user) {
      fetchBusinessData()

      if (isAdminUser) {
        fetchAvailableBusinesses()
      }
    }
  }, [user, isAdminUser])

  // Fetch customers when dependencies change
  useEffect(() => {
    if (user && !isLoading) {
      fetchCustomers()
    }
  }, [user, selectedBusinessId])

  // Format currency helper
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
      minimumFractionDigits: 2
    }).format(amount)
  }

  // Format date helper
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString)
      return format(date, 'dd MMM yyyy')
    } catch (error) {
      return dateString
    }
  }

  // Format time helper
  const formatTime = (dateString: string) => {
    try {
      const date = new Date(dateString)
      return format(date, 'HH:mm')
    } catch (error) {
      return ""
    }
  }

  // Get status badge helper
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return (
          <Badge
            variant="outline"
            className="bg-emerald-50 text-emerald-700 hover:bg-emerald-100 hover:text-emerald-800"
          >
            Active
          </Badge>
        )
      case "inactive":
        return (
          <Badge
            variant="outline"
            className="bg-yellow-50 text-yellow-700 hover:bg-yellow-100 hover:text-yellow-800"
          >
            Inactive
          </Badge>
        )
      case "new":
        return (
          <Badge
            variant="outline"
            className="bg-blue-50 text-blue-700 hover:bg-blue-100 hover:text-blue-800"
          >
            New
          </Badge>
        )
      case "at-risk":
        return (
          <Badge
            variant="outline"
            className="bg-red-50 text-red-700 hover:bg-red-100 hover:text-red-800"
          >
            At Risk
          </Badge>
        )
      default:
        return (
          <Badge variant="outline">
            {status}
          </Badge>
        )
    }
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading customers...</p>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-red-500 text-5xl mb-4">⚠️</div>
          <h2 className="text-2xl font-bold mb-2">Error Loading Customers</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button onClick={() => fetchCustomers()}>Retry</Button>
        </div>
      </div>
    )
  }

  return (
    <TooltipProvider>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Customers</h1>
            <p className="text-muted-foreground">
              Manage your customer relationships and data
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" className="h-8">
              <Download className="mr-2 h-3.5 w-3.5" />
              Export
            </Button>
            <Button variant="outline" size="sm" className="h-8">
              <Mail className="mr-2 h-3.5 w-3.5" />
              Email All
            </Button>
            <Button size="sm" className="h-8 bg-emerald-600 hover:bg-emerald-700">
              <Plus className="mr-2 h-3.5 w-3.5" />
              Add Customer
            </Button>
          </div>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Customers</CardTitle>
              <Users className="h-4 w-4 text-emerald-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{customerStats.total}</div>
              <p className="text-xs text-muted-foreground">
                {customerStats.total > 0 ? `+${Math.floor(customerStats.total * 0.05)} new this month` : "No customers yet"}
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="flex items-center">
                <CardTitle className="text-sm font-medium">Active Customers</CardTitle>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="w-[200px] text-xs">Customers who have placed an order in the last 30 days</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                className="h-4 w-4 text-emerald-500"
              >
                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                <circle cx="9" cy="7" r="4" />
                <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                <path d="M16 3.13a4 4 0 0 1 0 7.75" />
              </svg>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{customerStats.active}</div>
              <p className="text-xs text-muted-foreground">
                {customerStats.total > 0 ? `${Math.round((customerStats.active / customerStats.total) * 100)}% of total customers` : "No active customers"}
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Order Value</CardTitle>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                className="h-4 w-4 text-emerald-500"
              >
                <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
              </svg>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(customerStats.averageOrderValue)}</div>
              <p className="text-xs text-muted-foreground">+£2.50 from last month</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Retention Rate</CardTitle>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                className="h-4 w-4 text-emerald-500"
              >
                <rect width="20" height="14" x="2" y="5" rx="2" />
                <path d="M2 10h20" />
              </svg>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{customerStats.retentionRate}%</div>
              <p className="text-xs text-muted-foreground">+2% from last month</p>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="all" className="space-y-4 mt-6" onValueChange={handleTabChange}>
          <div className="flex items-center justify-between">
            <TabsList>
              <TabsTrigger value="all">All Customers</TabsTrigger>
              <TabsTrigger value="active">Active</TabsTrigger>
              <TabsTrigger value="inactive">Inactive</TabsTrigger>
              <TabsTrigger value="new">New</TabsTrigger>
            </TabsList>
            <div className="flex items-center gap-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="h-8">
                    <Filter className="mr-2 h-3.5 w-3.5" />
                    Filter
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[200px]">
                  <DropdownMenuLabel>Filter by</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuCheckboxItem checked>Active Customers</DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem>Inactive Customers</DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem>New Customers</DropdownMenuCheckboxItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuLabel>Order Count</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuCheckboxItem checked>1+ Orders</DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem>5+ Orders</DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem>10+ Orders</DropdownMenuCheckboxItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="h-8">
                    <SlidersHorizontal className="mr-2 h-3.5 w-3.5" />
                    Sort
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[180px]">
                  <DropdownMenuLabel>Sort by</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => handleSortChange("name-asc")}>Name (A-Z)</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleSortChange("name-desc")}>Name (Z-A)</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleSortChange("recent")}>Last Order (Recent)</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleSortChange("oldest")}>Last Order (Oldest)</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleSortChange("spent-high")}>Total Spent (High to Low)</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleSortChange("spent-low")}>Total Spent (Low to High)</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleSortChange("orders-high")}>Order Count (High to Low)</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleSortChange("orders-low")}>Order Count (Low to High)</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search customers..."
                  className="w-64 rounded-lg bg-background pl-8 h-8"
                  value={searchQuery}
                  onChange={handleSearch}
                />
              </div>
            </div>
          </div>

          <TabsContent value="all" className="space-y-4">
            <Card>
              <CardHeader className="p-4">
                <div className="flex items-center justify-between">
                  <CardTitle>Customer Directory</CardTitle>
                  <CardDescription>
                    Showing {filteredCustomers.length} of {customerStats.total} customers
                  </CardDescription>
                </div>
              </CardHeader>
              <CardContent className="p-0 overflow-auto">
                <TooltipProvider>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[40px]">
                          <Checkbox />
                        </TableHead>
                        <TableHead className="min-w-[50px]">
                          <div className="flex items-center gap-1">
                            ID
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p className="w-[200px] text-xs">Unique identifier for the customer</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableHead>
                        <TableHead className="min-w-[150px]">
                          <div className="flex items-center gap-1">
                            Name
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p className="w-[200px] text-xs">Customer's full name</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableHead>
                        <TableHead className="min-w-[180px]">
                          <div className="flex items-center gap-1">
                            Email
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p className="w-[200px] text-xs">Customer's email address</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableHead>
                        <TableHead className="min-w-[120px]">
                          <div className="flex items-center gap-1">
                            Phone
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p className="w-[200px] text-xs">Customer's phone number</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableHead>
                        <TableHead className="min-w-[100px]">
                          <div className="flex items-center gap-1">
                            Status
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p className="w-[200px] text-xs">Current status of the customer</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableHead>
                        <TableHead className="min-w-[100px]">
                          <div className="flex items-center gap-1">
                            Orders
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p className="w-[200px] text-xs">Total number of orders placed</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableHead>
                        <TableHead className="min-w-[100px]">
                          <div className="flex items-center gap-1">
                            Total Spent
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p className="w-[200px] text-xs">Total amount spent by the customer</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableHead>
                        <TableHead className="min-w-[150px]">
                          <div className="flex items-center gap-1">
                            Last Order
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p className="w-[200px] text-xs">Date of the customer's most recent order</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredCustomers.length > 0 ? (
                        filteredCustomers.map((customer) => (
                          <TableRow key={customer.id}>
                            <TableCell>
                              <Checkbox />
                            </TableCell>
                            <TableCell className="font-medium">{customer.id}</TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Avatar className="h-8 w-8">
                                  <AvatarImage src={customer.avatar || "/placeholder.svg"} alt={customer.name} />
                                  <AvatarFallback>
                                    {customer.name
                                      .split(" ")
                                      .map((n) => n[0])
                                      .join("")}
                                  </AvatarFallback>
                                </Avatar>
                                <div className="font-medium">{customer.name}</div>
                              </div>
                            </TableCell>
                            <TableCell>{customer.email}</TableCell>
                            <TableCell>{customer.phone}</TableCell>
                            <TableCell>{getStatusBadge(customer.status)}</TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                <ShoppingBag className="mr-2 h-3.5 w-3.5 text-muted-foreground" />
                                {customer.totalOrders}
                              </div>
                            </TableCell>
                            <TableCell>{formatCurrency(customer.totalSpent)}</TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                <Calendar className="mr-2 h-3.5 w-3.5 text-muted-foreground" />
                                {formatDate(customer.lastOrder)}
                              </div>
                            </TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon">
                                    <MoreHorizontal className="h-4 w-4" />
                                    <span className="sr-only">Open menu</span>
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuItem>
                                    <Link href={`/business-admin/customers/${customer.id}`} className="flex items-center w-full">
                                      <ExternalLink className="mr-2 h-4 w-4" />
                                      View Details
                                    </Link>
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <UserCog className="mr-2 h-4 w-4" />
                                    Edit Customer
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <Mail className="mr-2 h-4 w-4" />
                                    Send Email
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <ShoppingBag className="mr-2 h-4 w-4" />
                                    View Orders
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem className="text-red-600">Delete Customer</DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={10} className="h-24 text-center">
                            No customers found.
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TooltipProvider>
              </CardContent>
              <CardFooter className="flex items-center justify-between p-4">
                <div className="text-xs text-muted-foreground">
                  Showing {filteredCustomers.length} of {customerStats.total} customers
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm" disabled={filteredCustomers.length === 0}>
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 w-8 p-0"
                    disabled={filteredCustomers.length === 0}
                  >
                    1
                  </Button>
                  <Button variant="outline" size="sm" disabled={filteredCustomers.length === 0}>
                    Next
                  </Button>
                </div>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="active" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Active Customers</CardTitle>
                <CardDescription>Customers who have placed an order in the last 30 days</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  {filteredCustomers.length > 0 ? (
                    <p>Showing only active customers. Use the filter options above to refine your view.</p>
                  ) : (
                    <p>No active customers found.</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="inactive" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Inactive Customers</CardTitle>
                <CardDescription>Customers who haven't placed an order in the last 30 days</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  {filteredCustomers.length > 0 ? (
                    <p>Showing only inactive customers. Use the filter options above to refine your view.</p>
                  ) : (
                    <p>No inactive customers found.</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="new" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>New Customers</CardTitle>
                <CardDescription>Customers who registered in the last 7 days</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  {filteredCustomers.length > 0 ? (
                    <p>Showing only new customers. Use the filter options above to refine your view.</p>
                  ) : (
                    <p>No new customers found.</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </TooltipProvider>
  )
}
