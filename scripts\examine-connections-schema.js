#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to examine the detailed schema of connections-related database tables
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function examineTableSchemas() {
  console.log('🔍 Examining Connections Hub Table Schemas\n');

  const tables = [
    'connections',
    'communications', 
    'connection_profiles',
    'communication_attachments'
  ];

  for (const tableName of tables) {
    await examineTableSchema(tableName);
  }

  // Also check for any existing users and businesses tables that connections might reference
  await checkReferencedTables();
}

async function examineTableSchema(tableName) {
  console.log(`📋 ${tableName.toUpperCase()} TABLE SCHEMA`);
  console.log('='.repeat(60));

  try {
    // Query information_schema to get column details
    const { data: columns, error } = await supabase.rpc('get_table_schema', {
      table_name: tableName
    });

    if (error) {
      // Fallback: try to get schema info using a different approach
      console.log(`⚠️  Could not get detailed schema (${error.message})`);
      console.log('Attempting to describe table structure...\n');
      
      // Try to insert a test record to see what columns are expected
      const { error: insertError } = await supabase
        .from(tableName)
        .insert({})
        .select();
      
      if (insertError) {
        console.log(`Schema hints from insert error: ${insertError.message}`);
      }
    } else if (columns) {
      console.log('Columns:');
      columns.forEach(col => {
        console.log(`  ${col.column_name}: ${col.data_type}${col.is_nullable === 'NO' ? ' NOT NULL' : ''}`);
      });
    }

    // Try to get constraints and indexes
    await getTableConstraints(tableName);

  } catch (error) {
    console.log(`❌ Error examining ${tableName}: ${error.message}`);
  }
  
  console.log('\n');
}

async function getTableConstraints(tableName) {
  try {
    // Try to get constraint information
    const { data: constraints, error } = await supabase.rpc('get_table_constraints', {
      table_name: tableName
    });

    if (!error && constraints && constraints.length > 0) {
      console.log('\nConstraints:');
      constraints.forEach(constraint => {
        console.log(`  ${constraint.constraint_name}: ${constraint.constraint_type}`);
      });
    }
  } catch (error) {
    // Constraints query failed, that's okay
  }
}

async function checkReferencedTables() {
  console.log('🔗 CHECKING REFERENCED TABLES');
  console.log('='.repeat(60));

  const referencedTables = [
    'auth.users',
    'orders', 
    'businesses'
  ];

  for (const tableName of referencedTables) {
    try {
      const { data, error } = await supabase
        .from(tableName.replace('auth.', ''))
        .select('*')
        .limit(1);
      
      if (error) {
        console.log(`❌ Table '${tableName}': ${error.message}`);
      } else {
        console.log(`✅ Table '${tableName}': EXISTS (${data?.length || 0} sample records)`);
        if (data && data.length > 0) {
          console.log(`   Sample columns: ${Object.keys(data[0]).join(', ')}`);
        }
      }
    } catch (err) {
      console.log(`❌ Table '${tableName}': ERROR - ${err.message}`);
    }
  }

  console.log('\n');
}

// Create helper functions for schema queries (these would need to be created in Supabase)
async function createSchemaHelperFunctions() {
  console.log('📝 Creating schema helper functions...');

  // Function to get table schema
  const schemaFunction = `
    CREATE OR REPLACE FUNCTION get_table_schema(table_name text)
    RETURNS TABLE(
      column_name text,
      data_type text,
      is_nullable text,
      column_default text
    )
    LANGUAGE sql
    AS $$
      SELECT 
        c.column_name::text,
        c.data_type::text,
        c.is_nullable::text,
        c.column_default::text
      FROM information_schema.columns c
      WHERE c.table_name = $1
        AND c.table_schema = 'public'
      ORDER BY c.ordinal_position;
    $$;
  `;

  // Function to get table constraints
  const constraintsFunction = `
    CREATE OR REPLACE FUNCTION get_table_constraints(table_name text)
    RETURNS TABLE(
      constraint_name text,
      constraint_type text
    )
    LANGUAGE sql
    AS $$
      SELECT 
        tc.constraint_name::text,
        tc.constraint_type::text
      FROM information_schema.table_constraints tc
      WHERE tc.table_name = $1
        AND tc.table_schema = 'public';
    $$;
  `;

  try {
    // Note: These would need to be executed in Supabase SQL editor
    console.log('⚠️  Helper functions need to be created in Supabase SQL editor:');
    console.log('1. get_table_schema function');
    console.log('2. get_table_constraints function');
  } catch (error) {
    console.log(`❌ Error creating helper functions: ${error.message}`);
  }
}

// Alternative approach: examine table structure by attempting operations
async function examineTableStructureByOperation(tableName) {
  console.log(`🔍 Examining ${tableName} structure through operations...`);

  try {
    // Try to select with specific columns based on our migration file
    let selectColumns = [];
    
    switch (tableName) {
      case 'connections':
        selectColumns = [
          'id', 'user1_id', 'user2_id', 'connection_type', 'status',
          'user1_favorite', 'user2_favorite', 'created_by', 'notes',
          'created_at', 'updated_at'
        ];
        break;
      case 'communications':
        selectColumns = [
          'id', 'sender_id', 'recipient_id', 'connection_id', 'order_id',
          'business_id', 'channel_type', 'message_type', 'subject', 'content',
          'thread_id', 'parent_message_id', 'is_read', 'is_urgent', 'is_automated',
          'priority', 'created_at', 'read_at', 'expires_at'
        ];
        break;
      case 'connection_profiles':
        selectColumns = [
          'id', 'user_id', 'profile_type', 'display_name', 'bio', 'avatar_url',
          'communication_preferences', 'availability_info', 'specialties',
          'average_rating', 'total_ratings', 'connection_count',
          'is_public', 'allow_direct_messages', 'created_at', 'updated_at'
        ];
        break;
      case 'communication_attachments':
        selectColumns = [
          'id', 'communication_id', 'file_name', 'file_type', 'file_size',
          'file_url', 'uploaded_by', 'created_at'
        ];
        break;
    }

    if (selectColumns.length > 0) {
      const { data, error } = await supabase
        .from(tableName)
        .select(selectColumns.join(', '))
        .limit(1);

      if (error) {
        console.log(`❌ Error selecting from ${tableName}: ${error.message}`);
        // Try to identify which columns might be missing
        for (const col of selectColumns) {
          const { error: colError } = await supabase
            .from(tableName)
            .select(col)
            .limit(1);
          
          if (colError) {
            console.log(`   ❌ Column '${col}': ${colError.message}`);
          } else {
            console.log(`   ✅ Column '${col}': EXISTS`);
          }
        }
      } else {
        console.log(`✅ All expected columns exist in ${tableName}`);
        console.log(`   Columns: ${selectColumns.join(', ')}`);
      }
    }

  } catch (error) {
    console.log(`❌ Error examining ${tableName} structure: ${error.message}`);
  }
}

// Run the examination
examineTableSchemas()
  .then(() => {
    console.log('✅ Schema examination completed');
    
    // Also run the operational examination
    console.log('\n🔧 Running operational structure examination...\n');
    return Promise.all([
      'connections',
      'communications', 
      'connection_profiles',
      'communication_attachments'
    ].map(examineTableStructureByOperation));
  })
  .then(() => {
    console.log('\n✅ All examinations completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Fatal error:', error);
    process.exit(1);
  });
