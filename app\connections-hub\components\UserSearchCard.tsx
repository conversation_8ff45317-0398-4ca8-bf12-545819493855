"use client"

import { useState } from 'react'
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  Star, 
  UserPlus, 
  Building2,
  Truck,
  ShoppingBag,
  MapPin,
  Check,
  Clock,
  X
} from "lucide-react"
import { UserProfile, UserRole } from '../types'
import { getRoleColor, getRoleBadgeVariant } from '../mock-data'

interface UserSearchCardProps {
  user: UserProfile
  connectionStatus?: 'none' | 'pending' | 'active' | 'blocked'
  onConnect?: (userId: string, connectionType: string) => void
  onViewProfile?: (userId: string) => void
}

export function UserSearchCard({ 
  user, 
  connectionStatus = 'none',
  onConnect,
  onViewProfile 
}: UserSearchCardProps) {
  const [isLoading, setIsLoading] = useState(false)

  // Determine primary role for display
  const getPrimaryRole = (): UserRole => {
    if (user.role_capabilities.owns_business) return 'business'
    if (user.role_capabilities.can_be_rider) return 'rider'
    return 'customer'
  }

  const primaryRole = getPrimaryRole()
  const roleColor = getRoleColor(primaryRole)
  const roleBadgeVariant = getRoleBadgeVariant(primaryRole)

  const getRoleIcon = (role: UserRole) => {
    switch (role) {
      case 'business': return <Building2 className="h-4 w-4" />
      case 'rider': return <Truck className="h-4 w-4" />
      case 'customer': return <ShoppingBag className="h-4 w-4" />
      default: return null
    }
  }

  const getConnectionType = (): string => {
    // This would be determined by current user's role and target user's role
    // For now, defaulting to customer-business
    if (primaryRole === 'business') return 'customer-business'
    if (primaryRole === 'rider') return 'customer-rider'
    return 'customer-business'
  }

  const handleConnect = async () => {
    setIsLoading(true)
    try {
      const connectionType = getConnectionType()
      await onConnect?.(user.user_id, connectionType)
    } catch (error) {
      console.error('Failed to connect:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getSpecialtyDisplay = () => {
    if (!user.specialties) return null
    
    switch (primaryRole) {
      case 'business':
        return user.specialties.cuisine_types?.slice(0, 2).join(', ')
      case 'rider':
        return `${user.specialties.vehicle_type} • ${user.specialties.areas?.slice(0, 2).join(', ')}`
      case 'customer':
        return user.specialties.favorite_cuisines?.slice(0, 2).join(', ')
      default:
        return null
    }
  }

  const getConnectionStatusDisplay = () => {
    switch (connectionStatus) {
      case 'pending':
        return (
          <div className="flex items-center gap-1 text-xs text-amber-600">
            <Clock className="h-3 w-3" />
            <span>Pending</span>
          </div>
        )
      case 'active':
        return (
          <div className="flex items-center gap-1 text-xs text-green-600">
            <Check className="h-3 w-3" />
            <span>Connected</span>
          </div>
        )
      case 'blocked':
        return (
          <div className="flex items-center gap-1 text-xs text-red-600">
            <X className="h-3 w-3" />
            <span>Blocked</span>
          </div>
        )
      default:
        return null
    }
  }

  const getAvailableRoles = () => {
    const roles: UserRole[] = []
    if (user.role_capabilities.can_be_customer) roles.push('customer')
    if (user.role_capabilities.can_be_rider) roles.push('rider')
    if (user.role_capabilities.owns_business) roles.push('business')
    return roles
  }

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <Avatar className="h-12 w-12">
              <AvatarImage src={user.avatar_url} alt={user.display_name} />
              <AvatarFallback className={`bg-${roleColor}-100 text-${roleColor}-700`}>
                {user.display_name.split(' ').map(n => n[0]).join('').toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-semibold text-sm truncate">{user.display_name}</h3>
                <Badge variant={roleBadgeVariant} className="text-xs">
                  <span className="flex items-center gap-1">
                    {getRoleIcon(primaryRole)}
                    {primaryRole}
                  </span>
                </Badge>
              </div>
              {user.average_rating && (
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                  <span>{user.average_rating.toFixed(1)}</span>
                  <span>({user.total_ratings} reviews)</span>
                </div>
              )}
            </div>
          </div>
          {getConnectionStatusDisplay()}
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        {user.bio && (
          <p className="text-xs text-muted-foreground mb-3 line-clamp-2">
            {user.bio}
          </p>
        )}
        
        {getSpecialtyDisplay() && (
          <div className="flex items-center gap-1 text-xs text-muted-foreground mb-3">
            <MapPin className="h-3 w-3" />
            <span className="truncate">{getSpecialtyDisplay()}</span>
          </div>
        )}

        {/* Show multiple roles if user has them */}
        {getAvailableRoles().length > 1 && (
          <div className="flex gap-1 mb-3">
            <span className="text-xs text-muted-foreground">Also:</span>
            {getAvailableRoles()
              .filter(role => role !== primaryRole)
              .map(role => (
                <Badge key={role} variant="outline" className="text-xs">
                  <span className="flex items-center gap-1">
                    {getRoleIcon(role)}
                    {role}
                  </span>
                </Badge>
              ))
            }
          </div>
        )}

        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onViewProfile?.(user.user_id)}
            className="flex-1 text-xs"
          >
            View Profile
          </Button>
          
          {connectionStatus === 'none' && (
            <Button
              size="sm"
              onClick={handleConnect}
              disabled={isLoading}
              className="flex-1 text-xs bg-emerald-600 hover:bg-emerald-700"
            >
              <UserPlus className="h-3 w-3 mr-1" />
              {isLoading ? 'Connecting...' : 'Connect'}
            </Button>
          )}
          
          {connectionStatus === 'active' && (
            <Button
              size="sm"
              disabled
              className="flex-1 text-xs"
            >
              <Check className="h-3 w-3 mr-1" />
              Connected
            </Button>
          )}
          
          {connectionStatus === 'pending' && (
            <Button
              size="sm"
              disabled
              variant="outline"
              className="flex-1 text-xs"
            >
              <Clock className="h-3 w-3 mr-1" />
              Pending
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
