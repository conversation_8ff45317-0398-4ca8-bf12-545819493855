import { NextResponse } from "next/server"
import { cookies } from "next/headers"
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs"
import { createClient } from "@supabase/supabase-js"
import { updateOrderStatus } from "@/app/api/orders/transaction-helper"

// Get Supabase credentials from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET(request: Request) {
  try {
    // First, verify the user is authenticated and has business manager permissions
    const cookieStore = cookies()
    const authClient = createServerComponentClient({ cookies: () => cookieStore })
    const adminClient = createClient(supabaseUrl, supabaseServiceKey)

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession()

    // Check for custom token in cookies or headers
    const customToken = cookieStore.get('loop_jersey_auth_token')?.value;
    const userEmailCookie = cookieStore.get('loop_jersey_user_email')?.value;

    // Check for token in Authorization header
    const authHeader = request.headers.get('Authorization');
    const headerToken = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null;

    console.log("Auth check result:", {
      hasSession: !!session,
      hasCustomToken: !!customToken,
      hasHeaderToken: !!headerToken,
      userEmailCookie: userEmailCookie || null,
      sessionError: sessionError ? sessionError.message : null,
      userEmail: session?.user?.email || null
    })

    // If no session, try to use custom token
    let userEmail = session?.user?.email;

    if (!userEmail && userEmailCookie) {
      userEmail = decodeURIComponent(userEmailCookie);
      console.log("Using email from cookie:", userEmail);
    }

    if (!userEmail && !customToken && !headerToken) {
      console.error("No authentication found")
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Get the user's profile to check their role
    console.log("Checking user profile for email:", userEmail)

    // Get the user profile with role using admin client to bypass RLS
    const { data: userProfile, error: profileError } = await adminClient
      .from("users")
      .select("id, role, auth_id")
      .eq("email", userEmail)
      .single()

    console.log("Admin client profile check:", {
      hasProfile: !!userProfile,
      role: userProfile?.role || null,
      error: profileError ? profileError.message : null
    })

    if (profileError || !userProfile) {
      console.error("Failed to get user profile:", profileError)
      return NextResponse.json(
        { error: "User profile not found" },
        { status: 403 }
      )
    }

    console.log("Found user profile with role:", userProfile.role)

    // Check if user is a business manager, admin, or super_admin
    const isBusinessManager = userProfile.role === "business_manager"
    const isBusinessStaff = userProfile.role === "business_staff"
    const isAdmin = userProfile.role === "admin"
    const isSuperAdmin = userProfile.role === "super_admin"

    if (!isBusinessManager && !isBusinessStaff && !isAdmin && !isSuperAdmin) {
      console.error("User does not have business manager permissions:", userProfile.role)
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    console.log("User has appropriate permissions:", userProfile.role)

    // Parse query parameters
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get("page") || "1")
    const pageSize = parseInt(url.searchParams.get("pageSize") || "20")
    const status = url.searchParams.get("status") || null
    const search = url.searchParams.get("search") || null
    const sortBy = url.searchParams.get("sortBy") || "created_at"
    const sortOrder = url.searchParams.get("sortOrder") || "desc"
    const startDate = url.searchParams.get("startDate") || null
    const endDate = url.searchParams.get("endDate") || null

    console.log("Date filter parameters:", { startDate, endDate })

    // For admin users, return all orders or filter by business if specified
    if (isAdmin || isSuperAdmin) {
      console.log("Admin user detected, returning orders")

      // Check if a specific business ID was requested
      const businessId = url.searchParams.get("businessId")
      if (businessId) {
        console.log(`Admin user filtering by business ID: ${businessId}`)
      } else {
        console.log("No business ID provided, returning orders for all businesses")
      }

      // Log all query parameters for debugging
      console.log("Query parameters:", Object.fromEntries(url.searchParams.entries()))

      // Check if we should use the orders_view
      let query = adminClient
        .from("orders_view")
        .select("*", { count: "exact" })

      // Apply business filter if provided
      if (businessId) {
        console.log(`Applying business filter with ID: ${businessId} (type: ${typeof businessId})`)
        // Convert to number to ensure proper comparison
        const businessIdNum = parseInt(businessId)
        if (!isNaN(businessIdNum)) {
          console.log(`Converted business ID to number: ${businessIdNum}`)
          query = query.eq("business_id", businessIdNum)

          // Log the SQL query that would be executed (for debugging)
          const { data: debugData, error: debugError, count: debugCount } = await adminClient
            .from("orders_view")
            .select("id, business_id", { count: "exact" })
            .eq("business_id", businessIdNum)
            .limit(5)

          console.log("Debug query results:", {
            count: debugCount,
            error: debugError ? debugError.message : null,
            sampleData: debugData ? debugData.slice(0, 2) : null
          })
        } else {
          console.log(`Using business ID as string: ${businessId}`)
          query = query.eq("business_id", businessId)
        }
      } else {
        console.log("No business filter applied")
      }

      // Apply filters
      if (status) {
        query = query.eq("status", status)
      }

      if (search) {
        query = query.or(`order_id.ilike.%${search}%, customer_name.ilike.%${search}%`)
      }

      // Apply date range filters if provided
      if (startDate) {
        console.log("Applying start date filter:", startDate)
        // Convert to date object and format as ISO string for proper comparison
        const startDateObj = new Date(startDate)
        query = query.gte("created_at", startDateObj.toISOString())
      }

      if (endDate) {
        console.log("Applying end date filter:", endDate)
        // Convert to date object and format as ISO string for proper comparison
        const endDateObj = new Date(endDate)
        query = query.lte("created_at", endDateObj.toISOString())
      }

      // Apply sorting
      query = query.order(sortBy, { ascending: sortOrder === "asc" })

      // Apply pagination
      query = query.range((page - 1) * pageSize, page * pageSize - 1)

      const { data: orders, error: ordersError, count } = await query

      if (ordersError) {
        console.error("Error fetching orders:", ordersError)

        // If the view doesn't exist, fall back to the orders table
        console.log("Falling back to orders table")

        const fallbackQuery = adminClient
          .from("orders")
          .select(`
            *,
            users(name, email, phone)
          `, { count: "exact" })

        // Apply business filter if provided
        const businessId = url.searchParams.get("businessId")
        if (businessId) {
          console.log(`Fallback: Applying business filter with ID: ${businessId} (type: ${typeof businessId})`)
          // Convert to number to ensure proper comparison
          const businessIdNum = parseInt(businessId)
          if (!isNaN(businessIdNum)) {
            console.log(`Fallback: Converted business ID to number: ${businessIdNum}`)
            fallbackQuery.eq("business_id", businessIdNum)
          } else {
            console.log(`Fallback: Using business ID as string: ${businessId}`)
            fallbackQuery.eq("business_id", businessId)
          }
        } else {
          console.log("Fallback: No business filter applied")
        }

        if (status) {
          fallbackQuery.eq("status", status)
        }

        if (search) {
          fallbackQuery.or(`order_id.ilike.%${search}%`)
        }

        // Apply date range filters if provided
        if (startDate) {
          const startDateObj = new Date(startDate)
          fallbackQuery.gte("created_at", startDateObj.toISOString())
        }

        if (endDate) {
          const endDateObj = new Date(endDate)
          fallbackQuery.lte("created_at", endDateObj.toISOString())
        }

        fallbackQuery.order(sortBy, { ascending: sortOrder === "asc" })
        fallbackQuery.range((page - 1) * pageSize, page * pageSize - 1)

        const { data: fallbackOrders, error: fallbackError, count: fallbackCount } = await fallbackQuery

        if (fallbackError) {
          console.error("Error fetching orders from fallback:", fallbackError)
          return NextResponse.json(
            { error: "Failed to fetch orders" },
            { status: 500 }
          )
        }

        // Format the fallback orders
        const formattedOrders = await Promise.all(fallbackOrders.map(async order => {
          // Get order items count and total quantity
          const { data: items, error: itemsError } = await adminClient
            .from("order_items")
            .select(`
              id,
              quantity,
              price,
              unit_price,
              total_price,
              product_name,
              products(name, price)
            `)
            .eq("order_id", order.id);

          const itemsCount = items?.length || 0;
          const totalItems = items?.reduce((sum, item) => sum + (item.quantity || 0), 0) || 0;

          return {
            id: order.id,
            order_id: order.order_id || `ORD-${order.id}`,
            order_number: order.order_number || order.order_id || `ORD-${order.id}`,
            status: order.status,
            total: order.total || 0,
            subtotal: order.subtotal || 0,
            delivery_fee: order.delivery_fee || 0,
            service_fee: order.service_fee || 0,
            created_at: order.created_at,
            updated_at: order.updated_at,

            // Customer information from orders table
            customer_name: order.customer_name || order.users?.name || "Guest",
            customer_email: order.customer_email || order.users?.email || null,
            customer_phone: order.customer_phone || order.users?.phone || null,
            customer_address: order.customer_address || null,

            // Delivery information
            delivery_address: order.delivery_address || order.customer_address || null,
            delivery_type: order.delivery_type || "delivery",
            delivery_instructions: order.delivery_instructions || order.notes || null,

            // Timing information
            preparation_time: order.preparation_time || null,
            estimated_delivery_time: order.estimated_delivery_time || null,
            scheduled_time: order.scheduled_time || null,
            asap: order.asap !== undefined ? order.asap : true,

            // Items information
            items_count: itemsCount,
            total_items: totalItems,

            // Business information
            business_id: order.business_id,
            business_name: order.business_name || null,
            business_type: order.business_type || null,

            // Additional fields
            payment_method: order.payment_method || null,
            payment_status: order.payment_status || null,
            driver_id: order.driver_id || null,
            priority_level: order.priority_level || null
          };
        }))

        return NextResponse.json({
          orders: formattedOrders,
          total: fallbackCount || 0,
          page,
          pageSize,
          totalPages: Math.ceil((fallbackCount || 0) / pageSize)
        })
      }

      // Log the first few orders for debugging
      if (orders && orders.length > 0) {
        console.log("First order from database:", {
          id: orders[0].id,
          business_id: orders[0].business_id,
          business_id_type: typeof orders[0].business_id,
          order_id: orders[0].order_id,
          status: orders[0].status
        })

        // If a business ID filter was applied, verify it worked
        if (businessId) {
          const businessIdNum = parseInt(businessId)
          const matchingOrders = orders.filter(order =>
            order.business_id === businessIdNum ||
            order.business_id === businessId
          )
          console.log(`Found ${matchingOrders.length} orders matching business ID ${businessId} out of ${orders.length} total orders`)
        }
      }

      return NextResponse.json({
        orders,
        total: count || 0,
        page,
        pageSize,
        totalPages: Math.ceil((count || 0) / pageSize),
        // Include the applied filters in the response for debugging
        debug: {
          appliedFilters: {
            businessId: businessId || null,
            status: status || null,
            search: search || null,
            dateRange: { startDate, endDate }
          }
        }
      })
    }

    // For business users, get the business ID first
    const { data: managerData, error: managerError } = await adminClient
      .from("business_managers")
      .select("business_id")
      .eq("user_id", userProfile.id)
      .single()

    if (managerError) {
      console.error("Error fetching business manager data:", managerError)

      // Try business_staff table if not found in business_managers
      const { data: staffData, error: staffError } = await adminClient
        .from("business_staff")
        .select("business_id")
        .eq("user_id", userProfile.id)
        .single()

      if (staffError) {
        console.error("Error fetching business staff data:", staffError)
        return NextResponse.json(
          { error: "Business association not found" },
          { status: 404 }
        )
      }

      if (!staffData || !staffData.business_id) {
        console.error("No business ID found for this staff")
        return NextResponse.json(
          { error: "No business found for this user" },
          { status: 404 }
        )
      }

      // Continue with the staff's business ID
      const businessId = staffData.business_id
      console.log("Found business ID for staff:", businessId)

      // Try to use the orders_view first
      let query = adminClient
        .from("orders_view")
        .select("*", { count: "exact" })
        .eq("business_id", businessId)

      // Apply filters
      if (status) {
        query = query.eq("status", status)
      }

      if (search) {
        query = query.or(`order_id.ilike.%${search}%, customer_name.ilike.%${search}%`)
      }

      // Apply date range filters if provided
      if (startDate) {
        const startDateObj = new Date(startDate)
        query = query.gte("created_at", startDateObj.toISOString())
      }

      if (endDate) {
        const endDateObj = new Date(endDate)
        query = query.lte("created_at", endDateObj.toISOString())
      }

      // Apply sorting
      query = query.order(sortBy, { ascending: sortOrder === "asc" })

      // Apply pagination
      query = query.range((page - 1) * pageSize, page * pageSize - 1)

      const { data: orders, error: ordersError, count } = await query

      if (ordersError) {
        console.error("Error fetching orders from view:", ordersError)

        // If the view doesn't exist, fall back to the orders table
        console.log("Falling back to orders table")

        const fallbackQuery = adminClient
          .from("orders")
          .select(`
            *,
            users(name, email, phone)
          `, { count: "exact" })
          .eq("business_id", businessId)

        if (status) {
          fallbackQuery.eq("status", status)
        }

        if (search) {
          fallbackQuery.or(`order_id.ilike.%${search}%`)
        }

        // Apply date range filters if provided
        if (startDate) {
          const startDateObj = new Date(startDate)
          fallbackQuery.gte("created_at", startDateObj.toISOString())
        }

        if (endDate) {
          const endDateObj = new Date(endDate)
          fallbackQuery.lte("created_at", endDateObj.toISOString())
        }

        fallbackQuery.order(sortBy, { ascending: sortOrder === "asc" })
        fallbackQuery.range((page - 1) * pageSize, page * pageSize - 1)

        const { data: fallbackOrders, error: fallbackError, count: fallbackCount } = await fallbackQuery

        if (fallbackError) {
          console.error("Error fetching orders from fallback:", fallbackError)
          return NextResponse.json(
            { error: "Failed to fetch orders" },
            { status: 500 }
          )
        }

        // Get order items for each order
        const ordersWithItems = await Promise.all(
          fallbackOrders.map(async (order) => {
            const { data: items, error: itemsError } = await adminClient
              .from("order_items")
              .select(`
                id,
                quantity,
                price,
                unit_price,
                total_price,
                product_name,
                products(name, price)
              `)
              .eq("order_id", order.id)

            const itemsCount = items?.length || 0;
            const totalItems = items?.reduce((sum, item) => sum + (item.quantity || 0), 0) || 0;

            return {
              id: order.id,
              order_id: order.order_id || `ORD-${order.id}`,
              order_number: order.order_number || order.order_id || `ORD-${order.id}`,
              status: order.status,
              total: order.total || 0,
              subtotal: order.subtotal || 0,
              delivery_fee: order.delivery_fee || 0,
              service_fee: order.service_fee || 0,
              created_at: order.created_at,
              updated_at: order.updated_at,

              // Customer information from orders table
              customer_name: order.customer_name || order.users?.name || "Guest",
              customer_email: order.customer_email || order.users?.email || null,
              customer_phone: order.customer_phone || order.users?.phone || null,
              customer_address: order.customer_address || null,

              // Delivery information
              delivery_address: order.delivery_address || order.customer_address || null,
              delivery_type: order.delivery_type || "delivery",
              delivery_instructions: order.delivery_instructions || order.notes || null,

              // Timing information
              preparation_time: order.preparation_time || null,
              estimated_delivery_time: order.estimated_delivery_time || null,
              scheduled_time: order.scheduled_time || null,
              asap: order.asap !== undefined ? order.asap : true,

              // Items information
              items_count: itemsCount,
              total_items: totalItems,

              // Business information
              business_id: order.business_id,
              business_name: order.business_name || null,
              business_type: order.business_type || null,

              // Additional fields
              payment_method: order.payment_method || null,
              payment_status: order.payment_status || null,
              driver_id: order.driver_id || null,
              priority_level: order.priority_level || null
            }
          })
        )

        return NextResponse.json({
          orders: ordersWithItems,
          total: fallbackCount || 0,
          page,
          pageSize,
          totalPages: Math.ceil((fallbackCount || 0) / pageSize)
        })
      }

      return NextResponse.json({
        orders,
        total: count || 0,
        page,
        pageSize,
        totalPages: Math.ceil((count || 0) / pageSize)
      })
    }

    // Continue with the manager's business ID
    const businessId = managerData.business_id
    console.log("Found business ID for manager:", businessId)

    // Try to use the orders_view first
    let query = adminClient
      .from("orders_view")
      .select("*", { count: "exact" })
      .eq("business_id", businessId)

    // Apply filters
    if (status) {
      query = query.eq("status", status)
    }

    if (search) {
      query = query.or(`order_id.ilike.%${search}%, customer_name.ilike.%${search}%`)
    }

    // Apply date range filters if provided
    if (startDate) {
      const startDateObj = new Date(startDate)
      query = query.gte("created_at", startDateObj.toISOString())
    }

    if (endDate) {
      const endDateObj = new Date(endDate)
      query = query.lte("created_at", endDateObj.toISOString())
    }

    // Apply sorting
    query = query.order(sortBy, { ascending: sortOrder === "asc" })

    // Apply pagination
    query = query.range((page - 1) * pageSize, page * pageSize - 1)

    const { data: orders, error: ordersError, count } = await query

    if (ordersError) {
      console.error("Error fetching orders from view:", ordersError)

      // If the view doesn't exist, fall back to the orders table
      console.log("Falling back to orders table")

      const fallbackQuery = adminClient
        .from("orders")
        .select(`
          *,
          users(name, email, phone)
        `, { count: "exact" })
        .eq("business_id", businessId)

      if (status) {
        fallbackQuery.eq("status", status)
      }

      if (search) {
        fallbackQuery.or(`order_id.ilike.%${search}%`)
      }

      // Apply date range filters if provided
      if (startDate) {
        const startDateObj = new Date(startDate)
        fallbackQuery.gte("created_at", startDateObj.toISOString())
      }

      if (endDate) {
        const endDateObj = new Date(endDate)
        fallbackQuery.lte("created_at", endDateObj.toISOString())
      }

      fallbackQuery.order(sortBy, { ascending: sortOrder === "asc" })
      fallbackQuery.range((page - 1) * pageSize, page * pageSize - 1)

      const { data: fallbackOrders, error: fallbackError, count: fallbackCount } = await fallbackQuery

      if (fallbackError) {
        console.error("Error fetching orders from fallback:", fallbackError)
        return NextResponse.json(
          { error: "Failed to fetch orders" },
          { status: 500 }
        )
      }

      // Get order items for each order
      const ordersWithItems = await Promise.all(
        fallbackOrders.map(async (order) => {
          const { data: items, error: itemsError } = await adminClient
            .from("order_items")
            .select(`
              id,
              quantity,
              price,
              unit_price,
              total_price,
              product_name,
              products(name, price)
            `)
            .eq("order_id", order.id)

          const itemsCount = items?.length || 0;
          const totalItems = items?.reduce((sum, item) => sum + (item.quantity || 0), 0) || 0;

          return {
            id: order.id,
            order_id: order.order_id || `ORD-${order.id}`,
            order_number: order.order_number || order.order_id || `ORD-${order.id}`,
            status: order.status,
            total: order.total || 0,
            subtotal: order.subtotal || 0,
            delivery_fee: order.delivery_fee || 0,
            service_fee: order.service_fee || 0,
            created_at: order.created_at,
            updated_at: order.updated_at,

            // Customer information from orders table
            customer_name: order.customer_name || order.users?.name || "Guest",
            customer_email: order.customer_email || order.users?.email || null,
            customer_phone: order.customer_phone || order.users?.phone || null,
            customer_address: order.customer_address || null,

            // Delivery information
            delivery_address: order.delivery_address || order.customer_address || null,
            delivery_type: order.delivery_type || "delivery",
            delivery_instructions: order.delivery_instructions || order.notes || null,

            // Timing information
            preparation_time: order.preparation_time || null,
            estimated_delivery_time: order.estimated_delivery_time || null,
            scheduled_time: order.scheduled_time || null,
            asap: order.asap !== undefined ? order.asap : true,

            // Items information
            items_count: itemsCount,
            total_items: totalItems,

            // Business information
            business_id: order.business_id,
            business_name: order.business_name || null,
            business_type: order.business_type || null,

            // Additional fields
            payment_method: order.payment_method || null,
            payment_status: order.payment_status || null,
            driver_id: order.driver_id || null,
            priority_level: order.priority_level || null
          }
        })
      )

      return NextResponse.json({
        orders: ordersWithItems,
        total: fallbackCount || 0,
        page,
        pageSize,
        totalPages: Math.ceil((fallbackCount || 0) / pageSize)
      })
    }

    return NextResponse.json({
      orders,
      total: count || 0,
      page,
      pageSize,
      totalPages: Math.ceil((count || 0) / pageSize)
    })
  } catch (error: any) {
    console.error("Error in GET /api/business-admin/orders:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}

// PATCH handler to update order status
export async function PATCH(request: Request) {
  try {
    // First, verify the user is authenticated and has business manager permissions
    const cookieStore = await cookies()
    const authClient = createServerComponentClient({ cookies: () => cookieStore })
    const adminClient = createClient(supabaseUrl, supabaseServiceKey)

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession()

    // Check for custom token in cookies or headers
    const customToken = cookieStore.get('loop_jersey_auth_token')?.value;
    const userEmailCookie = cookieStore.get('loop_jersey_user_email')?.value;

    // Check for token in Authorization header
    const authHeader = request.headers.get('Authorization');
    const headerToken = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null;

    // If no session, try to use custom token
    let userEmail = session?.user?.email;

    if (!userEmail && userEmailCookie) {
      userEmail = decodeURIComponent(userEmailCookie);
      console.log("Using email from cookie:", userEmail);
    }

    if (!userEmail && !customToken && !headerToken) {
      console.error("No authentication found")
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Get the user's profile to check their role
    const { data: userProfile, error: profileError } = await adminClient
      .from("users")
      .select("id, role, auth_id")
      .eq("email", userEmail)
      .single()

    if (profileError || !userProfile) {
      console.error("Failed to get user profile:", profileError)
      return NextResponse.json(
        { error: "User profile not found" },
        { status: 403 }
      )
    }

    // Check if user is a business manager, admin, or super_admin
    const isBusinessManager = userProfile.role === "business_manager"
    const isBusinessStaff = userProfile.role === "business_staff"
    const isAdmin = userProfile.role === "admin"
    const isSuperAdmin = userProfile.role === "super_admin"

    if (!isBusinessManager && !isBusinessStaff && !isAdmin && !isSuperAdmin) {
      console.error("User does not have business manager permissions:", userProfile.role)
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    // Parse the request body
    const { orderId, status, notes } = await request.json()

    if (!orderId || !status) {
      return NextResponse.json(
        { error: "Order ID and status are required" },
        { status: 400 }
      )
    }

    // Default notes if not provided
    const statusNotes = notes || `Status changed to ${status}`;

    // For admin users, allow updating any order
    if (isAdmin || isSuperAdmin) {
      try {
        // Use enhanced status update with automatic notifications
        const { updateOrderStatusWithNotifications } = await import('../orders/enhanced-status-update');

        const result = await updateOrderStatusWithNotifications({
          orderId: orderId,
          newStatus: status,
          notes: statusNotes,
          updatedBy: userProfile.auth_id
        });

        return NextResponse.json({
          message: "Order status updated successfully",
          order: result.order
        });
      } catch (transactionError) {
        console.error("Error in order status update transaction:", transactionError);
        return NextResponse.json(
          { error: "Failed to update order status", details: transactionError.message },
          { status: 500 }
        );
      }
    }

    // For business users, verify they own the order
    let businessId: number | null = null

    // Check if user is a business manager
    if (isBusinessManager) {
      const { data: managerData, error: managerError } = await adminClient
        .from("business_managers")
        .select("business_id")
        .eq("user_id", userProfile.id)
        .single()

      if (managerError) {
        console.error("Error fetching business manager data:", managerError)
        return NextResponse.json(
          { error: "Business manager data not found" },
          { status: 404 }
        )
      }

      businessId = managerData.business_id
    }

    // Check if user is a business staff
    if (isBusinessStaff) {
      const { data: staffData, error: staffError } = await adminClient
        .from("business_staff")
        .select("business_id")
        .eq("user_id", userProfile.id)
        .single()

      if (staffError) {
        console.error("Error fetching business staff data:", staffError)
        return NextResponse.json(
          { error: "Business staff data not found" },
          { status: 404 }
        )
      }

      businessId = staffData.business_id
    }

    if (!businessId) {
      return NextResponse.json(
        { error: "No business found for this user" },
        { status: 404 }
      )
    }

    // Verify the order belongs to this business
    const { data: order, error: orderError } = await adminClient
      .from("orders")
      .select("id, business_id")
      .eq("id", orderId)
      .single()

    if (orderError) {
      console.error("Error fetching order:", orderError)
      return NextResponse.json(
        { error: "Order not found" },
        { status: 404 }
      )
    }

    if (order.business_id !== businessId) {
      return NextResponse.json(
        { error: "You do not have permission to update this order" },
        { status: 403 }
      )
    }

    try {
      // Use enhanced status update with automatic notifications
      const { updateOrderStatusWithNotifications } = await import('../orders/enhanced-status-update');

      const result = await updateOrderStatusWithNotifications({
        orderId: orderId,
        newStatus: status,
        notes: statusNotes,
        updatedBy: userProfile.auth_id,
        businessId: businessId.toString()
      });

      return NextResponse.json({
        message: "Order status updated successfully",
        order: result.order
      });
    } catch (transactionError) {
      console.error("Error in order status update transaction:", transactionError);
      return NextResponse.json(
        { error: "Failed to update order status", details: transactionError.message },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error("Error in PATCH /api/business-admin/orders:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
