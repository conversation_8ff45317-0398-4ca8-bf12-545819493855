// This service handles push notification subscriptions and sending
// In a production app, the server would handle sending notifications

const PUBLIC_VAPID_KEY = "BLBz-HdUcYLO8Qx0saA9tVQWMbxjG9zCKcgQGqQjgLJbA1TgkP3xdHBqsXF7Cz8YPCKJhcYEFYJ_zyVA2PR2VJo"

interface PushSubscription {
  endpoint: string
  expirationTime: number | null
  keys: {
    p256dh: string
    auth: string
  }
}

export interface NotificationPayload {
  title: string
  body: string
  icon?: string
  badge?: string
  image?: string
  data?: {
    url?: string
    orderId?: string
    type?: string
    [key: string]: any
  }
  actions?: {
    action: string
    title: string
    icon?: string
  }[]
}

class NotificationService {
  private _subscription: PushSubscription | null = null
  private _permissionStatus: NotificationPermission = "default"
  private _onSubscriptionChange: ((subscription: PushSubscription | null) => void)[] = []

  constructor() {
    // Check if we're in a browser environment
    if (typeof window !== "undefined" && "Notification" in window) {
      this._permissionStatus = Notification.permission
    }
  }

  get permissionStatus(): NotificationPermission {
    return this._permissionStatus
  }

  get subscription(): PushSubscription | null {
    return this._subscription
  }

  async initialize(): Promise<void> {
    if (!this.isPushSupported()) {
      console.warn("Push notifications are not supported in this browser")
      return
    }

    // Check if permission is already granted
    if (Notification.permission === "granted") {
      await this.getSubscription()
    }
  }

  isPushSupported(): boolean {
    return (
      typeof window !== "undefined" &&
      "serviceWorker" in navigator &&
      "PushManager" in window &&
      "Notification" in window
    )
  }

  async requestPermission(): Promise<NotificationPermission> {
    if (!this.isPushSupported()) {
      return "denied"
    }

    try {
      const permission = await Notification.requestPermission()
      this._permissionStatus = permission

      if (permission === "granted") {
        await this.subscribe()
      }

      return permission
    } catch (error) {
      console.error("Error requesting notification permission:", error)
      return "denied"
    }
  }

  async subscribe(): Promise<PushSubscription | null> {
    if (!this.isPushSupported() || Notification.permission !== "granted") {
      return null
    }

    try {
      const registration = await navigator.serviceWorker.ready

      // Subscribe to push notifications
      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(PUBLIC_VAPID_KEY),
      })

      this._subscription = subscription as unknown as PushSubscription

      // Save the subscription to your server
      await this.saveSubscriptionToServer(subscription)

      // Notify listeners
      this._onSubscriptionChange.forEach((callback) => callback(this._subscription))

      return this._subscription
    } catch (error) {
      console.error("Failed to subscribe to push notifications:", error)
      return null
    }
  }

  async unsubscribe(): Promise<boolean> {
    if (!this._subscription) {
      return false
    }

    try {
      const success = await this._subscription.unsubscribe()
      if (success) {
        // Remove subscription from server
        await this.removeSubscriptionFromServer(this._subscription)
        this._subscription = null

        // Notify listeners
        this._onSubscriptionChange.forEach((callback) => callback(null))
      }
      return success
    } catch (error) {
      console.error("Error unsubscribing from push notifications:", error)
      return false
    }
  }

  async getSubscription(): Promise<PushSubscription | null> {
    if (!this.isPushSupported()) {
      return null
    }

    try {
      const registration = await navigator.serviceWorker.ready
      const subscription = await registration.pushManager.getSubscription()
      this._subscription = subscription as unknown as PushSubscription
      return this._subscription
    } catch (error) {
      console.error("Error getting push subscription:", error)
      return null
    }
  }

  onSubscriptionChange(callback: (subscription: PushSubscription | null) => void): () => void {
    this._onSubscriptionChange.push(callback)
    return () => {
      this._onSubscriptionChange = this._onSubscriptionChange.filter((cb) => cb !== callback)
    }
  }

  private async saveSubscriptionToServer(subscription: PushSubscriptionJSON): Promise<void> {
    try {
      const response = await fetch('/api/notifications/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          subscription,
          deviceType: this.getDeviceType(),
          browserName: this.getBrowserName(),
          userAgent: navigator.userAgent
        })
      })

      if (!response.ok) {
        throw new Error(`Failed to save subscription: ${response.statusText}`)
      }

      console.log("Subscription saved to server successfully")
    } catch (error) {
      console.error("Error saving subscription to server:", error)
      throw error
    }
  }

  private async removeSubscriptionFromServer(subscription: PushSubscription): Promise<void> {
    try {
      const response = await fetch(`/api/notifications/subscribe?endpoint=${encodeURIComponent(subscription.endpoint)}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to remove subscription: ${response.statusText}`)
      }

      console.log("Subscription removed from server successfully")
    } catch (error) {
      console.error("Error removing subscription from server:", error)
      throw error
    }
  }

  private getDeviceType(): string {
    const userAgent = navigator.userAgent.toLowerCase()
    if (/mobile|android|iphone|ipad|tablet/.test(userAgent)) {
      return 'mobile'
    } else if (/tablet|ipad/.test(userAgent)) {
      return 'tablet'
    } else {
      return 'desktop'
    }
  }

  private getBrowserName(): string {
    const userAgent = navigator.userAgent
    if (userAgent.includes('Chrome')) return 'Chrome'
    if (userAgent.includes('Firefox')) return 'Firefox'
    if (userAgent.includes('Safari')) return 'Safari'
    if (userAgent.includes('Edge')) return 'Edge'
    return 'Unknown'
  }

  // For demo purposes - in a real app, this would be handled by your backend
  async sendNotification(payload: NotificationPayload): Promise<void> {
    if (!this._subscription) {
      console.warn("No push subscription available")
      return
    }

    // In a real app, you would send this to your server
    // and the server would send the push notification
    console.log("Sending notification:", payload)

    // For demo purposes, we'll show a notification directly
    if (Notification.permission === "granted") {
      const registration = await navigator.serviceWorker.ready
      registration.showNotification(payload.title, {
        body: payload.body,
        icon: payload.icon || "/android-chrome-192x192.png",
        badge: payload.badge || "/favicon-32x32.png",
        image: payload.image,
        data: payload.data,
        actions: payload.actions,
      })
    }
  }

  // Helper function to convert base64 to Uint8Array
  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = "=".repeat((4 - (base64String.length % 4)) % 4)
    const base64 = (base64String + padding).replace(/-/g, "+").replace(/_/g, "/")
    const rawData = window.atob(base64)
    const outputArray = new Uint8Array(rawData.length)
    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i)
    }
    return outputArray
  }
}

// Create a singleton instance
export const notificationService = new NotificationService()
