"use client"

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rig<PERSON> } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Users,
  Search,
  MessageSquare,
  User,
  Filter,
  Heart,
  Plus,
  Bell
} from "lucide-react"
import { ConnectionCard } from './ConnectionCard'
import { UserSearchCard } from './UserSearchCard'
import { Connection, UserProfile, SearchFilters, ConnectionFilters } from '../types'
import { mockConnections, mockProfiles, delay } from '../mock-data'

export function ConnectionsHub() {
  const [activeTab, setActiveTab] = useState('connections')
  const [connections, setConnections] = useState<Connection[]>([])
  const [searchResults, setSearchResults] = useState<UserProfile[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [connectionFilters, setConnectionFilters] = useState<ConnectionFilters>({
    status: 'active',
    favorites_only: false
  })

  // Load initial data
  useEffect(() => {
    loadConnections()
  }, [connectionFilters])

  const loadConnections = async () => {
    setIsLoading(true)
    try {
      // Simulate API call
      await delay(500)

      let filteredConnections = [...mockConnections]

      // Apply filters
      if (connectionFilters.status && connectionFilters.status !== 'all') {
        filteredConnections = filteredConnections.filter(
          conn => conn.status === connectionFilters.status
        )
      }

      if (connectionFilters.favorites_only) {
        filteredConnections = filteredConnections.filter(conn => conn.is_favorite)
      }

      if (connectionFilters.type) {
        filteredConnections = filteredConnections.filter(
          conn => conn.connection_type === connectionFilters.type
        )
      }

      setConnections(filteredConnections)
    } catch (error) {
      console.error('Failed to load connections:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const searchUsers = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([])
      return
    }

    setIsLoading(true)
    try {
      // Simulate API call
      await delay(300)

      const results = mockProfiles.filter(profile =>
        profile.display_name.toLowerCase().includes(query.toLowerCase()) ||
        profile.bio?.toLowerCase().includes(query.toLowerCase())
      )

      setSearchResults(results)
    } catch (error) {
      console.error('Failed to search users:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleToggleFavorite = async (connectionId: string, isFavorite: boolean) => {
    try {
      // Simulate API call
      await delay(200)

      setConnections(prev =>
        prev.map(conn =>
          conn.id === connectionId
            ? { ...conn, is_favorite: isFavorite }
            : conn
        )
      )
    } catch (error) {
      console.error('Failed to toggle favorite:', error)
    }
  }

  const handleConnect = async (userId: string, connectionType: string) => {
    try {
      // Simulate API call
      await delay(500)

      console.log(`Connecting to user ${userId} with type ${connectionType}`)
      // In real implementation, this would call the API
      // and refresh the connections list
    } catch (error) {
      console.error('Failed to connect:', error)
    }
  }

  const handleMessage = (connection: Connection) => {
    console.log('Opening message thread for connection:', connection.id)
    // In real implementation, this would open the messaging interface
  }

  const handleViewProfile = (userId: string) => {
    console.log('Viewing profile for user:', userId)
    // In real implementation, this would open the profile modal/page
  }

  const getConnectionStats = () => {
    const total = mockConnections.length
    const active = mockConnections.filter(c => c.status === 'active').length
    const pending = mockConnections.filter(c => c.status === 'pending').length
    const favorites = mockConnections.filter(c => c.is_favorite).length

    return { total, active, pending, favorites }
  }

  const stats = getConnectionStats()

  return (
    <div className="container mx-auto p-4 sm:p-6 max-w-6xl">
      <div className="mb-4 sm:mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold">Connections Hub</h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Connect with businesses, riders, and customers in the Loop Jersey ecosystem
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" className="text-xs sm:text-sm">
              <Bell className="h-4 w-4 mr-1 sm:mr-2" />
              <span className="hidden sm:inline">Notifications</span>
              <span className="sm:hidden">Alerts</span>
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 sm:gap-4 mb-4 sm:mb-6">
          <Card>
            <CardContent className="p-3 sm:p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs sm:text-sm text-muted-foreground">Total</p>
                  <p className="text-lg sm:text-2xl font-bold">{stats.total}</p>
                </div>
                <Users className="h-6 w-6 sm:h-8 sm:w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-3 sm:p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs sm:text-sm text-muted-foreground">Active</p>
                  <p className="text-lg sm:text-2xl font-bold text-green-600">{stats.active}</p>
                </div>
                <Users className="h-6 w-6 sm:h-8 sm:w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-3 sm:p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs sm:text-sm text-muted-foreground">Pending</p>
                  <p className="text-lg sm:text-2xl font-bold text-amber-600">{stats.pending}</p>
                </div>
                <Users className="h-6 w-6 sm:h-8 sm:w-8 text-amber-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-3 sm:p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs sm:text-sm text-muted-foreground">Favorites</p>
                  <p className="text-lg sm:text-2xl font-bold text-red-600">{stats.favorites}</p>
                </div>
                <Heart className="h-6 w-6 sm:h-8 sm:w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4 sm:space-y-6">
        <TabsList className="grid w-full grid-cols-4 h-auto">
          <TabsTrigger value="connections" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 py-2 sm:py-3 text-xs sm:text-sm">
            <Users className="h-4 w-4" />
            <span className="hidden sm:inline">My Connections</span>
            <span className="sm:hidden">Connections</span>
          </TabsTrigger>
          <TabsTrigger value="discover" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 py-2 sm:py-3 text-xs sm:text-sm">
            <Search className="h-4 w-4" />
            <span>Discover</span>
          </TabsTrigger>
          <TabsTrigger value="messages" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 py-2 sm:py-3 text-xs sm:text-sm">
            <MessageSquare className="h-4 w-4" />
            <span>Messages</span>
          </TabsTrigger>
          <TabsTrigger value="profile" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 py-2 sm:py-3 text-xs sm:text-sm">
            <User className="h-4 w-4" />
            <span>Profile</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="connections" className="space-y-4 sm:space-y-6">
          <Card>
            <CardHeader className="pb-4">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                  <CardTitle className="text-lg sm:text-xl">My Connections</CardTitle>
                  <CardDescription className="text-sm">
                    Manage your relationships with businesses, riders, and customers
                  </CardDescription>
                </div>
                <Button size="sm" className="text-xs sm:text-sm">
                  <Plus className="h-4 w-4 mr-1 sm:mr-2" />
                  <span className="hidden sm:inline">New Connection</span>
                  <span className="sm:hidden">New</span>
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {/* Filters */}
              <div className="flex flex-col sm:flex-row flex-wrap gap-3 sm:gap-4 mb-4 sm:mb-6">
                <Select
                  value={connectionFilters.status || 'active'}
                  onValueChange={(value) =>
                    setConnectionFilters(prev => ({ ...prev, status: value as any }))
                  }
                >
                  <SelectTrigger className="w-full sm:w-[140px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="blocked">Blocked</SelectItem>
                    <SelectItem value="archived">Archived</SelectItem>
                  </SelectContent>
                </Select>

                <Select
                  value={connectionFilters.type || 'all'}
                  onValueChange={(value) =>
                    setConnectionFilters(prev => ({ ...prev, type: value === 'all' ? undefined : value as any }))
                  }
                >
                  <SelectTrigger className="w-full sm:w-[160px]">
                    <SelectValue placeholder="Connection Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="customer-business">Customer-Business</SelectItem>
                    <SelectItem value="customer-rider">Customer-Rider</SelectItem>
                    <SelectItem value="business-rider">Business-Rider</SelectItem>
                  </SelectContent>
                </Select>

                <Button
                  variant={connectionFilters.favorites_only ? "default" : "outline"}
                  size="sm"
                  className="text-xs sm:text-sm"
                  onClick={() =>
                    setConnectionFilters(prev => ({
                      ...prev,
                      favorites_only: !prev.favorites_only
                    }))
                  }
                >
                  <Heart className="h-4 w-4 mr-1 sm:mr-2" />
                  <span className="hidden sm:inline">Favorites Only</span>
                  <span className="sm:hidden">Favorites</span>
                </Button>
              </div>

              {/* Connections Grid */}
              {isLoading ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[...Array(6)].map((_, i) => (
                    <Card key={i} className="animate-pulse">
                      <CardContent className="p-6">
                        <div className="h-20 bg-muted rounded"></div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : connections.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {connections.map((connection) => (
                    <ConnectionCard
                      key={connection.id}
                      connection={connection}
                      onMessage={handleMessage}
                      onToggleFavorite={handleToggleFavorite}
                      onViewProfile={handleViewProfile}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No connections found</h3>
                  <p className="text-muted-foreground mb-4">
                    {connectionFilters.favorites_only
                      ? "You haven't marked any connections as favorites yet."
                      : "Start connecting with businesses, riders, and customers."
                    }
                  </p>
                  <Button onClick={() => setActiveTab('discover')}>
                    <Search className="h-4 w-4 mr-2" />
                    Discover People
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="discover" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Discover People</CardTitle>
              <CardDescription>
                Find and connect with businesses, riders, and customers
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Search */}
              <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 mb-4 sm:mb-6">
                <div className="flex-1">
                  <Input
                    placeholder="Search by name, business, or specialty..."
                    value={searchQuery}
                    onChange={(e) => {
                      setSearchQuery(e.target.value)
                      searchUsers(e.target.value)
                    }}
                    className="w-full text-sm sm:text-base"
                  />
                </div>
                <Button variant="outline" size="sm" className="text-xs sm:text-sm">
                  <Filter className="h-4 w-4 mr-1 sm:mr-2" />
                  <span>Filters</span>
                </Button>
              </div>

              {/* Search Results */}
              {searchQuery ? (
                isLoading ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {[...Array(6)].map((_, i) => (
                      <Card key={i} className="animate-pulse">
                        <CardContent className="p-6">
                          <div className="h-20 bg-muted rounded"></div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : searchResults.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {searchResults.map((user) => (
                      <UserSearchCard
                        key={user.id}
                        user={user}
                        connectionStatus="none"
                        onConnect={handleConnect}
                        onViewProfile={handleViewProfile}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No results found</h3>
                    <p className="text-muted-foreground">
                      Try adjusting your search terms or filters.
                    </p>
                  </div>
                )
              ) : (
                <div className="text-center py-12">
                  <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Start searching</h3>
                  <p className="text-muted-foreground">
                    Enter a name, business, or specialty to find people to connect with.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="messages" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Messages</CardTitle>
              <CardDescription>
                Your conversations with connections
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Messages coming soon</h3>
                <p className="text-muted-foreground">
                  Real-time messaging will be available in the next update.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="profile" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>My Profile</CardTitle>
              <CardDescription>
                Manage your connection profile and preferences
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <User className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Profile management coming soon</h3>
                <p className="text-muted-foreground">
                  Profile editing and role management will be available in the next update.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
