const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

async function testMessagingAPISimple() {
  console.log('🧪 Testing Messaging API Endpoints (Simple)\n');

  try {
    // Test 1: Health check endpoint
    console.log('1️⃣ Testing health check endpoint...');
    const healthResponse = await fetch('http://localhost:3000/api/connections-hub');
    const healthData = await healthResponse.json();
    
    if (healthResponse.ok) {
      console.log('✅ Health check passed');
      console.log(`   API: ${healthData.name} v${healthData.version}`);
      console.log(`   Endpoints available: ${Object.keys(healthData.endpoints).length}`);
    } else {
      console.log('❌ Health check failed');
      return;
    }

    // Test 2: Unauthenticated request (should fail with 401)
    console.log('\n2️⃣ Testing unauthenticated request...');
    const unauthResponse = await fetch('http://localhost:3000/api/connections-hub/messages?view=conversations');
    
    if (unauthResponse.status === 401) {
      console.log('✅ Unauthenticated request correctly rejected (401)');
    } else {
      console.log(`❌ Expected 401, got ${unauthResponse.status}`);
    }

    // Test 3: Test with invalid token (should fail with 401)
    console.log('\n3️⃣ Testing with invalid token...');
    const invalidTokenResponse = await fetch('http://localhost:3000/api/connections-hub/messages?view=conversations', {
      headers: {
        'Authorization': 'Bearer invalid-token-12345',
        'Content-Type': 'application/json'
      }
    });
    
    if (invalidTokenResponse.status === 401) {
      console.log('✅ Invalid token correctly rejected (401)');
    } else {
      console.log(`❌ Expected 401, got ${invalidTokenResponse.status}`);
    }

    // Test 4: Test message sending endpoint without auth (should fail)
    console.log('\n4️⃣ Testing message sending without auth...');
    const sendUnauthResponse = await fetch('http://localhost:3000/api/connections-hub/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        recipient_id: 'test-user-id',
        content: 'Test message',
        channel_type: 'general'
      })
    });
    
    if (sendUnauthResponse.status === 401) {
      console.log('✅ Unauthenticated message sending correctly rejected (401)');
    } else {
      console.log(`❌ Expected 401, got ${sendUnauthResponse.status}`);
    }

    // Test 5: Test thread endpoint without auth (should fail)
    console.log('\n5️⃣ Testing thread endpoint without auth...');
    const threadUnauthResponse = await fetch('http://localhost:3000/api/connections-hub/threads/test-thread-id');
    
    if (threadUnauthResponse.status === 401) {
      console.log('✅ Unauthenticated thread access correctly rejected (401)');
    } else {
      console.log(`❌ Expected 401, got ${threadUnauthResponse.status}`);
    }

    // Test 6: Check if the API endpoints are properly compiled
    console.log('\n6️⃣ Testing API endpoint compilation...');
    
    const endpoints = [
      '/api/connections-hub/messages',
      '/api/connections-hub/connections',
      '/api/connections-hub/users/search'
    ];

    for (const endpoint of endpoints) {
      try {
        const response = await fetch(`http://localhost:3000${endpoint}`);
        // We expect 401 for authenticated endpoints, not 404
        if (response.status === 401) {
          console.log(`✅ ${endpoint} - Compiled and responding (401 as expected)`);
        } else if (response.status === 404) {
          console.log(`❌ ${endpoint} - Not found (404) - endpoint may not be compiled`);
        } else {
          console.log(`✅ ${endpoint} - Compiled and responding (${response.status})`);
        }
      } catch (error) {
        console.log(`❌ ${endpoint} - Error: ${error.message}`);
      }
    }

    // Test 7: Check database connectivity through API
    console.log('\n7️⃣ Testing database connectivity...');
    
    // Use the existing check-tables endpoint if it exists
    try {
      const dbResponse = await fetch('http://localhost:3000/api/check-tables');
      if (dbResponse.ok) {
        console.log('✅ Database connectivity working (check-tables endpoint)');
      } else {
        console.log(`⚠️  Database check endpoint returned ${dbResponse.status}`);
      }
    } catch (error) {
      console.log('⚠️  Database check endpoint not available or failed');
    }

    console.log('\n📋 Test Summary:');
    console.log('✅ API server is running and responding');
    console.log('✅ Authentication is properly enforced');
    console.log('✅ Messaging endpoints are compiled and accessible');
    console.log('✅ Error handling is working correctly');
    console.log('\n🎯 Next Steps:');
    console.log('   - Test with real user authentication');
    console.log('   - Test message creation and retrieval');
    console.log('   - Test real-time subscriptions');
    console.log('   - Test UI integration');

  } catch (error) {
    console.error('❌ Error during API testing:', error);
  }
}

// Run the tests
testMessagingAPISimple();
