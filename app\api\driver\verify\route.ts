import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

// Create a Supabase client with the service role key for admin operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

export async function GET(request: NextRequest) {
  try {
    // Get the user ID from the request (you might get this from auth headers)
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json(
        { error: "User ID is required" },
        { status: 400 }
      )
    }

    // Check if user has a driver profile
    const { data: driverProfile, error: driverError } = await supabase
      .from('driver_profiles')
      .select('id, is_verified, is_active, vehicle_type')
      .eq('user_id', userId)
      .single()

    if (driverError && driverError.code !== 'PGRST116') {
      console.error('Error checking driver profile:', driverError)
      return NextResponse.json(
        { error: "Failed to check driver status" },
        { status: 500 }
      )
    }

    if (!driverProfile) {
      return NextResponse.json({
        isDriver: false,
        isVerified: false,
        isActive: false,
        message: "No driver profile found"
      })
    }

    // Check business approvals
    const { data: approvals, error: approvalsError } = await supabase
      .from('driver_business_approvals')
      .select('business_id, status')
      .eq('driver_id', driverProfile.id)
      .eq('status', 'approved')

    if (approvalsError) {
      console.error('Error checking business approvals:', approvalsError)
    }

    return NextResponse.json({
      isDriver: true,
      isVerified: driverProfile.is_verified,
      isActive: driverProfile.is_active,
      vehicleType: driverProfile.vehicle_type,
      approvedBusinesses: approvals?.length || 0,
      driverProfileId: driverProfile.id
    })

  } catch (error) {
    console.error('Error in driver verification:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
