"use client";

import React from 'react';
import Link from 'next/link';
import { Star, ArrowRight } from 'lucide-react';
import FallbackImage from "@/components/fallback-image";
import SquareBusinessCard from "@/components/square-business-card";
import DynamicDeliveryTime from "@/components/dynamic-delivery-time";
import { ParishName } from "@/lib/parish-distances";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious
} from "@/components/ui/carousel";

interface Business {
  id: string | number;
  slug: string;
  name: string;
  logo_url?: string;
  banner_url?: string;
  rating?: number;
  review_count?: number;
  delivery_time_minutes?: number;
  preparation_time_minutes?: number;
  delivery_fee?: number;
  delivery_radius?: number;
  minimum_order_amount?: number;
  location?: string;
  business_type: string;
  business_type_slug: string;
}

interface BusinessCarouselProps {
  businesses: Business[];
  title: string;
}

export default function BusinessCarousel({ businesses, title }: BusinessCarouselProps) {
  if (!businesses || businesses.length === 0) {
    return <p className="text-center text-gray-500">No businesses found.</p>;
  }

  return (
    <div className="py-12">
      <div className="container-fluid">
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-2xl md:text-3xl font-bold text-gray-800">{title}</h2>
          <Link href="/search" className="hidden md:flex items-center bg-white px-3 py-1.5 rounded-full shadow-sm hover:shadow border border-emerald-100 text-emerald-600 hover:text-emerald-700 transition-colors">
            <span className="text-sm font-medium mr-2">View all</span>
            <ArrowRight className="h-4 w-4" />
          </Link>
        </div>

        <div className="w-full mx-auto">
          <Carousel
            opts={{
              align: "start",
              loop: true,
            }}
            className="w-full"
          >
            <CarouselContent className="-ml-2 -mr-2">
              {businesses.map((business) => {
                // Determine if we should use square card (for logo_url)
                const hasLogo = business.logo_url ? true : false;

                // Add offer badge if this is a deal
                const offers = business.dealType ? [
                  { text: business.dealType, color: 'bg-red-500' }
                ] : [];

                return (
                  <CarouselItem key={business.id} className="md:basis-1/2 lg:basis-1/3 xl:basis-1/4 2xl:basis-1/5 pl-2 pr-2">
                    {hasLogo ? (
                      // Use SquareBusinessCard for businesses with logo
                      <SquareBusinessCard
                        id={business.slug || business.id.toString()}
                        name={business.name}
                        image={business.logo_url || "/placeholder.svg"}
                        businessType={business.business_type_slug || "business"}
                        rating={business.rating}
                        deliveryTime={business.delivery_time_minutes ? `${business.delivery_time_minutes}` : "30"}
                        deliveryTimeRange={`${business.delivery_time_minutes ? Math.max(5, business.delivery_time_minutes - 5) : 25}-${business.delivery_time_minutes ? business.delivery_time_minutes + 5 : 35} min`}
                        deliveryFee={business.delivery_fee === 0
                          ? "Free delivery"
                          : `£${business.delivery_fee?.toFixed(2) || "N/A"}`}
                        distance={business.delivery_radius ? `${business.delivery_radius} miles` : undefined}
                        sponsored={false}
                        offers={offers}
                        location={business.location}
                        deliveryRadius={business.delivery_radius ? `${business.delivery_radius} miles` : undefined}
                        preparationTime={business.preparation_time_minutes ? `${business.preparation_time_minutes} min` : undefined}
                      />
                    ) : (
                      // Use standard card layout for businesses without logo
                      <Link href={`/business/${business.slug || business.id}`}>
                        <div className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 h-full flex flex-col">
                          <div className="relative h-40">
                            <div className="absolute top-0 right-0 bg-emerald-500 text-white text-xs font-bold px-3 py-1.5 m-3 rounded-full">
                              {business.business_type}
                            </div>
                            <FallbackImage
                              src={business.banner_url || "/placeholder.svg"}
                              alt={business.name}
                              fallbackSrc="/placeholder.svg"
                              className="w-full h-full object-contain"
                            />
                          </div>
                          <div className="p-4 flex flex-col flex-grow">
                            <h3 className="font-bold text-lg mb-1">{business.name}</h3>

                            {/* Location */}
                            <div className="flex items-center text-gray-500 text-sm mb-2">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                              </svg>
                              {business.location || "Jersey"}
                            </div>

                            {/* Rating */}
                            <div className="flex items-center mb-2">
                              <Star className="h-4 w-4 text-yellow-400 mr-1" />
                              <span className="text-sm font-medium text-gray-700">
                                {business.rating?.toFixed(1) || "N/A"}
                                {business.review_count && (
                                  <span className="text-gray-500 ml-1">
                                    ({business.review_count} reviews)
                                  </span>
                                )}
                              </span>
                            </div>

                            {/* Delivery Information Card */}
                            <div className="mt-4 bg-gray-50 p-3 rounded-md">
                              {/* Card Header */}
                              <div className="flex items-center mb-2">
                                <div className="bg-emerald-50 p-1.5 rounded-full mr-2">
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 17H4a2 2 0 01-2-2V5a2 2 0 012-2h16a2 2 0 012 2v10a2 2 0 01-2 2h-1m-6 0a2 2 0 002 2h6a2 2 0 002-2m-6 0a2 2 0 01-2-2v-1m0-10a2 2 0 00-2-2H5a2 2 0 00-2 2v1" />
                                  </svg>
                                </div>
                                <span className="font-medium text-gray-700">Delivery Information</span>
                              </div>

                              {/* Delivery Details Row */}
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mt-2">
                                {/* Delivery Time */}
                                <div className="flex items-center">
                                  <div className="bg-gray-100 p-1.5 rounded-full mr-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                  </div>
                                  <div>
                                    <DynamicDeliveryTime
                                      businessCoordinates={
                                        business.coordinates && Array.isArray(business.coordinates)
                                          ? business.coordinates as [number, number]
                                          : [-2.1053, 49.1805] // Default to St Helier
                                      }
                                      businessParish={business.location as ParishName}
                                      preparationTimeMinutes={business.preparationTimeMinutes || 15}
                                      defaultDeliveryTime={
                                        typeof business.delivery_time_minutes === 'number'
                                          ? business.delivery_time_minutes
                                          : typeof business.deliveryTime === 'number'
                                            ? business.deliveryTime
                                            : 20
                                      }
                                      showTimeRange={true}
                                    />
                                  </div>
                                </div>

                                {/* Delivery Fee */}
                                <div className="flex items-center">
                                  <div className="bg-gray-100 p-1.5 rounded-full mr-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 9a2 2 0 10-4 0v5a2 2 0 104 0V9z" />
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 9h.01M15 9h.01M9 13h.01M15 13h.01M9 17h.01M15 17h.01" />
                                    </svg>
                                  </div>
                                  <div className={`text-sm font-medium ${business.delivery_fee === 0 ? 'text-emerald-600' : 'text-gray-700'}`}>
                                    {business.delivery_fee === 0
                                      ? "Free delivery"
                                      : `£${business.delivery_fee?.toFixed(2) || "N/A"}`
                                    }
                                  </div>
                                </div>
                              </div>

                              {/* Additional Info Row */}
                              <div className="flex flex-wrap gap-4 text-xs text-gray-500 mt-2">
                                {/* Preparation Time */}
                                {(typeof business.preparation_time_minutes === 'number' ||
                                  typeof business.preparationTimeMinutes === 'number') && (
                                  <div className="flex items-center">
                                    <span>
                                      Prep time: {typeof business.preparation_time_minutes === 'number' ? business.preparation_time_minutes :
                                       typeof business.preparationTimeMinutes === 'number' ? business.preparationTimeMinutes : 15} min
                                    </span>
                                  </div>
                                )}

                                {/* Delivery Radius */}
                                {business.delivery_radius && (
                                  <div className="flex items-center">
                                    <span>
                                      Delivery radius: {business.delivery_radius} miles
                                    </span>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      </Link>
                    )}
                  </CarouselItem>
                );
              })}
            </CarouselContent>
            <div className="hidden md:flex justify-end mt-4 gap-2">
              <CarouselPrevious className="relative h-10 w-10 rounded-full border-0 bg-white shadow-md hover:bg-gray-50 hover:scale-105 transition-all duration-200" style={{ position: 'static', left: 'auto', transform: 'none' }} />
              <CarouselNext className="relative h-10 w-10 rounded-full border-0 bg-white shadow-md hover:bg-gray-50 hover:scale-105 transition-all duration-200" style={{ position: 'static', right: 'auto', transform: 'none' }} />
            </div>
          </Carousel>
        </div>
      </div>
    </div>
  );
}
