import { NextResponse } from 'next/server';
import { adminClient } from '@/lib/supabase-admin';
import { geocodeAddress } from '@/lib/geocoding';
import { formatPointString } from '@/lib/address-utils';

/**
 * API endpoint to update coordinates for all user addresses
 * This is an admin-only endpoint that should be protected
 */
export async function POST(request: Request) {
  try {
    console.log('Starting batch update of user address coordinates');

    // Test the adminClient first
    try {
      const { data: testData, error: testError } = await adminClient
        .from('users')
        .select('id, email')
        .limit(1);

      if (testError) {
        console.error('Error testing adminClient:', testError);
        return NextResponse.json(
          { error: 'Failed to test adminClient', details: testError.message },
          { status: 500 }
        );
      }

      console.log('adminClient test successful:', testData);
    } catch (testError: any) {
      console.error('Exception testing adminClient:', testError);
      return NextResponse.json(
        { error: 'Exception testing adminClient', details: testError.message },
        { status: 500 }
      );
    }

    // Get all user addresses that need coordinate updates
    console.log('Fetching user addresses that need coordinate updates');
    const { data: addresses, error: fetchError } = await adminClient
      .from('user_addresses')
      .select('id, user_id, address_line1, address_line2, parish, postcode, coordinates, latitude, longitude');

    if (fetchError) {
      console.error('Error fetching addresses:', fetchError);
      return NextResponse.json(
        { error: 'Failed to fetch addresses', details: fetchError.message },
        { status: 500 }
      );
    }

    if (!addresses || addresses.length === 0) {
      return NextResponse.json({
        message: 'No addresses found that need coordinate updates',
        updated: 0,
        total: 0
      });
    }

    console.log(`Found ${addresses.length} addresses that need coordinate updates`);

    // Process each address
    const results = {
      success: 0,
      failed: 0,
      total: addresses.length,
      failures: [] as any[]
    };

    for (const address of addresses) {
      try {
        // Check if we have coordinates in the wrong format (as a string like "POINT(-2.134973 49.246555)")
        if (address.coordinates && typeof address.coordinates === 'string' &&
            address.coordinates.includes('POINT') && (!address.latitude || !address.longitude)) {
          console.log(`Address ${address.id} has coordinates in wrong format: ${address.coordinates}`);

          // Try to parse the coordinates from the string
          const coordsMatch = address.coordinates.match(/POINT\s*\(\s*([^\s]+)\s+([^\s]+)\s*\)/i);
          if (coordsMatch && coordsMatch.length === 3) {
            const longitude = parseFloat(coordsMatch[1]);
            const latitude = parseFloat(coordsMatch[2]);

            if (!isNaN(longitude) && !isNaN(latitude)) {
              console.log(`Parsed coordinates from string: [${longitude}, ${latitude}]`);

              // Update with properly formatted coordinates
              try {
                const { data: updateData, error: updateError } = await adminClient
                  .from('user_addresses')
                  .update({
                    coordinates: formatPointString(longitude, latitude),
                    latitude: latitude,
                    longitude: longitude,
                    coordinates_updated_at: new Date().toISOString()
                  })
                  .eq('id', address.id)
                  .select();

                if (updateError) {
                  console.error(`Error updating address ${address.id} with parsed coordinates:`, updateError);
                  results.failed++;
                  results.failures.push({
                    id: address.id,
                    reason: updateError.message
                  });
                  continue;
                }

                console.log(`Successfully updated coordinates from parsed string for address ${address.id}`);
                results.success++;
                continue;
              } catch (updateException) {
                console.error(`Exception updating address ${address.id} with parsed coordinates:`, updateException);
                results.failed++;
                results.failures.push({
                  id: address.id,
                  reason: `Update exception: ${updateException.message || 'Unknown error'}`
                });
                continue;
              }
            }
          }
        }

        // Check if we already have valid coordinates in latitude and longitude fields
        if (address.latitude && address.longitude) {
          console.log(`Address ${address.id} already has valid coordinates: [${address.longitude}, ${address.latitude}]`);

          // Format coordinates as PostgreSQL POINT type
          const pointString = formatPointString(address.longitude, address.latitude);

          // Update the coordinates field with properly formatted POINT string
          try {
            const { data: updateData, error: updateError } = await adminClient
              .from('user_addresses')
              .update({
                coordinates: pointString,
                coordinates_updated_at: new Date().toISOString()
              })
              .eq('id', address.id)
              .select();

            if (updateError) {
              console.error(`Error updating address ${address.id} coordinates format:`, updateError);
              results.failed++;
              results.failures.push({
                id: address.id,
                reason: updateError.message
              });
              continue;
            }

            console.log(`Successfully updated coordinates format for address ${address.id}`);
            results.success++;
            continue;
          } catch (updateException) {
            console.error(`Exception updating address ${address.id} coordinates format:`, updateException);
            results.failed++;
            results.failures.push({
              id: address.id,
              reason: `Update exception: ${updateException.message || 'Unknown error'}`
            });
            continue;
          }
        }

        // Skip addresses without address_line1 or postcode
        if (!address.address_line1 && !address.postcode) {
          console.log(`Skipping address ${address.id} due to missing address_line1 and postcode`);
          results.failed++;
          results.failures.push({
            id: address.id,
            reason: 'Missing address_line1 and postcode'
          });
          continue;
        }

        // Construct the full address for geocoding
        const addressComponents = [
          address.address_line1,
          address.address_line2,
          `${address.parish}, Jersey`,
          address.postcode
        ].filter(Boolean);

        const fullAddress = addressComponents.join(", ");
        console.log(`Geocoding address for ID ${address.id}: "${fullAddress}"`);

        // Geocode the address
        console.log(`Calling geocodeAddress for: "${fullAddress}"`);
        let coordinates;
        try {
          coordinates = await geocodeAddress(fullAddress);
          console.log(`Geocoding result for "${fullAddress}":`, coordinates);
        } catch (geocodeError) {
          console.error(`Exception in geocodeAddress for "${fullAddress}":`, geocodeError);
          results.failed++;
          results.failures.push({
            id: address.id,
            reason: `Geocoding exception: ${geocodeError.message || 'Unknown error'}`
          });
          continue;
        }

        if (!coordinates) {
          console.error(`Could not geocode address: ${fullAddress}`);
          results.failed++;
          results.failures.push({
            id: address.id,
            reason: 'Geocoding failed - no coordinates returned'
          });
          continue;
        }

        console.log(`Successfully geocoded address to coordinates: [${coordinates[0]}, ${coordinates[1]}]`);

        // Format coordinates as PostgreSQL POINT type
        const pointString = formatPointString(coordinates[0], coordinates[1]);

        // Update the address record
        console.log(`Updating address ${address.id} with coordinates: ${pointString}`);
        const updateData = {
          coordinates: pointString,
          latitude: coordinates[1],
          longitude: coordinates[0],
          coordinates_updated_at: new Date().toISOString()
        };

        console.log(`Update data for address ${address.id}:`, updateData);

        try {
          const { data: updateData, error: updateError } = await adminClient
            .from('user_addresses')
            .update({
              coordinates: pointString,
              latitude: coordinates[1],
              longitude: coordinates[0],
              coordinates_updated_at: new Date().toISOString()
            })
            .eq('id', address.id)
            .select();

          if (updateError) {
            console.error(`Error updating address ${address.id}:`, updateError);
            results.failed++;
            results.failures.push({
              id: address.id,
              reason: updateError.message
            });
            continue;
          }

          console.log(`Update result for address ${address.id}:`, updateData);
        } catch (updateException) {
          console.error(`Exception updating address ${address.id}:`, updateException);
          results.failed++;
          results.failures.push({
            id: address.id,
            reason: `Update exception: ${updateException.message || 'Unknown error'}`
          });
          continue;
        }

        console.log(`Successfully updated coordinates for address ${address.id}`);
        results.success++;
      } catch (error: any) {
        console.error(`Error processing address ${address.id}:`, error);
        results.failed++;
        results.failures.push({
          id: address.id,
          reason: error.message || 'Unknown error'
        });
      }

      // Add a small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    return NextResponse.json({
      message: `Processed ${results.total} addresses: ${results.success} updated, ${results.failed} failed`,
      ...results
    });
  } catch (error: any) {
    console.error('Error in update-user-addresses-coordinates endpoint:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}
