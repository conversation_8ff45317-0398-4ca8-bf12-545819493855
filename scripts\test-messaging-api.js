const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function testMessagingAPI() {
  console.log('🧪 Testing Messaging API Endpoints\n');

  try {
    // Test 1: Health check endpoint
    console.log('1️⃣ Testing health check endpoint...');
    const healthResponse = await fetch('http://localhost:3000/api/connections-hub');
    const healthData = await healthResponse.json();
    
    if (healthResponse.ok) {
      console.log('✅ Health check passed');
      console.log(`   API: ${healthData.name} v${healthData.version}`);
    } else {
      console.log('❌ Health check failed');
      return;
    }

    // Test 2: Unauthenticated request (should fail)
    console.log('\n2️⃣ Testing unauthenticated request...');
    const unauthResponse = await fetch('http://localhost:3000/api/connections-hub/messages?view=conversations');
    
    if (unauthResponse.status === 401) {
      console.log('✅ Unauthenticated request correctly rejected (401)');
    } else {
      console.log(`❌ Expected 401, got ${unauthResponse.status}`);
    }

    // Test 3: Check if we can create a test user session
    console.log('\n3️⃣ Testing with service role authentication...');
    
    // Create a test user for API testing
    const testEmail = '<EMAIL>';
    const testPassword = 'test-password-123';
    
    // Try to sign up or sign in the test user
    let authResult = await supabase.auth.signInWithPassword({
      email: testEmail,
      password: testPassword
    });

    if (authResult.error && authResult.error.message.includes('Invalid login credentials')) {
      console.log('   Test user not found, creating...');
      authResult = await supabase.auth.signUp({
        email: testEmail,
        password: testPassword
      });
    }

    if (authResult.error) {
      console.log('❌ Could not create/authenticate test user:', authResult.error.message);
      console.log('   Skipping authenticated API tests');
      return;
    }

    const session = authResult.data.session;
    if (!session) {
      console.log('❌ No session created for test user');
      return;
    }

    console.log('✅ Test user authenticated');
    console.log(`   User ID: ${session.user.id}`);

    // Test 4: Authenticated request to get conversations
    console.log('\n4️⃣ Testing authenticated conversations request...');
    const conversationsResponse = await fetch('http://localhost:3000/api/connections-hub/messages?view=conversations', {
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json'
      }
    });

    if (conversationsResponse.ok) {
      const conversationsData = await conversationsResponse.json();
      console.log('✅ Conversations endpoint working');
      console.log(`   Found ${conversationsData.conversations?.length || 0} conversations`);
      console.log(`   Total: ${conversationsData.total || 0}`);
    } else {
      const errorData = await conversationsResponse.json();
      console.log(`❌ Conversations request failed (${conversationsResponse.status})`);
      console.log(`   Error: ${errorData.error}`);
    }

    // Test 5: Test sending a message (to self for testing)
    console.log('\n5️⃣ Testing message sending...');
    const messageData = {
      recipient_id: session.user.id, // Send to self for testing
      content: 'Test message from API test script',
      subject: 'API Test Message',
      channel_type: 'general',
      message_type: 'chat'
    };

    const sendResponse = await fetch('http://localhost:3000/api/connections-hub/messages', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(messageData)
    });

    if (sendResponse.ok) {
      const sendData = await sendResponse.json();
      console.log('✅ Message sending working');
      console.log(`   Message ID: ${sendData.data?.id}`);
      console.log(`   Thread ID: ${sendData.data?.thread_id}`);
      
      // Test 6: Get the thread we just created
      if (sendData.data?.thread_id) {
        console.log('\n6️⃣ Testing thread retrieval...');
        const threadResponse = await fetch(`http://localhost:3000/api/connections-hub/threads/${sendData.data.thread_id}`, {
          headers: {
            'Authorization': `Bearer ${session.access_token}`,
            'Content-Type': 'application/json'
          }
        });

        if (threadResponse.ok) {
          const threadData = await threadResponse.json();
          console.log('✅ Thread retrieval working');
          console.log(`   Messages in thread: ${threadData.messages?.length || 0}`);
          console.log(`   Unread count: ${threadData.unread_count || 0}`);
        } else {
          const threadError = await threadResponse.json();
          console.log(`❌ Thread retrieval failed (${threadResponse.status})`);
          console.log(`   Error: ${threadError.error}`);
        }
      }
    } else {
      const sendError = await sendResponse.json();
      console.log(`❌ Message sending failed (${sendResponse.status})`);
      console.log(`   Error: ${sendError.error}`);
    }

    // Test 7: Test conversations again to see if our message appears
    console.log('\n7️⃣ Testing conversations after sending message...');
    const finalConversationsResponse = await fetch('http://localhost:3000/api/connections-hub/messages?view=conversations', {
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json'
      }
    });

    if (finalConversationsResponse.ok) {
      const finalConversationsData = await finalConversationsResponse.json();
      console.log('✅ Final conversations check');
      console.log(`   Found ${finalConversationsData.conversations?.length || 0} conversations`);
      
      if (finalConversationsData.conversations?.length > 0) {
        const latestConv = finalConversationsData.conversations[0];
        console.log(`   Latest: "${latestConv.content?.substring(0, 50)}..."`);
        console.log(`   Channel: ${latestConv.channel_type}`);
      }
    }

    console.log('\n🎉 API testing completed!');

  } catch (error) {
    console.error('❌ Error during API testing:', error);
  }
}

// Run the tests
testMessagingAPI();
