"use client"

import { useEffect, useState } from "react"
import type { ReactNode } from "react"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { useAuth } from "@/context/unified-auth-context"
import { usePendingDriverApplications } from "@/hooks/use-pending-driver-applications"
import {
  BarChart3,
  Home,
  Settings,
  Users,
  ChevronDown,
  Bell,
  MenuIcon,
  LogOut,
  Store,
  MapPin,
  Database,
  Truck
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface AdminLayoutProps {
  children: ReactNode
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const pathname = usePathname()
  const router = useRouter()
  const { user, userProfile, isAdmin, refreshUserProfile, isLoading } = useAuth()
  const [isCheckingAccess, setIsCheckingAccess] = useState(false)
  const [accessChecked, setAccessChecked] = useState(false)
  const { pendingCount } = usePendingDriverApplications()

  // Redirect if not logged in or not authorized
  useEffect(() => {
    // Skip if we're still loading auth state or already checking access
    if (isLoading || isCheckingAccess) return;

    // Skip if we've already checked access and allowed it
    if (accessChecked) return;

    const checkAccess = async () => {
      setIsCheckingAccess(true);

      try {
        console.log("AdminLayout: Checking access, user:", !!user, "profile:", !!userProfile, "isAdmin:", isAdmin);

        // If no user, redirect to login
        if (!user) {
          console.log("AdminLayout: No user, redirecting to login");
          router.push("/login?redirectTo=" + encodeURIComponent(pathname));
          return;
        }

        // If we have a user but no profile, try to refresh the profile
        if (!userProfile) {
          console.log("AdminLayout: No user profile, attempting to refresh");
          try {
            const profile = await refreshUserProfile();

            // Check if the refreshed profile has admin access
            if (profile && (profile.role === 'admin' || profile.role === 'super_admin')) {
              console.log("AdminLayout: Refreshed profile has admin role, allowing access");
              setAccessChecked(true);
            } else {
              console.log("AdminLayout: Refreshed profile does not have admin role, redirecting to home");
              router.push("/");
            }
          } catch (err) {
            console.error("AdminLayout: Error refreshing profile:", err);
            // Don't redirect yet, we'll try again on next render
          }
        }
        // If we have a profile but not admin, redirect
        else if (!isAdmin) {
          console.log("AdminLayout: User does not have admin role, redirecting to home");
          router.push("/");
        }
        // If we have a profile and admin role, allow access
        else {
          console.log("AdminLayout: User has admin role, allowing access");
          setAccessChecked(true);
        }
      } finally {
        setIsCheckingAccess(false);
      }
    };

    checkAccess();
  }, [user, userProfile, isAdmin, router, refreshUserProfile, pathname, isLoading, isCheckingAccess, accessChecked]);

  // Add debugging to see what's happening
  console.log("AdminLayout: Current state:", {
    isLoading,
    isCheckingAccess,
    accessChecked,
    hasUser: !!user,
    hasProfile: !!userProfile,
    userRole: userProfile?.role,
    isAdmin
  });

  // Don't render anything until we've confirmed the user has access
  // This prevents any momentary display of admin content
  // But be more lenient during loading to avoid blocking legitimate admins
  if (!user || (!isAdmin && !isLoading && !isCheckingAccess)) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Verifying admin access...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen bg-gray-100">
      {/* Sidebar - Desktop */}
      <aside className="hidden md:flex flex-col w-64 bg-white border-r">
        <div className="p-4 border-b">
          <Link href="/admin" className="flex items-center">
            <span className="text-xl font-bold text-emerald-600">Loop Jersey</span>
            <span className="ml-2 text-xs bg-emerald-100 text-emerald-800 px-2 py-0.5 rounded">Admin</span>
          </Link>
        </div>

        <nav className="flex-1 p-4 space-y-1">
          <Link
            href="/admin"
            className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
              pathname === "/admin" ? "text-emerald-600 bg-emerald-50 font-medium" : ""
            }`}
          >
            <Home className="w-5 h-5 mr-3 text-gray-500" />
            Dashboard
          </Link>
          <Link
            href="/admin/businesses"
            className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
              pathname === "/admin/businesses" ? "text-emerald-600 bg-emerald-50 font-medium" : ""
            }`}
          >
            <Store className="w-5 h-5 mr-3 text-gray-500" />
            Businesses
          </Link>
          <Link
            href="/admin/update-coordinates"
            className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
              pathname === "/admin/update-coordinates" ? "text-emerald-600 bg-emerald-50 font-medium" : ""
            }`}
          >
            <MapPin className="w-5 h-5 mr-3 text-gray-500" />
            Update Coordinates
          </Link>
          <Link
            href="/admin/analytics"
            className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
              pathname === "/admin/analytics" ? "text-emerald-600 bg-emerald-50 font-medium" : ""
            }`}
          >
            <BarChart3 className="w-5 h-5 mr-3 text-gray-500" />
            Analytics
          </Link>
          <Link
            href="/admin/users"
            className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
              pathname === "/admin/users" ? "text-emerald-600 bg-emerald-50 font-medium" : ""
            }`}
          >
            <Users className="w-5 h-5 mr-3 text-gray-500" />
            Users
          </Link>
          <Link
            href="/admin/driver-applications"
            className={`flex items-center justify-between px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
              pathname === "/admin/driver-applications" ? "text-emerald-600 bg-emerald-50 font-medium" : ""
            }`}
          >
            <div className="flex items-center">
              <Truck className="w-5 h-5 mr-3 text-gray-500" />
              Driver Applications
            </div>
            {pendingCount > 0 && (
              <Badge variant="destructive" className="text-xs">
                {pendingCount}
              </Badge>
            )}
          </Link>
          <Link
            href="/admin/database"
            className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
              pathname === "/admin/database" ? "text-emerald-600 bg-emerald-50 font-medium" : ""
            }`}
          >
            <Database className="w-5 h-5 mr-3 text-gray-500" />
            Database
          </Link>
          <Link
            href="/admin/settings"
            className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
              pathname === "/admin/settings" ? "text-emerald-600 bg-emerald-50 font-medium" : ""
            }`}
          >
            <Settings className="w-5 h-5 mr-3 text-gray-500" />
            Settings
          </Link>
        </nav>

        <div className="p-4 border-t">
          <div className="flex items-center">
            <Avatar className="h-8 w-8">
              <AvatarFallback>AD</AvatarFallback>
            </Avatar>
            <div className="ml-3">
              <p className="text-sm font-medium">Super Admin</p>
              <p className="text-xs text-gray-500">System Administrator</p>
            </div>
            <Button variant="ghost" size="icon" className="ml-auto">
              <LogOut className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </aside>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Top Navigation */}
        <header className="bg-white border-b">
          <div className="flex items-center justify-between p-4">
            {/* Mobile Menu Button */}
            <Button variant="ghost" size="icon" className="md:hidden">
              <MenuIcon className="h-6 w-6" />
            </Button>

            <Link href="/admin" className="md:hidden flex items-center">
              <span className="text-xl font-bold text-emerald-600">Loop Jersey</span>
              <span className="ml-2 text-xs bg-emerald-100 text-emerald-800 px-2 py-0.5 rounded">Admin</span>
            </Link>

            {/* Spacer for layout */}
            <div className="hidden md:flex md:flex-1"></div>

            {/* Right Actions */}
            <div className="flex items-center space-x-3">
              <Button variant="ghost" size="icon" className="relative">
                <Bell className="h-5 w-5" />
                <span className="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full"></span>
              </Button>

              {/* User Menu - Desktop */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild className="hidden md:flex">
                  <Button variant="ghost" className="flex items-center">
                    <Avatar className="h-8 w-8 mr-2">
                      <AvatarFallback>AD</AvatarFallback>
                    </Avatar>
                    <span>Admin</span>
                    <ChevronDown className="ml-2 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuLabel>My Account</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>Profile</DropdownMenuItem>
                  <DropdownMenuItem>Settings</DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>Log out</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="flex-1 overflow-y-auto p-4 md:p-6">{children}</main>
      </div>
    </div>
  )
}
