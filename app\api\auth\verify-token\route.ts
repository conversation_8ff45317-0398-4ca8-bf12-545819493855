import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create Supabase client with service role key (only available server-side)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// GET endpoint to verify a token
export async function GET(request: NextRequest) {
  try {
    // Get the authorization header
    const authorization = request.headers.get('Authorization');

    // Check if we have an authorization header
    if (!authorization) {
      console.log("No authorization header found in verify-token API")
      // Skip auth check in development for easier testing
      if (process.env.NODE_ENV === 'development') {
        console.log("Development mode: Skipping auth check in verify-token API")
        return NextResponse.json({
          verified: true,
          message: "Development mode: Token verification skipped"
        });
      } else {
        return NextResponse.json(
          { error: "Authentication required" },
          { status: 401 }
        );
      }
    }

    console.log("Found authorization header in verify-token API, attempting to verify")

    // Extract the token
    const token = authorization.replace('Bearer ', '');

    try {
      // Verify the token
      const { data: { user }, error } = await supabase.auth.getUser(token);

      if (error || !user) {
        console.error("Invalid token in verify-token API:", error)

        // In development mode, allow expired tokens
        if (process.env.NODE_ENV === 'development') {
          console.log("Development mode: Allowing expired token");

          // Try to extract user info from the token
          try {
            // Basic JWT parsing to get the payload
            const payload = token.split('.')[1];
            if (payload) {
              const decodedData = JSON.parse(Buffer.from(payload, 'base64').toString());
              if (decodedData && decodedData.email) {
                console.log("Extracted email from expired token:", decodedData.email);

                // Return a mock user for development
                return NextResponse.json({
                  verified: true,
                  user: {
                    id: decodedData.sub || 'dev-user-id',
                    email: decodedData.email,
                    role: null // Will be fetched separately
                  },
                  message: "Development mode: Using expired token"
                });
              }
            }
          } catch (parseErr) {
            console.error("Could not parse expired token:", parseErr);
          }

          // If we couldn't extract user info, return a generic dev response
          return NextResponse.json({
            verified: true,
            user: {
              id: 'dev-user-id',
              email: '<EMAIL>',
              role: null
            },
            message: "Development mode: Using default dev user"
          });
        }

        return NextResponse.json(
          { error: "Invalid authentication token" },
          { status: 401 }
        );
      }

      console.log("Token verified for user in verify-token API:", user.email)

      // Get the user profile
      const { data: userProfile, error: profileError } = await supabase
        .from("users")
        .select("role")
        .eq("email", user.email)
        .single();

      if (profileError) {
        console.error("Error fetching user profile in verify-token API:", profileError)
      }

      return NextResponse.json({
        verified: true,
        user: {
          id: user.id,
          email: user.email,
          role: userProfile?.role || null
        }
      });
    } catch (authError) {
      console.error("Error verifying token in verify-token API:", authError)
      // Continue anyway in development mode
      if (process.env.NODE_ENV !== 'development') {
        return NextResponse.json(
          { error: "Authentication error" },
          { status: 401 }
        );
      } else {
        console.log("Development mode: Continuing despite auth error in verify-token API")
        return NextResponse.json({
          verified: true,
          message: "Development mode: Token verification skipped despite error"
        });
      }
    }
  } catch (error: any) {
    console.error("Unexpected error in verify-token API:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
