// Migration script to convert base64 voice data to file storage
const { createClient } = require('@supabase/supabase-js');
const { uploadVoiceMessage } = require('../utils/voice-storage');
require('dotenv').config();

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function migrateVoiceData() {
  console.log('🔄 Starting voice data migration...');
  console.log('Converting base64 audio data to file storage');
  console.log('==========================================');

  try {
    // 1. Find voice messages with base64 data but no file reference
    console.log('🔍 Finding voice messages with base64 data...');
    
    const { data: voiceMessages, error: queryError } = await supabase
      .from('communications')
      .select('id, sender_id, thread_id, audio_data, created_at')
      .eq('message_type', 'voice')
      .not('audio_data', 'is', null)
      .is('audio_file_name', null) // No file reference yet
      .order('created_at', { ascending: true });

    if (queryError) {
      throw new Error(`Failed to query voice messages: ${queryError.message}`);
    }

    if (!voiceMessages || voiceMessages.length === 0) {
      console.log('✅ No base64 voice data found to migrate');
      return;
    }

    console.log(`📋 Found ${voiceMessages.length} voice messages to migrate`);

    let migrated = 0;
    let failed = 0;

    // 2. Process each message
    for (const message of voiceMessages) {
      try {
        console.log(`🔄 Processing message ${message.id}...`);

        // Check if audio_data is base64 (starts with data:)
        if (!message.audio_data.startsWith('data:')) {
          console.log(`⏭️ Skipping message ${message.id} - not base64 data`);
          continue;
        }

        // Convert base64 to blob
        const response = await fetch(message.audio_data);
        const audioBlob = await response.blob();

        if (audioBlob.size === 0) {
          console.log(`⚠️ Skipping message ${message.id} - empty audio data`);
          continue;
        }

        // Upload to storage
        const uploadResult = await uploadVoiceMessage(
          audioBlob,
          message.sender_id,
          message.thread_id
        );

        if (!uploadResult.success) {
          throw new Error(uploadResult.error || 'Upload failed');
        }

        // Update database record
        const { error: updateError } = await supabase
          .from('communications')
          .update({
            audio_data: uploadResult.fileUrl, // Replace base64 with file URL
            audio_file_name: uploadResult.fileName,
            audio_file_size: audioBlob.size,
            audio_duration: null // We don't have duration from base64 data
          })
          .eq('id', message.id);

        if (updateError) {
          throw new Error(`Failed to update database: ${updateError.message}`);
        }

        migrated++;
        console.log(`✅ Migrated message ${message.id} (${audioBlob.size} bytes)`);

      } catch (error) {
        failed++;
        console.error(`❌ Failed to migrate message ${message.id}:`, error.message);
      }
    }

    console.log('');
    console.log('📊 Migration Summary:');
    console.log(`   - Total messages: ${voiceMessages.length}`);
    console.log(`   - Successfully migrated: ${migrated}`);
    console.log(`   - Failed: ${failed}`);

    if (migrated > 0) {
      console.log('');
      console.log('🎉 Voice data migration completed!');
      console.log('💡 Old base64 data has been replaced with file URLs');
      console.log('📁 Audio files are now stored in Supabase Storage');
    }

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

// Check if script is run directly
if (require.main === module) {
  migrateVoiceData()
    .then(() => {
      console.log('✅ Migration script finished');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Migration script error:', error);
      process.exit(1);
    });
}

module.exports = { migrateVoiceData };
