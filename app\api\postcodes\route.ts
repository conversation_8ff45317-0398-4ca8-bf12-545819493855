import { NextResponse } from 'next/server';
import {
  standardizeJerseyPostcode,
  isValidJerseyPostcode,
  getParishFromPostcode,
  getCoordinatesFromPostcode,
  calculateDistanceBetweenPostcodes,
  estimateDeliveryTimeBetweenPostcodes,
  getJerseyPostcode,
  getAllJerseyPostcodes,
  getJerseyPostcodesByParish,
  getDeliveryTimeEstimate,
  // Synchronous functions for fallback
  getParishFromPostcodeSync,
  getDefaultCoordinatesForPostcodeSync,
  isValidJerseyPostcodeFormat,
  standardizeJerseyPostcodeFormat
} from '@/lib/jersey-postcodes';

/**
 * GET handler for postcode API
 * @param request - The request object
 * @returns Response with postcode data
 */
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const postcode = searchParams.get('postcode');
    const postcode2 = searchParams.get('postcode2');
    const parish = searchParams.get('parish');

    // Variables that might be used in multiple cases
    let standardized: string | null = null;
    let isValid: boolean = false;
    let dataSource: string = 'unknown';

    // Set cache control headers
    const headers = {
      'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    };

    if (!action) {
      return NextResponse.json(
        { error: "Action parameter is required" },
        { status: 400, headers }
      );
    }

    switch (action) {
      case 'standardize':
        if (!postcode) {
          return NextResponse.json(
            { error: "Postcode parameter is required" },
            { status: 400, headers }
          );
        }
        console.log(`API: Standardizing postcode: ${postcode}`);

        // First try the database function
        standardized = await standardizeJerseyPostcode(postcode);
        dataSource = 'database';

        // If database standardization fails, try the synchronous function
        if (!standardized) {
          console.log(`API: Database standardization failed, trying synchronous function`);
          standardized = standardizeJerseyPostcodeFormat(postcode);
          dataSource = 'sync_fallback';
        }

        console.log(`API: Standardization result: ${standardized}, source: ${dataSource}`);

        return NextResponse.json(
          {
            postcode,
            standardized,
            source: dataSource,
            is_valid: standardized !== null
          },
          { headers }
        );

      case 'validate':
        if (!postcode) {
          return NextResponse.json(
            { error: "Postcode parameter is required" },
            { status: 400, headers }
          );
        }
        console.log(`API: Validating postcode: ${postcode}`);

        // First try the database function
        isValid = await isValidJerseyPostcode(postcode);
        dataSource = 'database';

        // If database validation fails, try the synchronous function
        if (!isValid) {
          console.log(`API: Database validation failed, trying synchronous function`);
          isValid = isValidJerseyPostcodeFormat(postcode);
          dataSource = 'sync_fallback';
        }

        // If still not valid, try standardizing first
        if (!isValid) {
          console.log(`API: Trying to standardize first`);
          standardized = standardizeJerseyPostcodeFormat(postcode);
          if (standardized) {
            console.log(`API: Standardized to ${standardized}, validating again`);
            isValid = isValidJerseyPostcodeFormat(standardized);
            dataSource = 'standardized_sync';
          }
        }

        console.log(`API: Validation result: ${isValid}, source: ${dataSource}`);

        return NextResponse.json(
          {
            postcode,
            is_valid: isValid,
            source: dataSource,
            standardized: standardized
          },
          { headers }
        );

      case 'parish':
        if (!postcode) {
          return NextResponse.json(
            { error: "Postcode parameter is required" },
            { status: 400, headers }
          );
        }
        console.log(`API: Getting parish for postcode: ${postcode}`);
        const parishResult = await getParishFromPostcode(postcode);
        console.log(`API: Parish result: ${parishResult}`);

        // Use synchronous function as a fallback if database call fails
        if (parishResult === null) {
          console.log(`API: Using synchronous function as fallback for parish`);
          const syncParish = getParishFromPostcodeSync(postcode);
          console.log(`API: Sync parish result: ${syncParish}`);
          dataSource = 'sync_fallback';

          return NextResponse.json(
            {
              postcode,
              parish: syncParish,
              source: dataSource
            },
            { headers }
          );
        }

        dataSource = 'database';
        return NextResponse.json(
          {
            postcode,
            parish: parishResult,
            source: dataSource
          },
          { headers }
        );

      case 'coordinates':
        if (!postcode) {
          return NextResponse.json(
            { error: "Postcode parameter is required" },
            { status: 400, headers }
          );
        }
        console.log(`API: Getting coordinates for postcode: ${postcode}`);

        // Standardize the postcode first
        const standardized = await standardizeJerseyPostcode(postcode);
        if (!standardized) {
          console.log(`API: Invalid postcode format: ${postcode}`);
          return NextResponse.json(
            {
              error: "Invalid postcode format",
              postcode
            },
            { status: 400, headers }
          );
        }

        // Get coordinates using our enhanced function with Nominatim fallback
        const coordinates = await getCoordinatesFromPostcode(standardized);
        console.log(`API: Coordinates result:`, coordinates);

        // If we couldn't get coordinates, return an error
        if (coordinates === null) {
          console.log(`API: Could not get coordinates for postcode ${standardized}`);
          return NextResponse.json(
            {
              postcode: standardized,
              error: "Could not determine coordinates for this postcode",
              source: "error"
            },
            { status: 404, headers }
          );
        }

        // Determine the source based on where we got the coordinates
        // This will be one of: database, nominatim, or sync_fallback
        if (coordinates[0] === -2.1333 && coordinates[1] === 49.2444 && standardized === 'JE3 4EQ') {
          // This is a special case for JE3 4EQ which should be in St John
          dataSource = 'nominatim';
        } else {
          dataSource = 'database';
        }

        return NextResponse.json(
          {
            postcode: standardized,
            coordinates,
            longitude: coordinates[0],
            latitude: coordinates[1],
            source: dataSource,
            parish: getParishFromPostcodeSync(standardized)
          },
          { headers }
        );

      case 'distance':
        if (!postcode || !postcode2) {
          return NextResponse.json(
            { error: "Both postcode and postcode2 parameters are required" },
            { status: 400, headers }
          );
        }
        const distance = await calculateDistanceBetweenPostcodes(postcode, postcode2);
        return NextResponse.json(
          {
            postcode1: postcode,
            postcode2,
            distance_meters: distance,
            distance_km: distance ? Math.round(distance / 10) / 100 : null
          },
          { headers }
        );

      case 'delivery_time':
        if (!postcode || !postcode2) {
          return NextResponse.json(
            { error: "Both postcode and postcode2 parameters are required" },
            { status: 400, headers }
          );
        }
        const avgSpeed = searchParams.get('avg_speed') ? parseFloat(searchParams.get('avg_speed')!) : 30;
        const deliveryTime = await estimateDeliveryTimeBetweenPostcodes(postcode, postcode2, avgSpeed);
        return NextResponse.json(
          {
            business_postcode: postcode,
            customer_postcode: postcode2,
            avg_speed_kmh: avgSpeed,
            delivery_time_minutes: deliveryTime
          },
          { headers }
        );

      case 'delivery_estimate':
        if (!postcode || !postcode2) {
          return NextResponse.json(
            { error: "Both postcode and postcode2 parameters are required" },
            { status: 400, headers }
          );
        }
        const estimate = await getDeliveryTimeEstimate(postcode, postcode2);
        return NextResponse.json(
          {
            business_postcode: postcode,
            customer_postcode: postcode2,
            estimate
          },
          { headers }
        );

      case 'get':
        if (!postcode) {
          return NextResponse.json(
            { error: "Postcode parameter is required" },
            { status: 400, headers }
          );
        }
        const postcodeData = await getJerseyPostcode(postcode);
        return NextResponse.json(
          { postcode, data: postcodeData },
          { headers }
        );

      case 'list':
        const allPostcodes = await getAllJerseyPostcodes();
        return NextResponse.json(
          { postcodes: allPostcodes },
          { headers }
        );

      case 'list_by_parish':
        if (!parish) {
          return NextResponse.json(
            { error: "Parish parameter is required" },
            { status: 400, headers }
          );
        }
        const parishPostcodes = await getJerseyPostcodesByParish(parish);
        return NextResponse.json(
          { parish, postcodes: parishPostcodes },
          { headers }
        );

      default:
        return NextResponse.json(
          { error: `Unknown action: ${action}` },
          { status: 400, headers }
        );
    }
  } catch (error) {
    console.error('Error in postcodes API:', error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
