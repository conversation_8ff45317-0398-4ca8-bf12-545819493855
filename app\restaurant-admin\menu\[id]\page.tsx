"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft, Save, Plus, Trash2, DollarSign, Star } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
// Sample menu item for the admin interface
const sampleItem = {
  id: "sample-item",
  name: "Sample Item",
  description: "This is a sample menu item description.",
  price: 12.95,
  isPopular: true,
  image: "/colorful-pasta-salad.png",
}

export default function MenuItemDetailPage({ params }: { params: { id: string } }) {
  // Unwrap params with React.use()
  const unwrappedParams = React.use(params)

  // In a real app, you would fetch the item data based on the ID
  const [item, setItem] = useState(
    sampleItem || {
      id: unwrappedParams.id,
      name: "Sample Item",
      description: "This is a sample menu item description.",
      price: 12.95,
      isPopular: true,
      image: "/colorful-pasta-salad.png",
    },
  )

  const [customizations, setCustomizations] = useState(
    sampleItem?.customizations || [
      {
        id: "sides",
        name: "Choose your side",
        required: true,
        isMultiple: false,
        options: [
          {
            id: "fries",
            name: "French Fries",
            price: 0,
          },
          {
            id: "salad",
            name: "Mixed Salad",
            price: 0,
          },
        ],
      },
    ],
  )

  const [newOption, setNewOption] = useState({
    name: "",
    price: 0,
  })

  const [newCustomizationGroup, setNewCustomizationGroup] = useState({
    name: "",
    required: false,
    isMultiple: false,
  })

  const handleItemChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setItem((prev: any) => ({ ...prev, [name]: value }))
  }

  const handleSwitchChange = (name: string, checked: boolean) => {
    setItem((prev: any) => ({ ...prev, [name]: checked }))
  }

  const handleAddOption = (groupIndex: number) => {
    if (!newOption.name) return

    const updatedCustomizations = [...customizations]
    const newId = `option-${Date.now()}`

    updatedCustomizations[groupIndex].options.push({
      id: newId,
      name: newOption.name,
      price: Number.parseFloat(newOption.price.toString()) || 0,
    })

    setCustomizations(updatedCustomizations)
    setNewOption({ name: "", price: 0 })
  }

  const handleRemoveOption = (groupIndex: number, optionIndex: number) => {
    const updatedCustomizations = [...customizations]
    updatedCustomizations[groupIndex].options.splice(optionIndex, 1)
    setCustomizations(updatedCustomizations)
  }

  const handleAddCustomizationGroup = () => {
    if (!newCustomizationGroup.name) return

    const newGroup = {
      id: `group-${Date.now()}`,
      name: newCustomizationGroup.name,
      required: newCustomizationGroup.required,
      isMultiple: newCustomizationGroup.isMultiple,
      options: [],
    }

    setCustomizations([...customizations, newGroup])
    setNewCustomizationGroup({
      name: "",
      required: false,
      isMultiple: false,
    })
  }

  const handleRemoveCustomizationGroup = (index: number) => {
    const updatedCustomizations = [...customizations]
    updatedCustomizations.splice(index, 1)
    setCustomizations(updatedCustomizations)
  }

  const handleSave = () => {
    // In a real app, you would save the item data to the database
    console.log("Saving item:", { ...item, customizations })
    // Redirect to menu page or show success message
  }

  return (
    <div>
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div className="flex items-center">
          <Link href="/restaurant-admin/menu" className="mr-4">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold">
              {item.name}
              {item.isPopular && (
                <Badge className="ml-2 bg-orange-500">
                  <Star className="mr-1 h-3 w-3" />
                  Popular
                </Badge>
              )}
            </h1>
            <p className="text-gray-500">Edit menu item details and customizations</p>
          </div>
        </div>
        <div className="mt-4 md:mt-0">
          <Button className="bg-emerald-600 hover:bg-emerald-700" onClick={handleSave}>
            <Save className="mr-2 h-4 w-4" />
            Save Changes
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Item Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">
                    Item Name
                  </Label>
                  <Input
                    id="name"
                    name="name"
                    value={item.name}
                    onChange={handleItemChange}
                    className="col-span-3"
                    required
                  />
                </div>

                <div className="grid grid-cols-4 items-start gap-4">
                  <Label htmlFor="description" className="text-right pt-2">
                    Description
                  </Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={item.description}
                    onChange={handleItemChange}
                    className="col-span-3"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="price" className="text-right">
                    Price (£)
                  </Label>
                  <div className="col-span-3 relative">
                    <DollarSign
                      className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                      size={16}
                    />
                    <Input
                      id="price"
                      name="price"
                      type="number"
                      step="0.01"
                      value={item.price}
                      onChange={handleItemChange}
                      className="pl-10"
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="image" className="text-right">
                    Image URL
                  </Label>
                  <Input
                    id="image"
                    name="image"
                    value={item.image}
                    onChange={handleItemChange}
                    className="col-span-3"
                    placeholder="https://example.com/image.jpg"
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <div className="text-right">Options</div>
                  <div className="col-span-3 space-y-2">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="isPopular"
                        checked={item.isPopular}
                        onCheckedChange={(checked) => handleSwitchChange("isPopular", checked)}
                      />
                      <Label htmlFor="isPopular">Mark as popular item</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="isAvailable" defaultChecked />
                      <Label htmlFor="isAvailable">Available for order</Label>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Customization Options</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="groups" className="w-full">
                <TabsList className="mb-4">
                  <TabsTrigger value="groups">Customization Groups</TabsTrigger>
                  <TabsTrigger value="add">Add New Group</TabsTrigger>
                </TabsList>

                <TabsContent value="groups" className="space-y-6">
                  {customizations.length === 0 ? (
                    <p className="text-center text-gray-500 py-4">No customization groups added yet.</p>
                  ) : (
                    customizations.map((group, groupIndex) => (
                      <div key={group.id} className="border rounded-lg p-4">
                        <div className="flex justify-between items-start mb-4">
                          <div>
                            <h3 className="font-medium">{group.name}</h3>
                            <div className="flex space-x-4 mt-1 text-sm text-gray-500">
                              <span>{group.required ? "Required" : "Optional"}</span>
                              <span>{group.isMultiple ? "Multiple selection" : "Single selection"}</span>
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-red-600 hover:text-red-700"
                            onClick={() => handleRemoveCustomizationGroup(groupIndex)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>

                        <div className="space-y-2 mb-4">
                          <h4 className="text-sm font-medium">Options</h4>
                          {group.options.map((option, optionIndex) => (
                            <div key={option.id} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                              <div>
                                <span>{option.name}</span>
                                {option.price > 0 && (
                                  <span className="ml-2 text-sm text-gray-500">+£{option.price.toFixed(2)}</span>
                                )}
                              </div>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0 text-red-600"
                                onClick={() => handleRemoveOption(groupIndex, optionIndex)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          ))}
                        </div>

                        <div className="flex space-x-2">
                          <Input
                            placeholder="Option name"
                            value={newOption.name}
                            onChange={(e) => setNewOption({ ...newOption, name: e.target.value })}
                            className="flex-grow"
                          />
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="Price"
                            value={newOption.price}
                            onChange={(e) =>
                              setNewOption({ ...newOption, price: Number.parseFloat(e.target.value) || 0 })
                            }
                            className="w-24"
                          />
                          <Button
                            variant="outline"
                            className="whitespace-nowrap"
                            onClick={() => handleAddOption(groupIndex)}
                          >
                            <Plus className="mr-2 h-4 w-4" />
                            Add
                          </Button>
                        </div>
                      </div>
                    ))
                  )}
                </TabsContent>

                <TabsContent value="add">
                  <div className="border rounded-lg p-4">
                    <h3 className="font-medium mb-4">Add New Customization Group</h3>
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="groupName">Group Name</Label>
                        <Input
                          id="groupName"
                          placeholder="e.g., Choose your side, Add toppings"
                          value={newCustomizationGroup.name}
                          onChange={(e) => setNewCustomizationGroup({ ...newCustomizationGroup, name: e.target.value })}
                          className="mt-1"
                        />
                      </div>

                      <div className="flex space-x-6">
                        <div className="flex items-center space-x-2">
                          <Switch
                            id="required"
                            checked={newCustomizationGroup.required}
                            onCheckedChange={(checked) =>
                              setNewCustomizationGroup({ ...newCustomizationGroup, required: checked })
                            }
                          />
                          <Label htmlFor="required">Required</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Switch
                            id="isMultiple"
                            checked={newCustomizationGroup.isMultiple}
                            onCheckedChange={(checked) =>
                              setNewCustomizationGroup({ ...newCustomizationGroup, isMultiple: checked })
                            }
                          />
                          <Label htmlFor="isMultiple">Allow multiple selections</Label>
                        </div>
                      </div>

                      <Button onClick={handleAddCustomizationGroup} className="bg-emerald-600 hover:bg-emerald-700">
                        <Plus className="mr-2 h-4 w-4" />
                        Add Customization Group
                      </Button>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="rounded-lg overflow-hidden border mb-4">
                {item.image ? (
                  <img src={item.image || "/placeholder.svg"} alt={item.name} className="w-full h-48 object-cover" />
                ) : (
                  <div className="w-full h-48 bg-gray-200 flex items-center justify-center">
                    <span className="text-gray-400">No Image</span>
                  </div>
                )}
                <div className="p-4">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-semibold">{item.name}</h3>
                    <Badge variant="outline" className="bg-emerald-50 text-emerald-700 border-emerald-200">
                      £{Number.parseFloat(item.price.toString()).toFixed(2)}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-500 mb-3">{item.description}</p>
                  {item.isPopular && (
                    <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                      <Star className="mr-1 h-3 w-3" />
                      Popular
                    </Badge>
                  )}
                </div>
              </div>

              <div className="text-sm text-gray-500">
                <p>This is how your item will appear to customers in the app.</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
