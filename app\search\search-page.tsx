'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { Search, MapPin, Filter, X, Menu, ChevronDown, Clock, Star, AlertTriangle } from 'lucide-react'
import { useLocation } from '@/context/location-context'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import SearchFilters from '@/components/filters/search-filters'
import MobileSearchPage from '@/components/mobile-search/mobile-search-page'
import SquareBusinessCard from '@/components/square-business-card'
import PortraitBusinessCard from '@/components/portrait-business-card'
import UnifiedBusinessCard from '@/components/unified-business-card'
import FallbackImage from '@/components/fallback-image'
import PortraitDealsSection from '@/app/components/portrait-deals-section'
import CategoriesAndFilters from '@/components/search/categories-and-filters'
import {
  getAllRestaurants,
  getAllShops,
  getAllBusinessesByType
} from '@/services/business-service-direct'
import { getBusinessTypeColors } from '@/utils/business-colors'
import { getHomepageData } from '@/services/optimized-home-service-direct'
import { useAttributeFilters } from '@/hooks/use-attribute-filters'
import { parseAttributeFilterId } from '@/services/attribute-service-direct'
import type { Restaurant, Shop } from '@/types/business'
import { isValidJerseyPostcodeFormat, standardizeJerseyPostcodeFormat } from '@/lib/jersey-postcodes'

export default function SearchPage() {
  const searchParams = useSearchParams()
  const initialQuery = searchParams.get('q') || ''
  const initialType = searchParams.get('type') || 'all'
  const initialPostcodeParam = searchParams.get('postcode') || ''

  // Use the location context to access stored postcode and coordinates
  const {
    postcode: contextPostcode,
    setPostcode: setContextPostcode,
    coordinates,
    geocodePostcode,
    userId
  } = useLocation()

  const [searchQuery, setSearchQuery] = useState(initialQuery)
  const [activeType, setActiveType] = useState(initialType)
  const [businesses, setBusinesses] = useState<(Restaurant | Shop)[]>([])
  const [filteredBusinesses, setFilteredBusinesses] = useState<(Restaurant | Shop)[]>([])
  const [businessesWithDeals, setBusinessesWithDeals] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [activeFilters, setActiveFilters] = useState<string[]>([])
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 50])
  const [maxDeliveryTime, setMaxDeliveryTime] = useState<number>(30)
  const [isMobile, setIsMobile] = useState<boolean>(false)

  // State for postcode dialog
  const [postcodeDialogOpen, setPostcodeDialogOpen] = useState(false);
  const [newPostcode, setNewPostcode] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);

  // Function to validate Jersey postcode format
  const isValidJerseyPostcode = (postcode: string): boolean => {
    // Use our new standardized function
    return isValidJerseyPostcodeFormat(postcode);
  };

  // Format a Jersey postcode to ensure it has a space
  const formatJerseyPostcode = (postcode: string): string => {
    // Use our new standardized function
    const standardized = standardizeJerseyPostcodeFormat(postcode);
    return standardized || postcode.trim().toUpperCase();
  };

  // Handle proceeding with default coordinates
  const handleProceedWithDefault = () => {
    setIsSubmitting(true);
    try {
      // Use the invalid postcode but let the system use default coordinates
      const trimmedPostcode = newPostcode.trim();
      console.log('Using default coordinates with invalid postcode:', trimmedPostcode);

      // Update the context with the invalid postcode
      // The location context will handle using default coordinates
      setContextPostcode(trimmedPostcode);

      // Try to geocode the postcode (will use default coordinates)
      geocodePostcode(trimmedPostcode);

      // Close the dialog
      setPostcodeDialogOpen(false);

      // Reset the form
      setNewPostcode('');
      setValidationError(null);

      // Update URL with the postcode without page reload
      const url = new URL(window.location.href);
      url.searchParams.set('postcode', trimmedPostcode);
      window.history.pushState({}, '', url.toString());

      // Force a reload of the businesses to recalculate delivery times
      setFilteredBusinesses([...filteredBusinesses]);
    } catch (error) {
      console.error('Error proceeding with default coordinates:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle postcode submission
  const handlePostcodeSubmit = async () => {
    if (!newPostcode.trim()) return;

    setValidationError(null);
    setIsSubmitting(true);

    try {
      // Validate the postcode format
      const trimmedPostcode = newPostcode.trim();

      if (!isValidJerseyPostcode(trimmedPostcode)) {
        setValidationError(`"${trimmedPostcode}" is not a valid Jersey postcode. Jersey postcodes should be in the format JE1 1AA, JE2 3BT, etc. Default Jersey coordinates will be used to calculate delivery times and fee.`);
        setIsSubmitting(false);
        return;
      }

      // Format the postcode properly
      const formattedPostcode = formatJerseyPostcode(trimmedPostcode);
      console.log('Updating postcode to:', formattedPostcode);

      // Save to session and update context
      const response = await fetch('/api/user/location', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ postcode: formattedPostcode }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save location');
      }

      // Update the context with the new postcode
      setContextPostcode(formattedPostcode);

      // Close the dialog
      setPostcodeDialogOpen(false);

      // Reset the form
      setNewPostcode('');
      setValidationError(null);

      // Update URL with the new postcode without page reload
      const url = new URL(window.location.href);
      url.searchParams.set('postcode', formattedPostcode);
      window.history.pushState({}, '', url.toString());

      // Force a reload of the businesses to recalculate delivery times
      setFilteredBusinesses([...filteredBusinesses]);
    } catch (error) {
      console.error('Error updating postcode:', error);
      setValidationError('Failed to save your location. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Sync postcode from URL parameters and try to geocode it if needed
  useEffect(() => {
    const syncPostcodeFromUrlOrSession = async () => {
      try {
        // First check if we have a postcode in the URL
        if (initialPostcodeParam && initialPostcodeParam !== contextPostcode) {
          console.log(`Validating postcode from URL: ${initialPostcodeParam}`);

          // Validate the postcode format
          if (isValidJerseyPostcode(initialPostcodeParam)) {
            // Format the postcode properly
            const formattedPostcode = formatJerseyPostcode(initialPostcodeParam);
            console.log(`Setting postcode from URL: ${formattedPostcode}`);

            // Save to session and update context
            await fetch('/api/user/location', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ postcode: formattedPostcode }),
            });

            setContextPostcode(formattedPostcode);

            // If we don't have coordinates for this postcode, try to geocode it
            if (!coordinates) {
              geocodePostcode(formattedPostcode).catch(error => {
                console.error('Error geocoding postcode from URL:', error);
              });
            }

            // Force a reload of the businesses to recalculate delivery times
            setFilteredBusinesses([...filteredBusinesses]);
          } else {
            console.warn(`Invalid postcode format in URL: ${initialPostcodeParam}`);
            // Show the postcode dialog with validation error
            setNewPostcode(initialPostcodeParam);
            setValidationError(`"${initialPostcodeParam}" is not a valid Jersey postcode. Jersey postcodes should be in the format JE1 1AA, JE2 3BT, etc. Default Jersey coordinates will be used to calculate delivery times and fee.`);
            setPostcodeDialogOpen(true);
          }
        }
        // If no postcode in URL, check session
        else if (!contextPostcode) {
          console.log('Checking for postcode in session');
          const response = await fetch('/api/user/location');
          const data = await response.json();

          if (data.location?.postcode) {
            console.log(`Found postcode in session: ${data.location.postcode}`);
            setContextPostcode(data.location.postcode);

            // If we don't have coordinates, try to geocode it
            if (!coordinates && data.location.coordinates) {
              console.log('Using coordinates from session');
              // Here we would update the coordinates in the location context
              // This depends on how your location context is implemented
            } else if (!coordinates) {
              geocodePostcode(data.location.postcode).catch(error => {
                console.error('Error geocoding postcode from session:', error);
              });
            }
          } else {
            console.log('No postcode in session. Showing postcode dialog.');
            setPostcodeDialogOpen(true);
          }
        }
      } catch (error) {
        console.error('Error syncing postcode:', error);
        // If all else fails, show the postcode dialog
        setPostcodeDialogOpen(true);
      }
    };

    syncPostcodeFromUrlOrSession();
  }, [initialPostcodeParam, contextPostcode, coordinates, geocodePostcode, setContextPostcode, filteredBusinesses]);

  // Check if we're on mobile/tablet
  useEffect(() => {
    const checkIfMobile = () => {
      const width = window.innerWidth
      const isMobileView = width < 1024
      console.log(`Screen width: ${width}px, isMobile: ${isMobileView}`)
      setIsMobile(isMobileView)
    }

    // Initial check
    checkIfMobile()

    // Add event listener for window resize
    window.addEventListener('resize', checkIfMobile)

    // Cleanup
    return () => window.removeEventListener('resize', checkIfMobile)
  }, [])

  // Function to get placeholder text based on business type
  const getPlaceholderText = (type: string) => {
    switch (type) {
      case 'restaurant':
        return 'Search by name, location, or cuisine...'
      case 'shop':
        return 'Search by name, location, or product...'
      case 'pharmacy':
        return 'Search by name, location, or product...'
      case 'cafe':
        return 'Search by name, location, or menu item...'
      case 'errand':
        return 'Search by name, location, or service type...'
      default:
        return 'Search by name, location, or business type...'
    }
  }

  // Fetch all businesses on mount or when postcode changes
  useEffect(() => {
    async function fetchBusinesses() {
      setLoading(true)
      try {
        let results: any[] = []

        // Fetch businesses with deals for the carousel
        const { businessesWithDeals: dealsData } = await getHomepageData();
        setBusinessesWithDeals(dealsData);

        // Ensure we have a postcode
        if (!contextPostcode) {
          console.warn('No postcode available in context. Delivery calculations will use defaults.');
        }

        // Log the postcode for debugging
        console.log(`Fetching businesses with postcode: ${contextPostcode || 'NONE PROVIDED'}`);

        if (activeType === 'all') {
          // Fetch all business types
          const restaurants = await getAllRestaurants(contextPostcode, userId || undefined)
          const shops = await getAllShops(contextPostcode, userId || undefined)
          // Use type assertions for business types that TypeScript doesn't recognize
          const pharmacies = await getAllBusinessesByType('pharmacy' as any, contextPostcode, userId || undefined)
          const cafes = await getAllBusinessesByType('cafe' as any, contextPostcode, userId || undefined)
          const errands = await getAllBusinessesByType('errand', contextPostcode, userId || undefined)

          results = [
            ...restaurants,
            ...shops,
            ...pharmacies,
            ...cafes,
            ...errands
          ]
        } else if (activeType === 'restaurant') {
          results = await getAllRestaurants(contextPostcode, userId || undefined)
        } else if (activeType === 'shop') {
          results = await getAllShops(contextPostcode, userId || undefined)
        } else if (['pharmacy', 'cafe', 'errand'].includes(activeType)) {
          // Use the generic function for other business types
          results = await getAllBusinessesByType(activeType as any, contextPostcode, userId || undefined)
        } else {
          results = []
        }

        setBusinesses(results)
      } catch (error) {
        console.error('Error fetching businesses:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchBusinesses()
  }, [activeType, contextPostcode, userId]) // Add contextPostcode and userId as dependencies

  // Refresh businesses when postcode changes to update delivery times
  useEffect(() => {
    if (businesses.length > 0 && contextPostcode) {
      // Force a re-render of the businesses to recalculate delivery times
      // We're using a timeout to ensure this doesn't cause render issues
      const timer = setTimeout(() => {
        // Create a fresh copy of the businesses array to trigger recalculation
        setFilteredBusinesses(businesses.slice());
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [contextPostcode]);

  // Filter businesses based on search query
  useEffect(() => {
    if (!businesses.length) {
      setFilteredBusinesses([])
      return
    }

    const filtered = businesses.filter(business => {
      const matchesSearch =
        searchQuery === '' ||
        business.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        business.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
        hasMatchingAttribute(business, searchQuery)

      return matchesSearch
    })

    // If we have active filters, apply them
    if (activeFilters.length > 0) {
      handleFiltersChange(activeFilters)
    } else {
      setFilteredBusinesses(filtered)
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [businesses, searchQuery, activeFilters.length])

  // Handle search form submission
  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault()

    // Update URL with search parameters without page reload
    const url = new URL(window.location.href)
    url.searchParams.set('q', searchQuery)
    url.searchParams.set('type', activeType)
    if (contextPostcode) {
      url.searchParams.set('postcode', contextPostcode)
    }
    window.history.pushState({}, '', url.toString())
  }

  // Handle business type change
  const handleTypeChange = (type: string) => {
    setActiveType(type)
    // Update URL with new type
    const url = new URL(window.location.href)
    url.searchParams.set('type', type)
    window.history.pushState({}, '', url.toString())
  }

  // Get attribute filter functionality
  const { filterBusinessesByAttributes } = useAttributeFilters(activeType)

  // Handle filter changes
  const handleFiltersChange = (filters: string[]) => {
    setActiveFilters(filters)

    // Apply filters to businesses
    if (filters.length === 0) {
      // If no filters, reset to original filtered businesses
      setFilteredBusinesses(
        businesses.filter(business =>
          searchQuery === '' ||
          business.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          business.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
          hasMatchingAttribute(business, searchQuery)
        )
      )
    } else {
      // Apply attribute filters
      const filteredByAttributes = filterBusinessesByAttributes(
        businesses.filter(business =>
          searchQuery === '' ||
          business.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          business.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
          hasMatchingAttribute(business, searchQuery)
        ),
        filters
      )

      // Apply price range filter
      const filteredByPrice = filteredByAttributes.filter(business => {
        // Check if business has a price in the range
        // For now, we'll use the delivery fee as a proxy for price
        return business.deliveryFee >= priceRange[0] && business.deliveryFee <= priceRange[1]
      })

      // Apply delivery time filter
      const filteredByTime = filteredByPrice.filter(business => {
        // Check if business has a delivery time less than or equal to maxDeliveryTime
        return (business.deliveryTime || 30) <= maxDeliveryTime
      })

      setFilteredBusinesses(filteredByTime)
    }
  }

  // Handle price range changes
  const handlePriceRangeChange = (range: [number, number]) => {
    setPriceRange(range)

    // Re-apply all filters with the new price range
    handleFiltersChange(activeFilters)
  }

  // Handle delivery time changes
  const handleMaxDeliveryTimeChange = (time: number) => {
    setMaxDeliveryTime(time)

    // Re-apply all filters with the new delivery time
    handleFiltersChange(activeFilters)
  }

  // Helper function to check if a business has attributes matching the search query
  const hasMatchingAttribute = (business: any, query: string): boolean => {
    if (!query) return false

    const lowerQuery = query.toLowerCase()

    if ('cuisines' in business && business.cuisines) {
      return business.cuisines.some((cuisine: string) =>
        cuisine.toLowerCase().includes(lowerQuery)
      )
    }
    if ('storeTypes' in business && business.storeTypes) {
      return business.storeTypes.some((type: string) =>
        type.toLowerCase().includes(lowerQuery)
      )
    }
    if ('serviceTypes' in business && business.serviceTypes) {
      return (business.serviceTypes as string[]).some((type: string) =>
        type.toLowerCase().includes(lowerQuery)
      )
    }
    if ('errandTypes' in business && business.errandTypes) {
      return (business.errandTypes as string[]).some((type: string) =>
        type.toLowerCase().includes(lowerQuery)
      )
    }
    return false
  }

  return (
    <div className="container-fluid px-2 sm:px-4">
      {/* Postcode Dialog */}
      <Dialog open={postcodeDialogOpen} onOpenChange={setPostcodeDialogOpen}>
        <DialogContent className="max-w-[95vw] sm:max-w-md p-4 sm:p-6">
          <DialogTitle className="sr-only">{validationError ? "Invalid Postcode" : "Enter Your Postcode"}</DialogTitle>
          <div className="absolute right-4 top-4">
            <button
              onClick={() => setPostcodeDialogOpen(false)}
              className="rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </button>
          </div>
          {validationError ? (
            <div className="flex flex-col gap-4">
              <div className="flex items-start gap-3">
                <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />
                <div>
                  <h3 className="text-lg font-semibold text-red-600">Invalid Postcode</h3>
                </div>
              </div>
              <p className="text-gray-600 mt-1 pl-8">
                {validationError}
              </p>
            </div>
          ) : (
            <DialogHeader>
              <DialogTitle>Enter Your Postcode</DialogTitle>
            </DialogHeader>
          )}
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="postcode">Please enter your postcode to see delivery times and fees</Label>
              <div className="relative">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="postcode"
                  placeholder="e.g. JE2 3NG"
                  className={`pl-10 ${validationError ? 'border-red-300 focus-visible:ring-red-500 focus-visible:border-red-500' : ''}`}
                  value={newPostcode}
                  onChange={(e) => {
                    setNewPostcode(e.target.value);
                    if (validationError) setValidationError(null);
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handlePostcodeSubmit();
                    }
                  }}
                />
              </div>
              <p className="text-xs text-gray-500">
                Enter a Jersey postcode (e.g., JE2 3NG) to see delivery times
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Jersey postcodes must be in the format JE1-4 followed by a space, a digit, and two letters
              </p>

              {validationError && (
                <div className="flex items-center justify-between mt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setValidationError(null)}
                    className="text-xs"
                  >
                    Try again
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleProceedWithDefault}
                    className="bg-emerald-600 hover:bg-emerald-700 text-xs"
                  >
                    Use default location
                  </Button>
                </div>
              )}
            </div>
          </div>
          {!validationError ? (
            <DialogFooter>
              <Button
                onClick={handlePostcodeSubmit}
                disabled={isSubmitting || !newPostcode.trim()}
                className="bg-emerald-600 hover:bg-emerald-700 text-white"
              >
                {isSubmitting ? "Updating..." : "Continue"}
              </Button>
            </DialogFooter>
          ) : (
            <div className="flex flex-col sm:flex-row gap-2 pt-4 w-full justify-center">
              <Button
                variant="outline"
                onClick={() => setValidationError(null)}
                className="w-[90%] mx-auto sm:w-[45%] text-[11px] sm:text-sm px-1 sm:px-2 whitespace-nowrap"
                size="sm"
                style={{ minHeight: "36px" }}
              >
                Enter a different postcode
              </Button>
              <Button
                onClick={handleProceedWithDefault}
                className="w-[90%] mx-auto sm:w-[55%] bg-emerald-600 hover:bg-emerald-700 text-[11px] sm:text-sm px-1 sm:px-2 whitespace-nowrap"
                size="sm"
                style={{ minHeight: "36px" }}
              >
                Continue with default location
              </Button>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {isMobile ? (
        // Mobile/Tablet View
        <>
          {console.log('Main search page - passing to mobile view:', {
            initialQuery: searchQuery,
            initialType: activeType,
            businessesCount: filteredBusinesses.length,
            businesses: filteredBusinesses
          })}
          <MobileSearchPage
            initialQuery={searchQuery}
            initialType={activeType}
            businesses={filteredBusinesses}
            onTypeChange={handleTypeChange}
          />
        </>
      ) : (
        // Desktop View
        <div className="min-h-screen bg-white">




          {/* Categories and Filters */}
          <div className="bg-white border-b border-gray-200">
            <div>
              <CategoriesAndFilters
                onCategorySelect={(category) => {
                  console.log('Category selected:', category)
                  // Implement category filtering logic here
                }}
                onFilterSelect={(filter) => {
                  console.log('Filter selected:', filter)
                  // Implement filter logic here
                }}
                resultsCount={!loading ? filteredBusinesses.length : undefined}
                businessType={activeType}
              />
            </div>
          </div>

          {/* Main content container with padding */}
          <div className="py-6">
            {/* Main content with sidebar layout for desktop */}
            <div className="flex flex-col lg:flex-row gap-6">
              {/* Sidebar with sticky positioning */}
              <div className="hidden lg:block lg:w-[280px]">
                <div className="sticky top-[calc(4rem+3rem)] max-h-[calc(100vh-4rem-3rem-2rem)] overflow-y-auto">
                  <div className="bg-white rounded-lg shadow-sm border border-gray-100">
                    <SearchFilters
                      businessType={activeType}
                      onFiltersChange={handleFiltersChange}
                      onPriceRangeChange={handlePriceRangeChange}
                      onMaxDeliveryTimeChange={handleMaxDeliveryTimeChange}
                      onTypeChange={handleTypeChange}
                    />
                  </div>
                </div>
              </div>

              {/* Main Content */}
              <div className="lg:w-3/4">
                {/* Deals Section */}
                {!loading && businessesWithDeals.length > 0 && (
                  <div className="mb-8">
                    <PortraitDealsSection businesses={businessesWithDeals} />
                  </div>
                )}

                {/* Search Results */}
                {loading ? (
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {[...Array(8)].map((_, i) => (
                      <div key={i} className="bg-white rounded-lg shadow-md overflow-hidden animate-pulse">
                        <div className="h-48 bg-gray-200"></div>
                        <div className="p-4">
                          <div className="h-5 bg-gray-200 rounded mb-2"></div>
                          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                          <div className="flex justify-between">
                            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : filteredBusinesses.length === 0 ? (
                  <div className="text-center py-12 bg-white rounded-lg shadow-sm border border-gray-100">
                    <p className="text-gray-600 mb-4">No results found matching your criteria</p>
                    <Button
                      variant="outline"
                      className="bg-white hover:bg-gray-50 border-[#22c55e] text-[#22c55e]"
                      onClick={() => {
                        setSearchQuery('')
                        setActiveType('all')
                      }}
                    >
                      Clear Filters
                    </Button>
                  </div>
                ) : (
                  <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 grid-auto-rows-fr">
                    {filteredBusinesses.map((business, index) => {
                      // Determine business type for URL
                      let businessType = 'restaurant';
                      if ('storeTypes' in business) businessType = 'shop';
                      else if ('cuisines' in business) businessType = 'restaurant';

                      // Determine if we should use square card (for logo_url)
                      // Check both image_url (which should contain logo_url if available) and direct logo_url property
                      const hasLogo = ('image_url' in business && business.image_url) ||
                                     ('logo_url' in business && business.logo_url);

                      return (
                        <UnifiedBusinessCard
                          key={business.id}
                          business={business}
                          index={index}
                          enableRealTimeUpdates={false} // Search page uses cached values for performance
                        />
                      );
                    })}
                  </div>
                )}

                {/* Add custom animation styles */}
                <style jsx global>{`
                  @keyframes fadeIn {
                    from { opacity: 0; transform: translateY(10px); }
                    to { opacity: 1; transform: translateY(0); }
                  }

                  .animate-fadeIn {
                    animation: fadeIn 0.5s ease-out forwards;
                  }

                  .animation-delay-100 { animation-delay: 100ms; }
                  .animation-delay-200 { animation-delay: 200ms; }
                  .animation-delay-300 { animation-delay: 300ms; }
                  .animation-delay-400 { animation-delay: 400ms; }
                  .animation-delay-500 { animation-delay: 500ms; }
                  .animation-delay-600 { animation-delay: 600ms; }
                `}</style>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
