import { NextRequest, NextResponse } from "next/server"
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"
import { Database } from "@/types/supabase"
import { createAdminClient } from "@/lib/supabase-admin"

export async function PATCH(request: NextRequest) {
  try {
    // Create a Supabase admin client to have proper permissions
    const supabase = createAdminClient()

    // Parse the request body
    const body = await request.json()
    const { orderId, preparationTime, sendNotification = true } = body

    // Validate input
    if (!orderId) {
      return NextResponse.json({ error: "Order ID is required" }, { status: 400 })
    }

    if (preparationTime === undefined) {
      return NextResponse.json({ error: "Preparation time must be provided" }, { status: 400 })
    }

    // Prepare update data
    const updateData = {
      preparation_time: preparationTime
    }

    // Update the order
    const { data: order, error } = await supabase
      .from("orders")
      .update(updateData)
      .eq("id", orderId)
      .select("*")
      .single()

    if (error) {
      console.error("Error updating order preparation time:", error)
      return NextResponse.json(
        { error: "Failed to update order preparation time", details: error.message },
        { status: 500 }
      )
    }

    // If notification is requested, add a record to the notifications table
    if (sendNotification && order) {
      try {
        // Determine notification message based on delivery type
        let notificationMessage = ""

        if (order.delivery_type === "pickup") {
          notificationMessage = `Your order is being prepared and will be ready for pickup in ${preparationTime} minutes.`
        } else {
          notificationMessage = `Your order is being prepared and will be ready for our driver in ${preparationTime} minutes.`
        }

        // Insert notification
        const { error: notificationError } = await supabase
          .from("order_notifications")
          .insert({
            order_id: orderId,
            message: notificationMessage,
            type: "time_update",
            status: "pending",
            created_at: new Date().toISOString()
          })

        if (notificationError) {
          console.error("Error creating notification:", notificationError)
          // Don't fail the whole operation if just the notification fails
        }
      } catch (notificationError) {
        console.error("Error in notification creation:", notificationError)
        // Don't fail the whole operation if just the notification fails
      }
    }

    return NextResponse.json({
      success: true,
      message: "Order preparation time updated successfully",
      order
    })

  } catch (error: any) {
    console.error("Error in PATCH /api/business-admin/orders/time-estimates:", error)
    return NextResponse.json(
      { error: "An unexpected error occurred", details: error?.message || "Unknown error" },
      { status: 500 }
    )
  }
}
