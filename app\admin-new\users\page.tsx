"use client"

import { useState, use<PERSON>ffect, use<PERSON>allback } from "react"
import { useRout<PERSON> } from "next/navigation"
import Link from "next/link"
import { format } from "date-fns"
import {
  AlertCircle,
  ArrowDown,
  ArrowUp,
  Clock,
  Download,
  Filter,
  Mail,
  Package2,
  Search,
  ShoppingBag,
  SlidersHorizontal,
  Star,
  Store,
  TrendingUp,
  User,
  UserPlus,
  Users,
} from "lucide-react"
import { UserDetailsDialog } from "@/components/user-details-dialog"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>rigger } from "@/components/ui/tabs"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useAuthDirect } from "@/context/auth-context-direct"
import { useSupabaseRealtime } from "@/hooks/use-supabase-realtime"
import { supabase } from "@/lib/supabase"

interface UserProfile {
  // Required fields from the schema
  id: number
  name: string
  email: string
  created_at: string
  updated_at: string

  // Optional fields from the schema
  address?: string
  phone?: string
  coordinates?: any
  postcode?: string
  first_name?: string
  last_name?: string
  role?: 'customer' | 'business_staff' | 'business_manager' | 'admin' | 'super_admin'
  auth_id?: string

  // Computed properties (not in the schema)
  total_orders?: number
  total_spent?: number
}

export default function AdminUsersPage() {
  const router = useRouter()
  const { user, isAdmin, isLoading: authLoading } = useAuthDirect()
  const [authChecked, setAuthChecked] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [sortBy, setSortBy] = useState("recent")
  const [filteredUsers, setFilteredUsers] = useState<UserProfile[]>([])
  // Explicitly set dataFetched to false initially to ensure data is loaded on mount
  const [dataFetched, setDataFetched] = useState(false)

  // User details dialog state
  const [isUserDetailsOpen, setIsUserDetailsOpen] = useState(false)
  const [selectedUser, setSelectedUser] = useState<UserProfile | null>(null)

  // Fetch users data with direct query using authenticated client
  const [users, setUsers] = useState<UserProfile[]>([])
  const [usersLoading, setUsersLoading] = useState(false) // Start with false to prevent unnecessary loading states
  const [usersError, setUsersError] = useState<Error | null>(null)

  // Function to fetch users
  const fetchUsers = useCallback(async () => {
    try {
      console.log('Fetching users...', {
        dataFetched,
        usersLoading,
        authChecked,
        timestamp: new Date().toISOString(),
        caller: new Error().stack?.split('\n')[2]?.trim() || 'unknown'
      });

      // If already loading, don't start another fetch
      if (usersLoading) {
        console.log('Already loading users, skipping this fetch request');
        return;
      }

      setUsersLoading(true)

      // Check if we should force using the API endpoint
      let forceApi = false;
      if (typeof window !== 'undefined') {
        const urlParams = new URLSearchParams(window.location.search);
        forceApi = urlParams.get('force_api') === 'true';
      }

      // Get the authentication token from localStorage
      let token = null;
      if (typeof window !== 'undefined') {
        try {
          token = localStorage.getItem('loop_jersey_auth_token');
        } catch (e) {
          console.error('Error accessing localStorage:', e);
        }
      }

      console.log('Fetching users with', forceApi ? 'API endpoint' : 'direct Supabase query');

      if (forceApi) {
        // Use the API endpoint with retry logic
        const fetchApiWithRetry = async (retryCount = 0, maxRetries = 2) => {
          try {
            console.log(`API fetch attempt ${retryCount + 1} of ${maxRetries + 1}`);

            // Create a new AbortController for each attempt
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

            const response = await fetch(`/api/admin/users-direct?t=${Date.now()}`, {
              headers: {
                'Authorization': token ? `Bearer ${token}` : '',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
              },
              signal: controller.signal
            });

            clearTimeout(timeoutId);
            return response;
          } catch (error) {
            if (error.name === 'AbortError') {
              console.error(`API fetch attempt ${retryCount + 1} timed out`);
            } else {
              console.error(`API fetch attempt ${retryCount + 1} failed:`, error);
            }

            // If we've reached max retries, throw the error
            if (retryCount >= maxRetries) {
              throw error;
            }

            // Otherwise, wait with exponential backoff and retry
            const backoffTime = Math.min(1000 * Math.pow(2, retryCount), 5000);
            console.log(`Retrying API fetch in ${backoffTime}ms...`);
            await new Promise(resolve => setTimeout(resolve, backoffTime));

            // Retry with incremented count
            return fetchApiWithRetry(retryCount + 1, maxRetries);
          }
        };

        const response = await fetchApiWithRetry();

        console.log('API response status:', response.status);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Error fetching users from API:', response.status, errorText);
          setUsersError(new Error(`API error: ${response.status} ${errorText}`));
          setUsers([]);
          return;
        }

        const result = await response.json();
        console.log('API response data:', {
          usersCount: result.users?.length || 0,
          firstFewUsers: result.users?.slice(0, 3)
        });

        if (result.error) {
          console.error('API returned error:', result.error);
          setUsersError(new Error(result.error));
          setUsers([]);
        } else {
          console.log('Successfully fetched users from API:', result.users?.length || 0);
          setUsers(result.users || []);
          setUsersError(null);
        }
      } else {
        // Use direct Supabase query with retry logic
        const fetchWithRetry = async (retryCount = 0, maxRetries = 2) => {
          try {
            console.log(`Supabase query attempt ${retryCount + 1} of ${maxRetries + 1}`);
            return await supabase
              .from('users')
              .select('id, name, email, created_at, updated_at, address, phone, postcode, first_name, last_name, role, auth_id')
              .order('created_at', { ascending: false });
          } catch (error) {
            console.error(`Supabase query attempt ${retryCount + 1} failed:`, error);

            // If we've reached max retries, throw the error
            if (retryCount >= maxRetries) {
              throw error;
            }

            // Otherwise, wait with exponential backoff and retry
            const backoffTime = Math.min(1000 * Math.pow(2, retryCount), 5000);
            console.log(`Retrying Supabase query in ${backoffTime}ms...`);
            await new Promise(resolve => setTimeout(resolve, backoffTime));

            // Retry with incremented count
            return fetchWithRetry(retryCount + 1, maxRetries);
          }
        };

        const { data, error } = await fetchWithRetry();

        console.log('Supabase query result:', { count: data?.length || 0, error });

        if (error) {
          console.error('Error fetching users from Supabase:', error);
          setUsersError(new Error(error.message));
          setUsers([]);
        } else {
          console.log('Successfully fetched users from Supabase:', data?.length || 0);
          console.log('First few users:', data?.slice(0, 3));
          setUsers(data || []);
          setUsersError(null);
        }
      }
    } catch (err) {
      console.error('Exception fetching users:', err);
      setUsersError(err instanceof Error ? err : new Error('Unknown error fetching users'));
      setUsers([]);
    } finally {
      setUsersLoading(false);
      setDataFetched(true);
    }
  }, [])

  // Fetch users when auth is checked or after a timeout
  useEffect(() => {
    // If data has already been fetched or is currently loading, don't fetch again
    if (dataFetched || usersLoading) {
      return
    }

    // If auth is checked, fetch users immediately
    if (authChecked) {
      console.log("Auth checked, fetching users")
      fetchUsers()
      return
    }

    // If auth is taking too long, use a fallback approach
    const authTimeoutId = setTimeout(() => {
      if (!authChecked && !dataFetched && !usersLoading) {
        console.log("Auth check taking too long, proceeding with user fetch anyway")
        fetchUsers()
      }
    }, 3000) // 3 second timeout for auth check

    return () => clearTimeout(authTimeoutId)
  }, [authChecked, usersLoading, dataFetched, fetchUsers])

  // Refetch function for retry button
  const refetchUsers = () => {
    setDataFetched(false)
    setUsersLoading(true)
    fetchUsers()
  }

  // Ensure data is fetched on component mount if auth is already checked
  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => {
    if (authChecked && !dataFetched && !usersLoading) {
      console.log("Component mounted with auth already checked, fetching data")
      fetchUsers()
    }
  }, []) // Empty dependency array ensures this runs once on mount

  // This effect runs whenever authChecked changes to true
  useEffect(() => {
    if (authChecked && !dataFetched && !usersLoading) {
      console.log("Auth check just completed, triggering data fetch")
      fetchUsers()
    }
  }, [authChecked, dataFetched, usersLoading, fetchUsers])

  // First, check authentication before showing any data
  useEffect(() => {
    // Skip if still loading auth state
    if (authLoading) return

    // If auth has been loading for more than 5 seconds, proceed anyway with default admin access
    // This is a fallback for when auth times out
    const authTimeoutId = setTimeout(() => {
      if (authLoading && !authChecked) {
        console.log("AdminUsersPage: Auth check taking too long, proceeding with default admin access")
        setAuthChecked(true)

        // Immediately trigger data fetch
        if (!dataFetched && !usersLoading) {
          console.log("Auth timeout, triggering data fetch anyway")
          fetchUsers()
        }
      }
    }, 5000) // 5 second timeout for auth check

    // If we have auth info and user is not admin, redirect immediately
    if (!authLoading && (!user || !isAdmin)) {
      console.log("AdminUsersPage: User is not authorized, redirecting to login")
      router.push("/login?redirectTo=/admin-new/users")
      return
    }

    // If user is admin, mark auth as checked
    if (!authLoading && user && isAdmin && !authChecked) {
      console.log("AdminUsersPage: User is authorized, proceeding to fetch data")
      setAuthChecked(true)

      // Immediately trigger data fetch if not already fetched
      if (!dataFetched && !usersLoading) {
        console.log("Auth check complete, triggering immediate data fetch")
        fetchUsers()
      }
    }

    return () => clearTimeout(authTimeoutId)
  }, [user, isAdmin, authLoading, router, authChecked, dataFetched, usersLoading, fetchUsers])

  // Apply filters to users
  useEffect(() => {
    if (!users) return

    let filtered = [...users]

    // Apply search filter using the actual schema fields
    if (searchQuery) {
      const searchLower = searchQuery.toLowerCase()
      filtered = filtered.filter(
        (user) =>
          (user.name && user.name.toLowerCase().includes(searchLower)) ||
          (user.first_name && user.first_name.toLowerCase().includes(searchLower)) ||
          (user.last_name && user.last_name.toLowerCase().includes(searchLower)) ||
          (user.email && user.email.toLowerCase().includes(searchLower)) ||
          (user.phone && user.phone.includes(searchLower))
      )
    }

    // Apply status filter using the role field
    if (statusFilter !== "all") {
      if (statusFilter === "admin") {
        filtered = filtered.filter((user) =>
          user.role === 'admin' || user.role === 'super_admin'
        )
      } else if (statusFilter === "business") {
        filtered = filtered.filter((user) =>
          user.role === 'business_manager' || user.role === 'business_staff'
        )
      } else if (statusFilter === "customer") {
        filtered = filtered.filter((user) =>
          user.role === 'customer' || !user.role
        )
      } else if (statusFilter === "recent") {
        // Users who signed up in the last 7 days
        const sevenDaysAgo = new Date()
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
        filtered = filtered.filter((user) =>
          user.created_at && new Date(user.created_at) >= sevenDaysAgo
        )
      }
    }

    // Apply sorting using the name field
    if (sortBy === "name-asc") {
      filtered.sort((a, b) => {
        const nameA = a.name || `${a.first_name || ''} ${a.last_name || ''}`.trim() || a.email || ''
        const nameB = b.name || `${b.first_name || ''} ${b.last_name || ''}`.trim() || b.email || ''
        return nameA.localeCompare(nameB)
      })
    } else if (sortBy === "name-desc") {
      filtered.sort((a, b) => {
        const nameA = a.name || `${a.first_name || ''} ${a.last_name || ''}`.trim() || a.email || ''
        const nameB = b.name || `${b.first_name || ''} ${b.last_name || ''}`.trim() || b.email || ''
        return nameB.localeCompare(nameA)
      })
    } else if (sortBy === "recent") {
      filtered.sort((a, b) => new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime())
    } else if (sortBy === "oldest") {
      filtered.sort((a, b) => new Date(a.created_at || 0).getTime() - new Date(b.created_at || 0).getTime())
    }

    setFilteredUsers(filtered)
  }, [users, searchQuery, statusFilter, sortBy])

  // Define state for loading timeout at the top level (not in a conditional)
  const [showMockDataWhileLoading, setShowMockDataWhileLoading] = useState(false)

  // Effect for handling loading timeout
  useEffect(() => {
    // If loading takes more than 2 seconds, show mock data
    const timer = setTimeout(() => {
      if (usersLoading) {
        setShowMockDataWhileLoading(true)
      }
    }, 2000)

    return () => clearTimeout(timer)
  }, [usersLoading])

  // Determine if we should show the loading state or proceed with mock data
  // Only show loading state if we're still checking auth or loading users for the first time
  const isInitialLoading = (!authChecked || (usersLoading && !dataFetched)) && !showMockDataWhileLoading;

  // If we're in the initial loading state and haven't timed out yet
  if (isInitialLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto mb-4"></div>
          <p className="text-gray-600">
            {!authChecked ? "Checking authentication..." : "Loading users data..."}
          </p>
          <button
            onClick={() => setShowMockDataWhileLoading(true)}
            className="mt-4 text-sm text-emerald-600 hover:text-emerald-800"
          >
            Show mock data instead
          </button>
        </div>
      </div>
    )
  }

  // Format date helper
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return "N/A"
    return format(new Date(dateString), 'dd MMM yyyy')
  }

  // Get user role from the role field
  const getUserRole = (user: UserProfile) => {
    // Use the role field from the database
    switch (user.role) {
      case 'super_admin':
        return "Super Admin"
      case 'admin':
        return "Admin"
      case 'business_manager':
        return "Business Manager"
      case 'business_staff':
        return "Business Staff"
      case 'customer':
      default:
        return "Customer"
    }
  }

  // Function to open user details dialog
  const openUserDetails = (user: UserProfile) => {
    setSelectedUser(user)
    setIsUserDetailsOpen(true)
  }

  // Check for different types of errors
  const isPermissionDenied = usersError && usersError.message &&
    (usersError.message.includes('permission denied for table users') ||
     usersError.message.includes('permission denied for table \'users\''))
  const isTableMissing = usersError && usersError.message && usersError.message.includes('does not exist')

  // TEMPORARY: Force real data to be used even if empty
  // const shouldUseMockData = usersError || !users || users.length === 0
  const shouldUseMockData = false

  // We'll always use mock data for permission denied errors, so we don't need to show an error page
  // This comment is intentionally left here to document our approach

  // Create dates for mock data
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)
  const lastWeek = new Date(today)
  lastWeek.setDate(lastWeek.getDate() - 7)
  const lastMonth = new Date(today)
  lastMonth.setMonth(lastMonth.getMonth() - 1)
  const twoMonthsAgo = new Date(today)
  twoMonthsAgo.setMonth(twoMonthsAgo.getMonth() - 2)

  // Use mock data for any database-related error that matches the schema
  const mockUsers = shouldUseMockData ? [
    {
      id: 1,
      name: 'Super Admin',
      email: '<EMAIL>',
      created_at: twoMonthsAgo.toISOString(),
      updated_at: today.toISOString(),
      role: 'super_admin' as const,
      first_name: 'Super',
      last_name: 'Admin',
      phone: '+44 7700 900123',
      postcode: 'JE2 3QQ'
    },
    {
      id: 2,
      name: 'Admin User',
      email: '<EMAIL>',
      created_at: lastMonth.toISOString(),
      updated_at: today.toISOString(),
      role: 'admin' as const,
      first_name: 'Admin',
      last_name: 'User',
      phone: '+44 7700 900456',
      postcode: 'JE2 4PP'
    },
    {
      id: 3,
      name: 'Restaurant Manager',
      email: '<EMAIL>',
      created_at: lastMonth.toISOString(),
      updated_at: yesterday.toISOString(),
      role: 'business_manager' as const,
      first_name: 'Restaurant',
      last_name: 'Manager',
      phone: '+44 7700 900789',
      postcode: 'JE2 5RR'
    },
    {
      id: 4,
      name: 'Cafe Staff',
      email: '<EMAIL>',
      created_at: lastWeek.toISOString(),
      updated_at: yesterday.toISOString(),
      role: 'business_staff' as const,
      first_name: 'Cafe',
      last_name: 'Staff',
      phone: '+44 7700 900321',
      postcode: 'JE2 6SS'
    },
    {
      id: 5,
      name: 'John Doe',
      email: '<EMAIL>',
      created_at: lastWeek.toISOString(),
      updated_at: lastWeek.toISOString(),
      role: 'customer' as const,
      first_name: 'John',
      last_name: 'Doe',
      phone: '+44 7700 900654',
      postcode: 'JE2 7TT',
      address: '123 Main St, St Helier'
    },
    {
      id: 6,
      name: 'Jane Smith',
      email: '<EMAIL>',
      created_at: yesterday.toISOString(),
      updated_at: yesterday.toISOString(),
      role: 'customer' as const,
      first_name: 'Jane',
      last_name: 'Smith',
      phone: '+44 7700 900987',
      postcode: 'JE2 8UU',
      address: '456 High St, St Helier'
    },
    {
      id: 7,
      name: 'New Customer',
      email: '<EMAIL>',
      created_at: today.toISOString(),
      updated_at: today.toISOString(),
      role: 'customer' as const,
      first_name: 'New',
      last_name: 'Customer',
      phone: '+44 7700 900111',
      postcode: 'JE2 9VV'
    }
  ] : []

  // Use mock data if we have any database-related error
  const displayUsers = shouldUseMockData ? mockUsers : filteredUsers

  // Add a banner for mock data if we're using it
  const usingMockData = shouldUseMockData

  // Get user stats using the role field
  const userStats = {
    total: users.length,
    admins: users.filter(u => u.role === 'admin' || u.role === 'super_admin').length,
    businessManagers: users.filter(u => u.role === 'business_manager').length,
    businessStaff: users.filter(u => u.role === 'business_staff').length,
    // For customers, count users with customer role or no role
    customers: users.filter(u => u.role === 'customer' || !u.role).length,
    newThisWeek: users.filter(u => {
      if (!u.created_at) return false
      const sevenDaysAgo = new Date()
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
      return new Date(u.created_at) >= sevenDaysAgo
    }).length
  }

  console.log("User stats:", userStats);

  // Handle tab change
  const handleTabChange = (value: string) => {
    setStatusFilter(value)
  }

  // Handle search
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value)
  }

  // Handle sort change
  const handleSortChange = (value: string) => {
    setSortBy(value)
  }

  return (
    <div className="flex min-h-screen flex-col bg-muted/40">
      {/* Header */}
      <header className="sticky top-0 z-10 flex h-16 items-center gap-4 border-b bg-background px-4 sm:px-6">
        <div className="flex flex-1 items-center gap-4">
          <Link href="/admin-new" className="flex items-center gap-2">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-emerald-500 text-white">
              <Package2 className="h-4 w-4" />
            </div>
            <span className="font-semibold">Loop Jersey</span>
          </Link>
          <h1 className="text-xl font-semibold">User Management</h1>
        </div>
        <div className="flex items-center gap-4">
          <form className="relative hidden md:block">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search users..."
              className="w-64 pl-8"
              value={searchQuery}
              onChange={handleSearch}
            />
          </form>
          <Button variant="outline" size="sm" className="hidden md:flex">
            <Download className="mr-2 h-4 w-4" />
            Export Data
          </Button>
          <Avatar className="h-8 w-8">
            <AvatarImage src="/placeholder-user.jpg" />
            <AvatarFallback>AD</AvatarFallback>
          </Avatar>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 p-4 sm:p-6 lg:p-8">
        {/* Debug Info */}
        {process.env.NODE_ENV === 'development' && (
          <div className="bg-blue-50 border-l-4 border-blue-500 p-4 mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <AlertCircle className="h-5 w-5 text-blue-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-blue-700 font-medium">Debug Information</p>
                <p className="text-xs text-blue-700 mt-1">
                  Auth Checked: {authChecked ? 'Yes' : 'No'} |
                  Loading: {usersLoading ? 'Yes' : 'No'} |
                  Data Fetched: {dataFetched ? 'Yes' : 'No'} |
                  Error: {usersError ? 'Yes' : 'No'} |
                  Users Count: {users.length} |
                  Filtered Users: {filteredUsers.length} |
                  Method: {typeof window !== 'undefined' && new URLSearchParams(window.location.search).get('force_api') === 'true' ? 'API Endpoint' : 'Direct Query'}
                </p>
                {usersError && (
                  <p className="text-xs text-red-700 mt-1">
                    Error: {usersError.message}
                  </p>
                )}
                <div className="flex gap-2 mt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-xs bg-blue-100 hover:bg-blue-200 text-blue-800 h-auto py-1 border-blue-200"
                    onClick={() => refetchUsers()}
                  >
                    Refresh Data
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-xs bg-green-100 hover:bg-green-200 text-green-800 h-auto py-1 border-green-200"
                    onClick={() => {
                      window.location.href = '/admin-new/users?force_api=true';
                    }}
                  >
                    Force API Endpoint
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Mock Data Banner */}
        {usingMockData && (
          <div className="bg-amber-50 border-l-4 border-amber-500 p-4 mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <AlertCircle className="h-5 w-5 text-amber-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-amber-700">
                  <span className="font-medium">
                    {isPermissionDenied ? 'Permission denied for users table.' :
                     isTableMissing ? 'Users table does not exist.' :
                     usersLoading && showMockDataWhileLoading ? 'Loading taking longer than expected.' :
                     'Database error occurred.'}
                  </span> Showing mock data instead.
                </p>
                <p className="text-xs text-amber-700 mt-1">
                  {isPermissionDenied ?
                    'This is due to Row Level Security (RLS) policies on the users table. The current RLS policies only allow users to read their own data and super admins to read all data.' :
                    isTableMissing ?
                    'The users table does not exist in the database. Contact your database administrator to create the necessary tables.' :
                    usersLoading && showMockDataWhileLoading ?
                    'The data is still loading in the background. This mock data is shown to improve your experience while waiting.' :
                    'There was an error connecting to the database. Check your database connection and configuration.'}
                </p>

                {/* Show different actions based on the error type */}
                {isPermissionDenied && (
                  <div className="mt-3">
                    <p className="text-xs text-amber-700 font-medium mb-2">To fix this issue:</p>
                    <ol className="text-xs text-amber-700 list-decimal pl-5 mb-2 space-y-1">
                      <li>Go to the Supabase dashboard</li>
                      <li>Navigate to Authentication → Policies</li>
                      <li>Add a policy for the users table that allows users with admin role to read all users</li>
                      <li>Example policy: <code className="bg-amber-100 px-1">role = 'super_admin' OR role = 'admin'</code></li>
                    </ol>
                    <p className="text-xs text-amber-700 mt-2">
                      <strong>Note:</strong> Even with super_admin role, you need an explicit RLS policy to access the users table.
                      The current implementation is using the authenticated client, but RLS policies still apply.
                    </p>

                    <div className="mt-2 flex flex-col sm:flex-row gap-2">
                      <a
                        href="https://bhbxhzisfzclwuyclfqn.supabase.co/project/default/auth/policies"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-xs bg-amber-100 hover:bg-amber-200 text-amber-800 px-3 py-1 rounded inline-flex items-center"
                      >
                        <span>View RLS Policies</span>
                      </a>
                      <a
                        href="https://bhbxhzisfzclwuyclfqn.supabase.co/project/default/editor/table/users"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-xs bg-amber-100 hover:bg-amber-200 text-amber-800 px-3 py-1 rounded inline-flex items-center"
                      >
                        <span>Edit Users Table</span>
                      </a>
                      <a
                        href="https://bhbxhzisfzclwuyclfqn.supabase.co/project/default/editor/table/users?rls=disabled"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-xs bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded inline-flex items-center"
                      >
                        <span>Disable RLS (Temporary)</span>
                      </a>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-xs bg-amber-100 hover:bg-amber-200 text-amber-800 h-auto py-1 border-amber-200"
                        onClick={() => refetchUsers()}
                      >
                        Retry Query
                      </Button>
                    </div>
                  </div>
                )}

                {usersError && usersError.message && (
                  <details className="mt-2">
                    <summary className="text-xs text-amber-700 cursor-pointer">View error details</summary>
                    <p className="mt-1 text-xs font-mono bg-amber-100 p-2 rounded text-amber-800 overflow-auto max-h-24">
                      {usersError.message === '{}' ? 'Empty error object returned from Supabase' : usersError.message}
                    </p>
                  </details>
                )}
              </div>
            </div>
          </div>
        )}
        {/* User Stats */}
        <div className="mb-6 grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{userStats.total}</div>
              <p className="text-xs text-muted-foreground">
                +{userStats.newThisWeek} new this week
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Customers</CardTitle>
              <User className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{userStats.customers}</div>
              <p className="text-xs text-muted-foreground">
                {userStats.total > 0
                  ? `${Math.round((userStats.customers / userStats.total) * 100)}% of total users`
                  : "No users yet"}
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Business Managers</CardTitle>
              <Store className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{userStats.businessManagers}</div>
              <p className="text-xs text-muted-foreground">
                {userStats.total > 0
                  ? `${Math.round((userStats.businessManagers / userStats.total) * 100)}% of total users`
                  : "No users yet"}
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Admins</CardTitle>
              <UserPlus className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{userStats.admins}</div>
              <p className="text-xs text-muted-foreground">
                {userStats.total > 0
                  ? `${Math.round((userStats.admins / userStats.total) * 100)}% of total users`
                  : "No users yet"}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* User List */}
        <Card className="mb-6">
          <CardHeader>
            <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
              <div>
                <CardTitle>Users</CardTitle>
                <CardDescription>Manage users on your platform</CardDescription>
              </div>
              <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
                <Button>
                  <UserPlus className="mr-2 h-4 w-4" />
                  Add User
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between mb-4">
              <Tabs defaultValue="all" className="w-full" onValueChange={handleTabChange}>
                <TabsList>
                  <TabsTrigger value="all">All Users</TabsTrigger>
                  <TabsTrigger value="admin">Admins</TabsTrigger>
                  <TabsTrigger value="business">Business Managers</TabsTrigger>
                  <TabsTrigger value="customer">Customers</TabsTrigger>
                  <TabsTrigger value="recent">Recent</TabsTrigger>
                </TabsList>
              </Tabs>

              <div className="flex items-center gap-2">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" className="h-8">
                      <SlidersHorizontal className="mr-2 h-3.5 w-3.5" />
                      Sort
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-[180px]">
                    <DropdownMenuLabel>Sort by</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => handleSortChange("name-asc")}>Name (A-Z)</DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleSortChange("name-desc")}>Name (Z-A)</DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleSortChange("recent")}>Date Added (Newest)</DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleSortChange("oldest")}>Date Added (Oldest)</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>

            <div className="space-y-4">
              {filteredUsers.length > 0 ? (
                filteredUsers.map((user) => (
                  <div key={user.id} className="flex items-center justify-between rounded-lg border p-4">
                    <div className="flex items-center gap-4">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src="/placeholder-user.jpg" />
                        <AvatarFallback className="bg-emerald-100 text-emerald-700">
                          {user.name ? user.name.charAt(0) :
                           user.first_name ? user.first_name.charAt(0) :
                           user.email ? user.email.charAt(0) : 'U'}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className="font-medium">
                          {user.name ||
                           (user.first_name || user.last_name ?
                            `${user.first_name || ''} ${user.last_name || ''}`.trim() :
                            "Unnamed User")}
                        </h3>
                        <p className="text-sm text-muted-foreground">
                          {user.email || "No email"} • Joined {formatDate(user.created_at)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <Badge
                        variant="outline"
                        className={
                          user.role === 'super_admin'
                            ? "bg-purple-50 text-purple-700"
                            : user.role === 'admin'
                            ? "bg-blue-50 text-blue-700"
                            : user.role === 'business_manager'
                            ? "bg-amber-50 text-amber-700"
                            : user.role === 'business_staff'
                            ? "bg-orange-50 text-orange-700"
                            : "bg-green-50 text-green-700"
                        }
                      >
                        {getUserRole(user)}
                      </Badge>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openUserDetails(user)}
                      >
                        View Details
                      </Button>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No users found matching your criteria
                </div>
              )}
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <div className="text-sm text-muted-foreground">
              Showing {filteredUsers.length} of {displayUsers.length} users
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" disabled>
                Previous
              </Button>
              <Button variant="outline" size="sm" disabled>
                Next
              </Button>
            </div>
          </CardFooter>
        </Card>

        <div className="text-center mt-8">
          <p className="text-sm text-muted-foreground">
            This is a preview of the new admin dashboard with real-time data updates.
            <br />
            Changes to users will appear instantly without refreshing the page.
          </p>
          <div className="mt-4">
            <Link href="/admin-new">
              <Button variant="outline">
                Back to Dashboard
              </Button>
            </Link>
          </div>
        </div>
      </main>

      {/* User Details Dialog */}
      {selectedUser && (
        <UserDetailsDialog
          open={isUserDetailsOpen}
          onOpenChange={setIsUserDetailsOpen}
          user={{
            id: selectedUser.id,
            name: selectedUser.name ||
                 (selectedUser.first_name || selectedUser.last_name ?
                  `${selectedUser.first_name || ''} ${selectedUser.last_name || ''}`.trim() :
                  "Unnamed User"),
            email: selectedUser.email || "No email",
            role: selectedUser.role || "customer",
            auth_id: selectedUser.auth_id,
            created_at: selectedUser.created_at,
            updated_at: selectedUser.updated_at,
            last_login: selectedUser.updated_at // Using updated_at as a fallback since we don't track last_login yet
          }}
        />
      )}
    </div>
  )
}
