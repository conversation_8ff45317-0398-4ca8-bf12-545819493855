// Voice message cleanup utilities
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY! // Use service role for admin operations
)

export interface CleanupResult {
  success: boolean
  deletedFiles: number
  errors: string[]
}

/**
 * Clean up orphaned voice message files
 * Removes files from storage that don't have corresponding database records
 */
export async function cleanupOrphanedVoiceFiles(): Promise<CleanupResult> {
  const result: CleanupResult = {
    success: true,
    deletedFiles: 0,
    errors: []
  }

  try {
    console.log('🧹 Starting voice file cleanup...')

    // 1. Get all files from storage
    const { data: files, error: listError } = await supabase.storage
      .from('voice-messages')
      .list('', {
        limit: 1000,
        sortBy: { column: 'created_at', order: 'desc' }
      })

    if (listError) {
      result.errors.push(`Failed to list files: ${listError.message}`)
      result.success = false
      return result
    }

    if (!files || files.length === 0) {
      console.log('✅ No voice files found in storage')
      return result
    }

    console.log(`📁 Found ${files.length} files in storage`)

    // 2. Get all audio file names from database
    const { data: dbRecords, error: dbError } = await supabase
      .from('communications')
      .select('audio_file_name')
      .not('audio_file_name', 'is', null)

    if (dbError) {
      result.errors.push(`Failed to query database: ${dbError.message}`)
      result.success = false
      return result
    }

    const dbFileNames = new Set(
      dbRecords?.map(record => record.audio_file_name).filter(Boolean) || []
    )

    console.log(`💾 Found ${dbFileNames.size} voice files referenced in database`)

    // 3. Find orphaned files
    const orphanedFiles = files.filter(file => {
      // Skip directories
      if (!file.name || file.name.endsWith('/')) return false
      
      // Check if file is referenced in database
      return !dbFileNames.has(file.name)
    })

    console.log(`🗑️ Found ${orphanedFiles.length} orphaned files`)

    // 4. Delete orphaned files
    if (orphanedFiles.length > 0) {
      const filesToDelete = orphanedFiles.map(file => file.name)
      
      const { error: deleteError } = await supabase.storage
        .from('voice-messages')
        .remove(filesToDelete)

      if (deleteError) {
        result.errors.push(`Failed to delete files: ${deleteError.message}`)
        result.success = false
      } else {
        result.deletedFiles = orphanedFiles.length
        console.log(`✅ Deleted ${orphanedFiles.length} orphaned files`)
      }
    }

    return result

  } catch (error) {
    result.errors.push(`Cleanup failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    result.success = false
    return result
  }
}

/**
 * Clean up old voice messages (older than specified days)
 */
export async function cleanupOldVoiceMessages(olderThanDays: number = 30): Promise<CleanupResult> {
  const result: CleanupResult = {
    success: true,
    deletedFiles: 0,
    errors: []
  }

  try {
    console.log(`🕒 Cleaning up voice messages older than ${olderThanDays} days...`)

    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays)

    // 1. Find old voice messages in database
    const { data: oldMessages, error: queryError } = await supabase
      .from('communications')
      .select('id, audio_file_name')
      .eq('message_type', 'voice')
      .not('audio_file_name', 'is', null)
      .lt('created_at', cutoffDate.toISOString())

    if (queryError) {
      result.errors.push(`Failed to query old messages: ${queryError.message}`)
      result.success = false
      return result
    }

    if (!oldMessages || oldMessages.length === 0) {
      console.log('✅ No old voice messages found')
      return result
    }

    console.log(`📅 Found ${oldMessages.length} old voice messages`)

    // 2. Delete files from storage
    const filesToDelete = oldMessages
      .map(msg => msg.audio_file_name)
      .filter(Boolean)

    if (filesToDelete.length > 0) {
      const { error: deleteError } = await supabase.storage
        .from('voice-messages')
        .remove(filesToDelete)

      if (deleteError) {
        result.errors.push(`Failed to delete old files: ${deleteError.message}`)
        result.success = false
        return result
      }

      result.deletedFiles = filesToDelete.length
    }

    // 3. Clear audio data from database records
    const messageIds = oldMessages.map(msg => msg.id)
    
    const { error: updateError } = await supabase
      .from('communications')
      .update({
        audio_data: null,
        audio_file_name: null,
        audio_file_size: null,
        audio_duration: null
      })
      .in('id', messageIds)

    if (updateError) {
      result.errors.push(`Failed to update database records: ${updateError.message}`)
      result.success = false
      return result
    }

    console.log(`✅ Cleaned up ${oldMessages.length} old voice messages`)
    return result

  } catch (error) {
    result.errors.push(`Old message cleanup failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    result.success = false
    return result
  }
}

/**
 * Get storage usage statistics
 */
export async function getVoiceStorageStats() {
  try {
    // Get file count and total size
    const { data: files, error } = await supabase.storage
      .from('voice-messages')
      .list('', {
        limit: 1000,
        sortBy: { column: 'created_at', order: 'desc' }
      })

    if (error) {
      console.error('Failed to get storage stats:', error)
      return null
    }

    const totalFiles = files?.length || 0
    const totalSize = files?.reduce((sum, file) => sum + (file.metadata?.size || 0), 0) || 0

    // Get database count
    const { count: dbCount, error: countError } = await supabase
      .from('communications')
      .select('*', { count: 'exact', head: true })
      .eq('message_type', 'voice')
      .not('audio_file_name', 'is', null)

    if (countError) {
      console.error('Failed to get database count:', countError)
    }

    return {
      totalFiles,
      totalSizeBytes: totalSize,
      totalSizeMB: Math.round(totalSize / (1024 * 1024) * 100) / 100,
      databaseRecords: dbCount || 0,
      orphanedFiles: Math.max(0, totalFiles - (dbCount || 0))
    }

  } catch (error) {
    console.error('Storage stats error:', error)
    return null
  }
}
