const { createClient } = require('@supabase/supabase-js');
const { v4: uuidv4 } = require('uuid');
require('dotenv').config();

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function addUserConnections() {
  console.log('🔗 Adding Connections and Messages for User\n');

  const targetUserId = '43560081-d469-4d4e-85e2-457bda286397';

  try {
    // Step 1: Check if user exists
    console.log('1️⃣ Checking if user exists...');
    const { data: users } = await supabase.auth.admin.listUsers();
    const targetUser = users.users.find(u => u.id === targetUserId);

    if (!targetUser) {
      console.log('❌ User not found. Creating user...');

      // Create the user
      const { data: newUser, error: createError } = await supabase.auth.admin.createUser({
        user_id: targetUserId,
        email: '<EMAIL>',
        password: 'test-password-123',
        email_confirm: true,
        user_metadata: {
          name: 'Test User'
        }
      });

      if (createError) {
        console.log('❌ Could not create user:', createError.message);
        return;
      }

      console.log('✅ User created successfully');
    } else {
      console.log('✅ User exists:', targetUser.email);
    }

    // Step 2: Get existing users to connect with
    console.log('\n2️⃣ Getting existing users to connect with...');
    const existingUsers = users.users.filter(u => u.id !== targetUserId).slice(0, 3);

    if (existingUsers.length === 0) {
      console.log('❌ No other users found to create connections');
      return;
    }

    console.log(`✅ Found ${existingUsers.length} users to connect with`);

    // Step 3: Create connections
    console.log('\n3️⃣ Creating connections...');

    const connections = [
      {
        user1_id: targetUserId < existingUsers[0].id ? targetUserId : existingUsers[0].id,
        user2_id: targetUserId < existingUsers[0].id ? existingUsers[0].id : targetUserId,
        connection_type: 'customer-business',
        status: 'active',
        created_by: targetUserId
      },
      {
        user1_id: targetUserId < existingUsers[1].id ? targetUserId : existingUsers[1].id,
        user2_id: targetUserId < existingUsers[1].id ? existingUsers[1].id : targetUserId,
        connection_type: 'customer-business',
        status: 'active',
        created_by: targetUserId
      }
    ];

    for (const connection of connections) {
      const { error } = await supabase
        .from('connections')
        .upsert(connection, { onConflict: 'user1_id,user2_id' });

      if (error) {
        console.log(`   ⚠️  Connection error: ${error.message}`);
      } else {
        console.log(`   ✅ Connection created between users`);
      }
    }

    // Step 4: Create connection profile for the user
    console.log('\n4️⃣ Creating connection profile...');

    const profile = {
      user_id: targetUserId,
      display_name: 'Test User',
      bio: 'Active Loop Jersey user looking for great local food',
      is_public: true,
      allow_direct_messages: true
    };

    const { error: profileError } = await supabase
      .from('connection_profiles')
      .upsert(profile, { onConflict: 'user_id' });

    if (profileError) {
      console.log(`   ⚠️  Profile error: ${profileError.message}`);
    } else {
      console.log(`   ✅ Profile created: ${profile.display_name}`);
    }

    // Step 5: Create realistic conversations
    console.log('\n5️⃣ Creating conversations and messages...');

    const conversations = [
      {
        thread_id: uuidv4(),
        participants: [targetUserId, existingUsers[0].id],
        channel_type: 'customer_enquiries',
        messages: [
          {
            sender_id: targetUserId,
            content: 'Hi! I saw your restaurant on Loop Jersey. Do you have any gluten-free options?',
            subject: 'Gluten-Free Options',
            message_type: 'chat',
            created_at: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString() // 3 hours ago
          },
          {
            sender_id: existingUsers[0].id,
            content: 'Hello! Yes, we have several gluten-free dishes. Our gluten-free pasta is very popular!',
            message_type: 'chat',
            created_at: new Date(Date.now() - 2.5 * 60 * 60 * 1000).toISOString() // 2.5 hours ago
          },
          {
            sender_id: targetUserId,
            content: 'That sounds perfect! Could you tell me more about your gluten-free menu?',
            message_type: 'chat',
            created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString() // 2 hours ago
          }
        ]
      },
      {
        thread_id: uuidv4(),
        participants: [targetUserId, existingUsers[1].id],
        channel_type: 'active_order_delivery',
        messages: [
          {
            sender_id: existingUsers[1].id,
            content: 'Your order has been confirmed and is being prepared (Order #LJ2024001)',
            subject: 'Order Confirmed',
            message_type: 'status_update',
            is_automated: true,
            priority: 1,
            created_at: new Date(Date.now() - 45 * 60 * 1000).toISOString() // 45 minutes ago
          },
          {
            sender_id: existingUsers[1].id,
            content: 'Your order is ready for pickup! Please come to the counter.',
            subject: 'Order Ready',
            message_type: 'status_update',
            is_automated: true,
            is_urgent: true,
            priority: 1,
            created_at: new Date(Date.now() - 15 * 60 * 1000).toISOString() // 15 minutes ago
          },
          {
            sender_id: targetUserId,
            content: 'Great! I\'ll be there in 5 minutes. Thank you!',
            message_type: 'chat',
            created_at: new Date(Date.now() - 12 * 60 * 1000).toISOString() // 12 minutes ago
          }
        ]
      },
      {
        thread_id: uuidv4(),
        participants: [targetUserId, existingUsers[0].id],
        channel_type: 'post_order_feedback',
        messages: [
          {
            sender_id: targetUserId,
            content: 'The gluten-free pasta was absolutely delicious! Thank you for the excellent service.',
            subject: 'Great Experience!',
            message_type: 'chat',
            created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() // 1 day ago
          },
          {
            sender_id: existingUsers[0].id,
            content: 'Thank you so much for the wonderful feedback! We\'re delighted you enjoyed your meal. Looking forward to serving you again soon!',
            message_type: 'chat',
            created_at: new Date(Date.now() - 23 * 60 * 60 * 1000).toISOString() // 23 hours ago
          }
        ]
      }
    ];

    // Insert all messages
    for (const conversation of conversations) {
      console.log(`   📝 Creating conversation: ${conversation.channel_type}`);

      for (let i = 0; i < conversation.messages.length; i++) {
        const message = conversation.messages[i];
        const recipient_id = conversation.participants.find(p => p !== message.sender_id);

        const messageData = {
          sender_id: message.sender_id,
          recipient_id: recipient_id,
          thread_id: conversation.thread_id,
          channel_type: conversation.channel_type,
          message_type: message.message_type,
          subject: message.subject,
          content: message.content,
          is_automated: message.is_automated || false,
          is_urgent: message.is_urgent || false,
          is_read: i < conversation.messages.length - 1, // Mark all but last message as read
          priority: message.priority || 0,
          created_at: message.created_at
        };

        const { error } = await supabase
          .from('communications')
          .insert(messageData);

        if (error) {
          console.log(`     ❌ Message error: ${error.message}`);
        } else {
          console.log(`     ✅ Message created: "${message.content.substring(0, 40)}..."`);
        }
      }
    }

    console.log('\n🎉 User connections and messages created successfully!');
    console.log('\n📊 Summary:');
    console.log(`   👤 User: ${targetUserId}`);
    console.log(`   🔗 ${connections.length} connections created`);
    console.log(`   💬 ${conversations.length} conversation threads`);
    console.log('   📝 Multiple messages per conversation');
    console.log('   📋 Channel types: customer_enquiries, active_order_delivery, post_order_feedback');
    console.log('\n🎯 The user can now see connections and messages in the UI!');

  } catch (error) {
    console.error('❌ Error creating user connections:', error);
  }
}

addUserConnections();
