"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  MessageSquare,
  Clock,
  CheckCircle2,
  Circle,
  Building2,
  Truck,
  User
} from "lucide-react"
import { cn } from "@/lib/utils"

interface User {
  id: string
  email?: string
  user_metadata?: any
}

interface Conversation {
  id: string
  contact_name: string
  contact_type: 'business' | 'rider' | 'customer'
  channel_type: string
  last_message: string
  last_message_time: string
  is_read: boolean
  is_urgent: boolean
  unread_count: number
  avatar_url?: string
}

interface ConversationListProps {
  user: User
  searchQuery: string
  onConversationSelected: (conversationId: string) => void
}

export function ConversationList({
  user,
  searchQuery,
  onConversationSelected
}: ConversationListProps) {
  const [conversations, setConversations] = useState<Conversation[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadConversations()
  }, [user, searchQuery])

  const loadConversations = async () => {
    setIsLoading(true)
    try {
      // Fetch conversations from API
      const response = await fetch('/api/connections-hub/messages?view=conversations')

      if (!response.ok) {
        throw new Error('Failed to fetch conversations')
      }

      const data = await response.json()
      const apiConversations = data.conversations || []

      // Transform API data to component format
      const transformedConversations: Conversation[] = apiConversations.map((conv: any) => ({
        id: conv.thread_id,
        contact_name: getContactName(conv),
        contact_type: getContactType(conv),
        channel_type: conv.channel_type,
        last_message: conv.content,
        last_message_time: formatTime(conv.created_at),
        is_read: conv.is_read,
        is_urgent: conv.is_urgent,
        unread_count: conv.unread_count || 0,
        avatar_url: undefined // TODO: Add avatar support
      }))

      // Filter by search query
      const filtered = searchQuery
        ? transformedConversations.filter(conv =>
            conv.contact_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            conv.last_message.toLowerCase().includes(searchQuery.toLowerCase())
          )
        : transformedConversations

      setConversations(filtered)
    } catch (error) {
      console.error('Error loading conversations:', error)
      // Fallback to empty array on error
      setConversations([])
    } finally {
      setIsLoading(false)
    }
  }

  // Helper functions for data transformation
  const getContactName = (conv: any): string => {
    // TODO: Implement proper user/business name lookup
    return conv.other_user_id ? `User ${conv.other_user_id.slice(0, 8)}` : 'Unknown Contact'
  }

  const getContactType = (conv: any): 'business' | 'rider' | 'customer' => {
    // TODO: Implement proper contact type detection based on user profile lookup
    // For now, use business_id presence to detect business contacts
    if (conv.business_id) return 'business'
    // Could add rider detection logic here based on user roles
    return 'customer'
  }

  const formatTime = (timestamp: string): string => {
    const now = new Date()
    const messageTime = new Date(timestamp)
    const diffInMinutes = Math.floor((now.getTime() - messageTime.getTime()) / (1000 * 60))

    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`
    return `${Math.floor(diffInMinutes / 1440)}d ago`
  }

  const getChannelDisplayName = (channelType: string): string => {
    const channelNames: Record<string, string> = {
      'order': 'Active Order',
      'pre-order': 'Pre-order Planning',
      'post-order': 'Feedback',
      'general': 'General'
    }
    return channelNames[channelType] || channelType
  }

  const getContactIcon = (contactType: string) => {
    switch (contactType) {
      case 'business':
        return <Building2 className="h-5 w-5 text-green-600" />
      case 'rider':
        return <Truck className="h-5 w-5 text-blue-600" />
      default:
        return <User className="h-5 w-5 text-gray-600" />
    }
  }

  const getChannelColor = (channelType: string): string => {
    const colors: Record<string, string> = {
      'order': 'bg-orange-100 text-orange-800',
      'pre-order': 'bg-blue-100 text-blue-800',
      'post-order': 'bg-yellow-100 text-yellow-800',
      'general': 'bg-purple-100 text-purple-800'
    }
    return colors[channelType] || 'bg-gray-100 text-gray-800'
  }

  if (isLoading) {
    return (
      <div className="px-4 space-y-3">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-4">
              <div className="flex items-start space-x-3">
                <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-3 bg-gray-200 rounded w-full"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (conversations.length === 0) {
    return (
      <div className="px-4">
        <Card>
          <CardContent className="p-8 text-center">
            <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchQuery ? 'No conversations found' : 'No conversations yet'}
            </h3>
            <p className="text-gray-500">
              {searchQuery
                ? 'Try adjusting your search terms'
                : 'Start a conversation to connect with businesses and riders'
              }
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="px-4 space-y-2">
      {conversations.map((conversation) => (
        <Card
          key={conversation.id}
          className={cn(
            "cursor-pointer transition-colors hover:bg-gray-50",
            !conversation.is_read && "border-emerald-200 bg-emerald-50/30"
          )}
          onClick={() => onConversationSelected(conversation.id)}
        >
          <CardContent className="p-4">
            <div className="flex items-start space-x-3">
              {/* Avatar */}
              <div className="relative">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={conversation.avatar_url} />
                  <AvatarFallback className="bg-gray-100">
                    {getContactIcon(conversation.contact_type)}
                  </AvatarFallback>
                </Avatar>
                {conversation.unread_count > 0 && (
                  <div className="absolute -top-1 -right-1 h-5 w-5 bg-emerald-600 text-white text-xs rounded-full flex items-center justify-center">
                    {conversation.unread_count}
                  </div>
                )}
              </div>

              {/* Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <h4 className="text-sm font-medium text-gray-900 truncate">
                    {conversation.contact_name}
                  </h4>
                  <div className="flex items-center space-x-2">
                    <span className="text-xs text-gray-500">
                      {conversation.last_message_time}
                    </span>
                    {conversation.is_read ? (
                      <CheckCircle2 className="h-4 w-4 text-gray-400" />
                    ) : (
                      <Circle className="h-4 w-4 text-emerald-600" />
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-2 mb-2">
                  <Badge
                    variant="secondary"
                    className={cn("text-xs", getChannelColor(conversation.channel_type))}
                  >
                    {getChannelDisplayName(conversation.channel_type)}
                  </Badge>
                  {conversation.is_urgent && (
                    <Badge variant="destructive" className="text-xs">
                      Urgent
                    </Badge>
                  )}
                </div>

                <p className="text-sm text-gray-600 line-clamp-2">
                  {conversation.last_message}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
