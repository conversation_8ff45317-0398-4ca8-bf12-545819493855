// Types for the Connections Hub

export type UserRole = 'customer' | 'business' | 'rider'
export type ConnectionStatus = 'pending' | 'active' | 'blocked' | 'archived'
export type ConnectionType = 'customer-business' | 'business-rider' | 'customer-rider' | 'business-business' | 'rider-rider'
export type ChannelType = 'customer_enquiries' | 'active_order_delivery' | 'pre_order_planning' | 'post_order_feedback' | 'business_networking' | 'rider_coordination' | 'general_networking'

export interface Connection {
  id: string
  connection_type: ConnectionType
  status: ConnectionStatus
  other_user_id: string
  is_favorite: boolean
  created_at: string
  updated_at: string
  notes?: string
  other_user?: UserProfile
}

export interface UserProfile {
  id: string
  user_id: string
  display_name: string
  bio?: string
  avatar_url?: string
  role_capabilities: {
    can_be_customer: boolean
    can_be_rider: boolean
    owns_business: boolean
  }
  specialties?: Record<string, any>
  average_rating?: number
  total_ratings?: number
  is_public: boolean
  allow_direct_messages: boolean
}

export interface Message {
  id: string
  sender_id: string
  recipient_id: string
  content: string
  channel_type: ChannelType
  message_type: 'chat' | 'notification' | 'alert' | 'request' | 'response' | 'status_update'
  thread_id: string
  is_read: boolean
  is_urgent: boolean
  created_at: string
  sender_role?: UserRole
  recipient_role?: UserRole
  sender_color?: string
  recipient_color?: string
}

export interface SearchFilters {
  query?: string
  profile_type?: UserRole
  limit?: number
  offset?: number
}

export interface ConnectionFilters {
  status?: ConnectionStatus | 'all'
  type?: ConnectionType
  favorites_only?: boolean
}
