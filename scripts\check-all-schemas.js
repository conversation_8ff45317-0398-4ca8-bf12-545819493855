const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function checkAllSchemas() {
  try {
    console.log('🔍 Checking all schemas and tables...');
    
    // Get all schemas
    const { data: schemas, error: schemasError } = await supabase
      .rpc('exec_sql', {
        sql: `
          SELECT schema_name
          FROM information_schema.schemata
          WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
          ORDER BY schema_name;
        `
      });
    
    if (schemasError) {
      console.error('❌ Error getting schemas:', schemasError);
      return;
    }
    
    console.log('📊 Available schemas:');
    if (schemas && schemas.length > 0) {
      schemas.forEach(schema => {
        console.log(`  - ${schema.schema_name}`);
      });
    }
    
    // Get all tables across all schemas
    const { data: allTables, error: allTablesError } = await supabase
      .rpc('exec_sql', {
        sql: `
          SELECT table_schema, table_name, table_type
          FROM information_schema.tables
          WHERE table_schema NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
          ORDER BY table_schema, table_name;
        `
      });
    
    if (allTablesError) {
      console.error('❌ Error getting all tables:', allTablesError);
      return;
    }
    
    console.log('\n📋 All tables by schema:');
    if (allTables && allTables.length > 0) {
      let currentSchema = '';
      allTables.forEach(table => {
        if (table.table_schema !== currentSchema) {
          currentSchema = table.table_schema;
          console.log(`\n  ${currentSchema}:`);
        }
        console.log(`    - ${table.table_name} (${table.table_type})`);
      });
    } else {
      console.log('  No tables found');
    }
    
    // Look specifically for orders-related tables
    const { data: ordersTables, error: ordersTablesError } = await supabase
      .rpc('exec_sql', {
        sql: `
          SELECT table_schema, table_name
          FROM information_schema.tables
          WHERE table_name LIKE '%order%'
          ORDER BY table_schema, table_name;
        `
      });
    
    if (!ordersTablesError && ordersTables && ordersTables.length > 0) {
      console.log('\n🛒 Order-related tables:');
      ordersTables.forEach(table => {
        console.log(`  - ${table.table_schema}.${table.table_name}`);
      });
    }
    
    // Look specifically for communications tables
    const { data: commTables, error: commTablesError } = await supabase
      .rpc('exec_sql', {
        sql: `
          SELECT table_schema, table_name
          FROM information_schema.tables
          WHERE table_name LIKE '%communication%' OR table_name LIKE '%message%'
          ORDER BY table_schema, table_name;
        `
      });
    
    if (!commTablesError && commTables && commTables.length > 0) {
      console.log('\n💬 Communication-related tables:');
      commTables.forEach(table => {
        console.log(`  - ${table.table_schema}.${table.table_name}`);
      });
    }
    
  } catch (err) {
    console.error('❌ Error:', err);
  }
}

checkAllSchemas();
