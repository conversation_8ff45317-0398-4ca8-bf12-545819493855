"use client";

import React from "react";
import { ShoppingBag, Clock, MapPin, CheckCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useCheckout } from "../checkout-context";
import { useRealtimeCart } from "@/context/realtime-cart-context";
import Link from "next/link";

interface OrderSummaryProps {
  isFormComplete: boolean;
}

export const OrderSummary: React.FC<OrderSummaryProps> = ({ isFormComplete }) => {
  const {
    itemsByBusiness,
    businessNames,
    totalDeliveryFee,
    serviceFee,
    grandTotal,
    stepsCompleted,
    firstName,
    lastName,
    phone,
    address,
    postcode,
    paymentMethod,
    handleSubmit,
    isSubmitting,
    businessDetails
  } = useCheckout();

  const {
    totalPrice,
    totalItems,
    getDeliveryMethod,
    getDeliveryFee,
    getDeliveryType,
    getScheduledTime
  } = useRealtimeCart();

  // Format date for display
  const formatDeliveryTime = (date: Date | null): string => {
    if (!date) return "As soon as possible";

    return date.toLocaleString([], {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6 sticky top-4">
      <h2 className="text-xl font-semibold mb-4 flex items-center">
        <ShoppingBag className="h-5 w-5 mr-2 text-emerald-600" />
        Order Summary
      </h2>

      {/* Order Items */}
      <div className="space-y-4 mb-4">
        {Object.keys(itemsByBusiness).map((businessId) => (
          <div key={businessId} className="border-b pb-3">
            <h3 className="font-medium">{businessNames(businessId)}</h3>
            <div className="space-y-2 mt-2">
              {itemsByBusiness[businessId].map((item) => (
                <div key={item.id} className="flex justify-between text-sm">
                  <span>{item.quantity}x {item.name}</span>
                  <span>£{(item.price * item.quantity).toFixed(2)}</span>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Order Totals */}
      <div className="border-t pt-3 space-y-2">
        <div className="flex justify-between">
          <span>Subtotal:</span>
          <span>£{totalPrice.toFixed(2)}</span>
        </div>

        {/* Delivery Fees Per Business */}
        {Object.keys(itemsByBusiness).map(businessId => {
          const deliveryMethod = getDeliveryMethod(businessId);
          const deliveryFee = getDeliveryFee(businessId);

          return (
            <div key={`fee-${businessId}`} className="flex justify-between text-sm">
              <span className="flex items-center">
                {deliveryMethod === 'delivery' ? 'Delivery' : 'Pickup'} ({businessNames(businessId)}):
              </span>
              <span>
                {deliveryMethod === 'pickup' ? 'Free' : `£${deliveryFee.toFixed(2)}`}
              </span>
            </div>
          );
        })}

        <div className="flex justify-between">
          <span>Service Fee:</span>
          <span>£{serviceFee.toFixed(2)}</span>
        </div>
        <div className="flex justify-between font-bold text-lg pt-2">
          <span>Total:</span>
          <span>£{grandTotal.toFixed(2)}</span>
        </div>
      </div>

      {/* Order Details (only show when form is complete) */}
      {isFormComplete && (
        <div className="mt-6 pt-4 border-t border-dashed">
          <h3 className="font-medium mb-3">Order Details</h3>

          <div className="space-y-3 text-sm">
            <div className="flex">
              <div className="w-8">
                <CheckCircle className="h-4 w-4 text-emerald-600" />
              </div>
              <div>
                <p className="font-medium">Customer</p>
                <p className="text-gray-600">{firstName} {lastName}</p>
                <p className="text-gray-600">{phone}</p>
              </div>
            </div>

            <div className="flex">
              <div className="w-8">
                <MapPin className="h-4 w-4 text-emerald-600" />
              </div>
              <div>
                <p className="font-medium">Delivery Address</p>
                <p className="text-gray-600">{address}</p>
                <p className="text-gray-600">{postcode}</p>
              </div>
            </div>

            <div className="flex">
              <div className="w-8">
                <Clock className="h-4 w-4 text-emerald-600" />
              </div>
              <div className="flex-1">
                <p className="font-medium">Delivery & Pickup Times</p>
                {Object.keys(itemsByBusiness).map(businessId => {
                  const deliveryMethod = getDeliveryMethod(businessId);
                  const deliveryType = getDeliveryType(businessId);
                  const scheduledTimeStr = getScheduledTime(businessId);
                  const scheduledTime = scheduledTimeStr ? new Date(scheduledTimeStr) : null;

                  return (
                    <div key={`time-${businessId}`} className="text-gray-600 mb-1">
                      <span className="font-medium">{businessNames(businessId)}</span>: {deliveryMethod === 'delivery' ? 'Delivery' : 'Pickup'} -
                      {deliveryType === 'asap'
                        ? ' As soon as possible'
                        : scheduledTime
                          ? ` ${formatDeliveryTime(scheduledTime)}`
                          : ' Schedule not set'}
                    </div>
                  );
                })}
              </div>
            </div>

            <div className="flex">
              <div className="w-8">
                <CheckCircle className="h-4 w-4 text-emerald-600" />
              </div>
              <div>
                <p className="font-medium">Payment Method</p>
                <p className="text-gray-600">
                  {paymentMethod === 'card' ? 'Credit/Debit Card' : 'Cash on Delivery'}
                </p>
              </div>
            </div>
          </div>

          <Button
            onClick={handleSubmit}
            className="w-full mt-4 bg-emerald-600 hover:bg-emerald-700"
            disabled={!isFormComplete || isSubmitting}
          >
            {isSubmitting ? (
              <div className="flex items-center">
                <div className="animate-spin h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full"></div>
                Processing...
              </div>
            ) : (
              `Place Order (£${grandTotal.toFixed(2)})`
            )}
          </Button>
        </div>
      )}

      {/* Empty Cart Message */}
      {totalItems === 0 && (
        <div className="mt-6 text-center">
          <p className="text-gray-600 mb-4">Your cart is empty</p>
          <Button asChild>
            <Link href="/businesses">Browse Businesses</Link>
          </Button>
        </div>
      )}
    </div>
  );
};
