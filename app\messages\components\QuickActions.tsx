"use client"

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { 
  MessageSquare, 
  Package, 
  Utensils, 
  Truck,
  Clock,
  Star,
  ArrowRight
} from "lucide-react"
import { cn } from "@/lib/utils"

interface User {
  id: string
  email?: string
  user_metadata?: any
}

interface QuickAction {
  id: string
  label: string
  description: string
  icon: React.ReactNode
  priority: number
  context?: any
}

interface QuickActionsProps {
  user: User
  onActionSelected: (action?: QuickAction) => void
}

export function QuickActions({ user, onActionSelected }: QuickActionsProps) {
  const [actions, setActions] = useState<QuickAction[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    generateQuickActions()
  }, [user])

  const generateQuickActions = async () => {
    setIsLoading(true)
    
    try {
      // Simulate API call to get user context
      const userActions = await getUserContextActions(user)
      setActions(userActions)
    } catch (error) {
      console.error('Error generating quick actions:', error)
      // Fallback to default actions
      setActions(getDefaultActions())
    } finally {
      setIsLoading(false)
    }
  }

  const getUserContextActions = async (user: User): Promise<QuickAction[]> => {
    // This would normally be an API call
    // For now, simulate with mock data based on user capabilities
    
    const actions: QuickAction[] = []
    
    // Check for active orders
    const hasActiveOrders = await checkActiveOrders(user.id)
    if (hasActiveOrders) {
      actions.push({
        id: 'active-order',
        label: 'Message about current order',
        description: 'Get updates on your active delivery',
        icon: <Package className="h-5 w-5 text-orange-600" />,
        priority: 1,
        context: { type: 'active_order' }
      })
    }

    // Check user role capabilities
    const userRoles = getUserRoles(user)
    
    if (userRoles.includes('customer')) {
      actions.push({
        id: 'ask-business',
        label: 'Ask a business a question',
        description: 'Menu, allergens, availability',
        icon: <Utensils className="h-5 w-5 text-green-600" />,
        priority: 2,
        context: { type: 'customer_enquiry' }
      })
    }

    if (userRoles.includes('business')) {
      actions.push({
        id: 'coordinate-riders',
        label: 'Coordinate with riders',
        description: 'Delivery planning and logistics',
        icon: <Truck className="h-5 w-5 text-blue-600" />,
        priority: 2,
        context: { type: 'rider_coordination' }
      })
    }

    // Check for recent conversations
    const hasRecentConversations = await checkRecentConversations(user.id)
    if (hasRecentConversations) {
      actions.push({
        id: 'continue-recent',
        label: 'Continue recent conversation',
        description: 'Pick up where you left off',
        icon: <MessageSquare className="h-5 w-5 text-purple-600" />,
        priority: 3,
        context: { type: 'recent_conversation' }
      })
    }

    // Add general networking option
    actions.push({
      id: 'general-networking',
      label: 'Connect with someone new',
      description: 'Expand your network',
      icon: <Star className="h-5 w-5 text-emerald-600" />,
      priority: 4,
      context: { type: 'general_networking' }
    })

    return actions.sort((a, b) => a.priority - b.priority)
  }

  const getDefaultActions = (): QuickAction[] => [
    {
      id: 'ask-business',
      label: 'Ask a business a question',
      description: 'Menu, allergens, availability',
      icon: <Utensils className="h-5 w-5 text-green-600" />,
      priority: 1,
      context: { type: 'customer_enquiry' }
    },
    {
      id: 'general-networking',
      label: 'Start a conversation',
      description: 'Connect with businesses or riders',
      icon: <MessageSquare className="h-5 w-5 text-emerald-600" />,
      priority: 2,
      context: { type: 'general_networking' }
    }
  ]

  // Mock functions - these would be real API calls
  const checkActiveOrders = async (userId: string): Promise<boolean> => {
    // Simulate API call
    return Math.random() > 0.7 // 30% chance of having active orders
  }

  const checkRecentConversations = async (userId: string): Promise<boolean> => {
    // Simulate API call
    return Math.random() > 0.5 // 50% chance of having recent conversations
  }

  const getUserRoles = (user: User): string[] => {
    // Extract roles from user metadata or make API call
    // For now, assume customer role
    return ['customer']
  }

  if (isLoading) {
    return (
      <div className="px-4">
        <Card>
          <CardContent className="p-4">
            <div className="space-y-3">
              {[1, 2].map((i) => (
                <div key={i} className="animate-pulse">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (actions.length === 0) {
    return null
  }

  return (
    <div className="px-4">
      <Card>
        <CardContent className="p-4">
          <h3 className="text-sm font-medium text-gray-700 mb-3">Quick Actions</h3>
          <div className="space-y-2">
            {actions.slice(0, 3).map((action) => (
              <Button
                key={action.id}
                variant="ghost"
                className="w-full justify-start h-auto p-3 text-left"
                onClick={() => onActionSelected(action)}
              >
                <div className="flex items-center space-x-3 w-full">
                  <div className="flex-shrink-0">
                    {action.icon}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {action.label}
                    </p>
                    <p className="text-xs text-gray-500 truncate">
                      {action.description}
                    </p>
                  </div>
                  <ArrowRight className="h-4 w-4 text-gray-400 flex-shrink-0" />
                </div>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
