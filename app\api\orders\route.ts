import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { generateSequentialOrderNumber, generateBusinessOrderNumber } from './order-number-generator';

// Create Supabase client with service role key (only available server-side)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Helper function to verify user access
async function verifyUserAccess(request: NextRequest) {
  // Get the authorization header
  const authorization = request.headers.get('Authorization');

  // Check if we have an authorization header
  if (!authorization) {
    console.log("No authorization header found in orders API")
    // In development mode, allow guest orders without authentication
    if (process.env.NODE_ENV === 'development') {
      console.log("Development mode: Allowing guest order without authentication")
      return {
        authorized: true,
        // Return null user to indicate a guest order
        user: null
      };
    } else {
      return {
        authorized: false,
        error: "Authentication required",
        status: 401
      };
    }
  }

  console.log("Found authorization header in orders API, attempting to verify")

  // Extract the token
  const token = authorization.replace('Bearer ', '');

  try {
    // Verify the token
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      console.error("Invalid token in orders API:", error)

      // In development mode, allow guest orders without authentication
      if (process.env.NODE_ENV === 'development') {
        console.log("Development mode: Allowing guest order despite invalid token")
        return {
          authorized: true,
          // Return null user to indicate a guest order
          user: null
        };
      }

      return {
        authorized: false,
        error: "Invalid authentication token",
        status: 401
      };
    }

    console.log("Token verified for user in orders API:", user.email)
    return { authorized: true, user };
  } catch (authError) {
    console.error("Error verifying token in orders API:", authError)
    // In development mode, allow guest orders without authentication
    if (process.env.NODE_ENV === 'development') {
      console.log("Development mode: Allowing guest order despite auth error")
      return {
        authorized: true,
        // Return null user to indicate a guest order
        user: null
      };
    } else {
      return {
        authorized: false,
        error: "Authentication error",
        status: 401
      };
    }
  }
}

// GET endpoint to fetch a user's orders
export async function GET(request: NextRequest) {
  try {
    // Verify user access
    const accessCheck = await verifyUserAccess(request);
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      );
    }

    // Get the user from the access check
    const user = accessCheck.user;

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');
    const status = searchParams.get('status');

    // Fetch the user's orders
    let query = supabase
      .from('orders')
      .select(`
        *,
        order_items(*),
        order_status_history(*),
        order_payment_allocations(*)
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(limit)
      .range(offset, offset + limit - 1);

    // Add status filter if provided
    if (status) {
      query = query.eq('order_status', status);
    }

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching orders:', error);
      return NextResponse.json(
        { error: 'Failed to fetch orders', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      orders: data || [],
      count: count || 0
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}

// Import the transaction helpers
import { createOrderWithTransaction } from './transaction-helper';
import { createMultiBusinessOrder } from './multi-business-transaction-helper';

// POST endpoint to create a new order
export async function POST(request: NextRequest) {
  console.log('🔵 POST /api/orders - Order creation endpoint called');

  try {
    // Verify user access
    console.log('🔐 API: Verifying user access');
    const accessCheck = await verifyUserAccess(request);
    if (!accessCheck.authorized) {
      console.error('❌ API: User access verification failed:', accessCheck.error);
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      );
    }

    // Get the user from the access check
    const user = accessCheck.user;
    console.log('✅ API: User access verified:', user && user.email ? user.email : 'Guest user (no email)');

    // Parse the request body
    console.log('📦 API: Parsing request body');
    const body = await request.json();

    // Validate required fields
    const requiredFields = [
      'customerName', 'customerPhone', 'customerAddress',
      'paymentMethod', 'items', 'businesses', 'subtotal',
      'deliveryFee', 'serviceFee', 'total'
    ];

    // Log the received body for debugging
    console.log('Order API received body keys:', Object.keys(body));
    console.log('Order API received body:', {
      customerName: body.customerName,
      customerPhone: body.customerPhone,
      customerAddress: body.customerAddress,
      paymentMethod: body.paymentMethod,
      itemCount: body.items?.length || 0,
      businessCount: body.businesses?.length || 0,
      subtotal: body.subtotal,
      deliveryFee: body.deliveryFee,
      serviceFee: body.serviceFee,
      total: body.total,
      deliveryType: body.deliveryType
    });

    // Check for missing fields
    const missingFields = requiredFields.filter(field => {
      const value = body[field];
      const isMissing = value === undefined || value === null ||
                       (typeof value === 'string' && value.trim() === '') ||
                       (Array.isArray(value) && value.length === 0);

      if (isMissing) {
        console.log(`Order API: Missing required field: ${field}`);
      }

      return isMissing;
    });

    if (missingFields.length > 0) {
      return NextResponse.json(
        {
          error: `Missing required fields: ${missingFields.join(', ')}`,
          receivedFields: Object.keys(body)
        },
        { status: 400 }
      );
    }

    // Validate array fields
    if (!Array.isArray(body.items)) {
      return NextResponse.json(
        { error: 'items must be an array' },
        { status: 400 }
      );
    }

    if (!Array.isArray(body.businesses)) {
      return NextResponse.json(
        { error: 'businesses must be an array' },
        { status: 400 }
      );
    }

    // Prepare customer coordinates
    let customerCoordinates = null;
    if (body.customerCoordinates &&
        Array.isArray(body.customerCoordinates) &&
        body.customerCoordinates.length === 2) {
      customerCoordinates = `(${body.customerCoordinates[0]},${body.customerCoordinates[1]})`;
    }

    // Log the order parameters for debugging
    console.log('Order API parameters:', {
      customerName: body.customerName,
      customerPhone: body.customerPhone,
      customerEmail: body.customerEmail || (user?.email || null),
      customerAddress: body.customerAddress,
      paymentMethod: body.paymentMethod,
      // Don't log everything to keep the logs clean
    });

    // Generate a sequential order number
    const orderNumber = await generateSequentialOrderNumber();

    // Get the primary business ID from the first business in the array
    // First try to use businessNumericId, then fall back to id if it's a number
    let businessId = null;
    if (body.businesses && body.businesses.length > 0) {
      const firstBusiness = body.businesses[0];

      // First try to use businessNumericId if available
      if (firstBusiness.businessNumericId && !isNaN(parseInt(firstBusiness.businessNumericId.toString()))) {
        businessId = parseInt(firstBusiness.businessNumericId.toString());
        console.log(`Using businessNumericId: ${businessId} for primary business`);
      }
      // Then try to use id if it's a number
      else if (firstBusiness.id && !isNaN(parseInt(firstBusiness.id))) {
        businessId = parseInt(firstBusiness.id);
        console.log(`Using numeric id: ${businessId} for primary business`);
      } else {
        console.log(`Neither businessNumericId nor id is a valid integer for business "${firstBusiness.name || firstBusiness.id}", setting to null`);
      }
    }

    // Prepare order items for the transaction helper with simplified business ID handling
    const orderItems = body.items.map(item => {
      // Get product ID - simplified approach
      let productId = null;

      // First try to parse the item.id as an integer
      if (item.id && !isNaN(parseInt(item.id))) {
        productId = parseInt(item.id);
        console.log(`Using numeric product ID ${productId} from item.id for ${item.name || 'unknown item'}`);
      }
      // Then try product_id if available
      else if (item.product_id && !isNaN(parseInt(item.product_id.toString()))) {
        productId = parseInt(item.product_id.toString());
        console.log(`Using numeric product ID ${productId} from item.product_id for ${item.name || 'unknown item'}`);
      }
      // If no valid ID found, log an error
      else {
        console.error(`ERROR: No valid product ID found for item ${item.name || 'unknown'}`);
        // Use a placeholder ID for now, but this should be fixed in the product data
        productId = 0;
      }

      // Get business ID - simplified approach using numeric IDs directly
      let businessId = null;

      // First check if businessId is already a number
      if (item.businessId && !isNaN(Number(item.businessId))) {
        businessId = Number(item.businessId);
        console.log(`Using numeric businessId ${businessId} for item ${item.name || 'unknown'}`);
      }
      // Then try business_id if available
      else if (item.business_id && !isNaN(Number(item.business_id))) {
        businessId = Number(item.business_id);
        console.log(`Using business_id ${businessId} for item ${item.name || 'unknown'}`);
      }
      // Then try businessNumericId if available
      else if (item.businessNumericId && !isNaN(Number(item.businessNumericId))) {
        businessId = Number(item.businessNumericId);
        console.log(`Using businessNumericId ${businessId} for item ${item.name || 'unknown'}`);
      }
      // If we still don't have a valid business ID, log an error
      else {
        console.error(`ERROR: No valid business ID found for item ${item.name || 'unknown'}`);
        console.error(`Item data: ${JSON.stringify(item)}`);
        // Use a placeholder ID for now, but this should be fixed in the product data
        businessId = 0;
      }

      // Get product name
      const productName = item.name || 'Unknown Product';

      return {
        product_id: productId,
        product_name: productName,
        business_id: businessId,
        quantity: item.quantity,
        price: item.price,
        notes: item.notes || '',
        options: item.options || null
      };
    });

    // Filter out items with invalid business IDs
    const validOrderItems = orderItems.filter(item => item.business_id !== 0);

    if (validOrderItems.length !== orderItems.length) {
      console.warn(`Filtered out ${orderItems.length - validOrderItems.length} items with invalid business IDs`);
    }

    console.log(`Created ${validOrderItems.length} order items with valid business IDs`);

    // Prepare order businesses for the transaction helper
    // First, look up the actual business IDs from the businesses table using the slugs
    const businessSlugs = body.businesses.map(business => business.id);
    let businessIdMap = {};

    if (businessSlugs.length > 0) {
      try {
        console.log('Looking up business IDs for slugs:', businessSlugs);

        // Try to look up by slug first
        const { data: businessDataBySlug, error: slugLookupError } = await supabase
          .from('businesses')
          .select('id, slug, name')
          .in('slug', businessSlugs);

        if (slugLookupError) {
          console.error('Error looking up business IDs by slug:', slugLookupError);
        } else if (businessDataBySlug && businessDataBySlug.length > 0) {
          // Create a mapping from slug to actual ID
          businessIdMap = businessDataBySlug.reduce((map, business) => {
            map[business.slug] = business.id;
            console.log(`Mapped slug ${business.slug} to ID ${business.id} (${business.name})`);
            return map;
          }, {});
        } else {
          console.log('No businesses found by slug, trying to look up by name...');

          // If no businesses found by slug, try looking up by name
          const businessNames = body.businesses.map(business => business.name).filter(Boolean);

          if (businessNames.length > 0) {
            const { data: businessDataByName, error: nameLookupError } = await supabase
              .from('businesses')
              .select('id, slug, name')
              .in('name', businessNames);

            if (nameLookupError) {
              console.error('Error looking up business IDs by name:', nameLookupError);
            } else if (businessDataByName && businessDataByName.length > 0) {
              // Create a mapping from name to actual ID
              const nameToIdMap = businessDataByName.reduce((map, business) => {
                map[business.name.toLowerCase()] = business.id;
                console.log(`Mapped name ${business.name} to ID ${business.id}`);
                return map;
              }, {});

              // Add name mappings to the businessIdMap
              body.businesses.forEach(business => {
                if (business.name && nameToIdMap[business.name.toLowerCase()]) {
                  businessIdMap[business.id] = nameToIdMap[business.name.toLowerCase()];
                  console.log(`Mapped slug ${business.id} to ID ${nameToIdMap[business.name.toLowerCase()]} via name ${business.name}`);
                }
              });
            } else {
              console.log('No businesses found by name either');
            }
          }
        }

        console.log('Final business ID mapping:', businessIdMap);
      } catch (err) {
        console.error('Exception looking up business IDs:', err);
      }
    }

    // Now create the order businesses with simplified business ID handling
    // Use Promise.all with async map function to handle async operations inside the map
    const orderBusinesses = await Promise.all(body.businesses.map(async (business, index) => {
      // Get business ID - simplified approach using numeric IDs directly
      let numericBusinessId = null;

      // First, check if we have a valid numeric business ID
      if (business.business_id && !isNaN(Number(business.business_id))) {
        // Use the numeric business ID directly
        numericBusinessId = Number(business.business_id);
        console.log(`Using numeric business_id: ${numericBusinessId} for business ${business.business_name || business.name || 'unknown'}`);
      }
      // Then check if businessId is a number
      else if (business.businessId && !isNaN(Number(business.businessId))) {
        // Use the numeric business ID directly
        numericBusinessId = Number(business.businessId);
        console.log(`Using numeric businessId: ${numericBusinessId} for business ${business.business_name || business.name || 'unknown'}`);
      }
      // Then check if business.id is a number
      else if (business.id && !isNaN(Number(business.id))) {
        numericBusinessId = Number(business.id);
        console.log(`Using numeric ID from business.id: ${numericBusinessId}`);
      }
      // If we still don't have a valid ID, log an error
      else {
        console.error(`ERROR: No valid numeric business ID found for business: ${business.business_name || business.name || 'unknown'}`);
        console.error(`Business data: ${JSON.stringify(business)}`);
        console.error(`This order may need manual intervention to associate with the correct business.`);
        return null; // Skip this business
      }

      // Fetch additional business details if needed
      let businessName = business.business_name || business.name;
      let businessType = business.business_type || business.type;
      let businessSlug = business.business_slug || business.businessSlug || '';

      // If we're missing any essential details, fetch them from the database
      if (!businessName || !businessType || !businessSlug) {
        try {
          console.log(`Fetching additional details for business ID: ${numericBusinessId}`);

          const { data: businessData, error: businessError } = await supabase
            .from('businesses')
            .select('id, name, type, slug')
            .eq('id', numericBusinessId)
            .single();

          if (!businessError && businessData) {
            // Use the database values for any missing fields
            businessName = businessName || businessData.name;
            businessType = businessType || businessData.type;
            businessSlug = businessSlug || businessData.slug;

            console.log(`Found business details: ${businessData.name} (ID: ${businessData.id}, Type: ${businessData.type})`);
          } else {
            console.error(`Error fetching business details for ID ${numericBusinessId}:`, businessError);
          }
        } catch (err) {
          console.error(`Exception fetching business details for ID ${numericBusinessId}:`, err);
        }
      }

      // Create a record with all the business details
      // Create business record with only the fields that are provided
      const orderBusiness: any = {
        business_id: numericBusinessId,
        business_name: businessName || `Business ${numericBusinessId}`,
        business_type: businessType || 'restaurant',
        status: business.status || 'pending'
      };

      // Add business slug if available (for reference only)
      if (businessSlug) orderBusiness.business_slug = businessSlug;

      // Financial fields - only include if they exist
      if (business.subtotal !== undefined) orderBusiness.subtotal = business.subtotal;
      if (business.deliveryFee !== undefined) orderBusiness.delivery_fee = business.deliveryFee;
      if (business.serviceFee !== undefined) orderBusiness.service_fee = business.serviceFee;
      if (business.total !== undefined) orderBusiness.total = business.total;

      // Time fields - only include if they exist
      if (business.preparationTime !== undefined) orderBusiness.preparation_time = business.preparationTime;
      if (business.estimatedDeliveryTime !== undefined) orderBusiness.estimated_delivery_time = business.estimatedDeliveryTime;
      if (business.estimated_delivery_time !== undefined) orderBusiness.estimated_delivery_time = business.estimated_delivery_time;

      // Debug logging for delivery time
      console.log('DEBUG: Business delivery time data:', {
        business_id: business.business_id || business.businessId,
        snake_case: business.estimated_delivery_time,
        camelCase: business.estimatedDeliveryTime,
        debug_source: business._debug_delivery_time_source
      });

      // Log the business data for debugging
      console.log(`Business data for ID ${numericBusinessId}:`, {
        name: businessName,
        type: businessType,
        slug: businessSlug,
        subtotal: business.subtotal,
        deliveryFee: business.deliveryFee,
        serviceFee: business.serviceFee,
        total: business.total,
        preparationTime: business.preparationTime,
        estimatedDeliveryTime: business.estimatedDeliveryTime
      });

      // Log the business record for debugging
      console.log(`Prepared order_business record: ID=${numericBusinessId}, Name=${businessName}, Type=${businessType}, Subtotal=${business.subtotal}, DeliveryFee=${business.deliveryFee}`);

      return orderBusiness;
    }));

    // Filter out null values (businesses that couldn't be processed)
    const validOrderBusinesses = orderBusinesses.filter(business => business !== null);

    console.log(`Created ${validOrderBusinesses.length} order business records:`,
      validOrderBusinesses.map(b => ({
        business_id: b.business_id,
        business_name: b.business_name,
        business_type: b.business_type
      }))
    );

    // Use transaction helper to create order and related records
    try {
      console.log('Creating order using transaction helper...');

      // Prepare the order data - include all columns that exist in the orders table
      // Get the first business for the main order data
      const firstBusiness = validOrderBusinesses.length > 0 ? validOrderBusinesses[0] : null;

      // Prepare the order data with all fields
      const orderInsertData = {
        order_number: body.orderNumber || orderNumber,
        customer_name: body.customerName,
        // Only use user.email if user exists, otherwise use email from form or null
        customer_email: body.customerEmail || null,
        customer_phone: body.customerPhone,
        customer_address: body.customerAddress || '',
        delivery_address: body.customerAddress || '', // Required field
        payment_method: body.paymentMethod,
        payment_status: body.paymentStatus || 'pending',
        notes: body.instructions || '',
        total: body.total || 0,
        customer_coordinates: body.customerCoordinates ? `(${body.customerCoordinates[0]},${body.customerCoordinates[1]})` : null,
        delivery_instructions: body.instructions || '',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Add business details from the first business
      if (firstBusiness) {
        console.log('Adding business details from first business:', {
          business_id: firstBusiness.business_id,
          business_name: firstBusiness.business_name,
          business_type: firstBusiness.business_type,
          business_slug: firstBusiness.business_slug,
          subtotal: firstBusiness.subtotal,
          delivery_fee: firstBusiness.delivery_fee,
          service_fee: firstBusiness.service_fee,
          preparation_time: firstBusiness.preparation_time,
          estimated_delivery_time: firstBusiness.estimated_delivery_time
        });

        // Make sure business_id is a number
        const businessId = Number(firstBusiness.business_id);
        if (!isNaN(businessId) && businessId > 0) {
          orderInsertData.business_id = businessId;
          console.log(`Using numeric business_id: ${businessId}`);
        } else {
          console.error(`Invalid business_id: ${firstBusiness.business_id} is not a valid positive number`);
        }

        // Add business details
        orderInsertData.business_name = firstBusiness.business_name;
        orderInsertData.business_type = firstBusiness.business_type;

        if (firstBusiness.business_slug) {
          orderInsertData.business_slug = firstBusiness.business_slug;
        }

        // Add financial details - use values from the business record if available, otherwise use body values
        orderInsertData.subtotal = firstBusiness.subtotal || body.subtotal || 0;
        orderInsertData.delivery_fee = firstBusiness.delivery_fee || body.deliveryFee || 0;
        orderInsertData.service_fee = firstBusiness.service_fee || body.serviceFee || 0;

        // Add status and time details
        orderInsertData.status = firstBusiness.status || 'pending';
        orderInsertData.preparation_time = firstBusiness.preparation_time || 15;

        // Debug logging for delivery time
        console.log('DEBUG: First business delivery time data:', {
          business_id: firstBusiness.business_id,
          snake_case: firstBusiness.estimated_delivery_time,
          camelCase: firstBusiness.estimatedDeliveryTime,
          debug_source: firstBusiness._debug_delivery_time_source
        });

        // Check for delivery time in multiple places
        if (firstBusiness.estimated_delivery_time !== undefined) {
          orderInsertData.estimated_delivery_time = firstBusiness.estimated_delivery_time;
          console.log(`Using estimated_delivery_time from first business: ${firstBusiness.estimated_delivery_time}`);
        } else if (firstBusiness.estimatedDeliveryTime !== undefined) {
          orderInsertData.estimated_delivery_time = firstBusiness.estimatedDeliveryTime;
          console.log(`Using estimatedDeliveryTime from first business: ${firstBusiness.estimatedDeliveryTime}`);
        } else {
          orderInsertData.estimated_delivery_time = 20; // Default value
          console.log(`Using default estimated_delivery_time: 20`);
        }

        console.log('Final business details for order:', {
          business_id: orderInsertData.business_id,
          business_name: orderInsertData.business_name,
          business_type: orderInsertData.business_type,
          business_slug: orderInsertData.business_slug,
          subtotal: orderInsertData.subtotal,
          delivery_fee: orderInsertData.delivery_fee,
          service_fee: orderInsertData.service_fee,
          status: orderInsertData.status,
          preparation_time: orderInsertData.preparation_time,
          estimated_delivery_time: orderInsertData.estimated_delivery_time
        });
      } else {
        console.log('No business details available for order');
      }

      // If we have a valid user with an email, use it
      if (user && user.email) {
        orderInsertData.customer_email = user.email;
        console.log(`Using authenticated user email: ${user.email}`);
      } else if (body.customerEmail) {
        orderInsertData.customer_email = body.customerEmail;
        console.log(`Using email from form: ${body.customerEmail}`);
      } else {
        console.log('No email provided, using null');
      }

      // Log the minimal order data
      console.log('Using minimal order data with only valid columns:', orderInsertData);

      // Only include user_id if we have a valid authenticated user
      if (user && user.id) {
        try {
          // Ensure it's a valid UUID
          if (typeof user.id === 'string' && user.id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
            orderInsertData['user_id'] = user.id;
            console.log(`Using authenticated user ID: ${user.id}`);
          } else {
            console.log(`Invalid user ID format: ${user.id}, not including in order data`);
          }
        } catch (error) {
          console.error('Error processing user ID:', error);
          console.log('Creating order without user_id due to error');
        }
      } else {
        console.log('No authenticated user, creating a guest order without user_id');
      }

      // Include business_id in the orders table for single-business orders
      // For multi-business orders, we'll create separate order rows for each business
      // Make sure we have the business details from the first business in the order

      console.log('Creating order with transaction helper using data:', {
        orderData: orderInsertData,
        itemCount: orderItems.length,
        businessCount: orderBusinesses.length
      });

      // Check if we have multiple businesses
      const isMultiBusinessOrder = validOrderBusinesses.length > 1;

      if (isMultiBusinessOrder) {
        console.log(`Creating multi-business order with ${validOrderBusinesses.length} businesses`);

        // Create separate order rows for each business
        console.log('🚀 Calling createMultiBusinessOrder with:', {
          businessCount: validOrderBusinesses.length,
          itemCount: validOrderItems.length
        });

        // Note: We're still using validOrderBusinesses here because the createMultiBusinessOrder function
        // needs this information to create separate orders for each business
        console.log('🚀 CALLING createMultiBusinessOrder with data:', {
          orderData: JSON.stringify(orderInsertData, null, 2),
          itemCount: validOrderItems.length,
          businessCount: validOrderBusinesses.length,
          businesses: validOrderBusinesses.map(b => ({ id: b.business_id, name: b.business_name }))
        });

        // Log the exact data being passed to the transaction helper
        console.log('🔍 DETAILED DATA BEING PASSED:');
        console.log('📋 Order Data:', JSON.stringify(orderInsertData, null, 2));
        console.log('📦 Order Items:', JSON.stringify(validOrderItems, null, 2));
        console.log('🏪 Order Businesses:', JSON.stringify(validOrderBusinesses, null, 2));

        const orderIds = await createMultiBusinessOrder(
          orderInsertData,
          validOrderItems,
          validOrderBusinesses
        );

        console.log('🔄 createMultiBusinessOrder returned:', orderIds);

        if (!orderIds || orderIds.length === 0) {
          console.error('❌ Transaction helper returned invalid order IDs:', orderIds);
          console.error('❌ Expected: Array with order IDs, Got:', typeof orderIds, orderIds);
          throw new Error('Transaction helper did not return any valid order IDs');
        }

        console.log(`Created ${orderIds.length} separate orders for multi-business order`);

        // Verify the orders were actually created in the database
        const { data: verifiedOrders, error: verifyError } = await supabase
          .from('orders')
          .select('id')
          .in('id', orderIds);

        if (verifyError) {
          throw new Error(`Order creation verification failed: ${verifyError.message}`);
        }

        if (!verifiedOrders || verifiedOrders.length !== orderIds.length) {
          throw new Error(`Order creation verification failed: Expected ${orderIds.length} orders, found ${verifiedOrders?.length || 0}`);
        }

        console.log('Orders verified in database:', verifiedOrders.map(o => o.id).join(', '));

        // Fetch the created orders with all related data
        const { data: fetchedOrdersData, error: ordersError } = await supabase
          .from('orders')
          .select(`
            *,
            order_items(*)
          `)
          .in('id', orderIds);

        if (ordersError) {
          console.error('Error fetching created orders:', ordersError);
          return NextResponse.json(
            { error: 'Orders created but could not fetch details', details: ordersError.message },
            { status: 500 }
          );
        }

        console.log('Orders fetched with all related data:', {
          count: fetchedOrdersData?.length || 0,
          order_ids: fetchedOrdersData?.map(o => o.id) || []
        });

        // If we have a cart ID, mark the cart items as ordered
        // For multi-business orders, we'll use the first order ID as a reference
        if (body.cartId && orderIds.length > 0) {
          try {
            console.log('Updating cart items with order reference for cart ID:', body.cartId);
            const { error: updateError } = await supabase
              .from('cart_items')
              .update({ order_id: orderIds[0] })
              .eq('cart_id', body.cartId);

            if (updateError) {
              console.error('Error updating cart items with order reference:', updateError);
            } else {
              console.log('Successfully updated cart items with order reference');
            }
          } catch (cartErr) {
            console.error('Exception updating cart items:', cartErr);
            // Continue anyway - this is not critical
          }
        }

        return NextResponse.json({
          success: true,
          isMultiBusiness: true,
          orderIds: orderIds,
          orders: fetchedOrdersData
        });
      } else {
        console.log('Creating single-business order');

        // Create a single order with the traditional approach
        const orderId = await createOrderWithTransaction(
          orderInsertData,
          validOrderItems
        );

        if (!orderId) {
          throw new Error('Transaction helper did not return a valid order ID');
        }

        console.log('Order created successfully with ID:', orderId);

        // Verify the order was actually created in the database
        const { data: verifiedOrder, error: verifyError } = await supabase
          .from('orders')
          .select('id')
          .eq('id', orderId)
          .single();

        if (verifyError || !verifiedOrder) {
          throw new Error(`Order creation verification failed: ${verifyError?.message || 'Order not found in database'}`);
        }

        console.log('Order verified in database:', verifiedOrder.id);

        // Fetch the created order with all related data
        const { data: fetchedOrderData, error: orderError } = await supabase
          .from('orders')
          .select(`
            *,
            order_items(*)
          `)
          .eq('id', orderId)
          .single();

        if (orderError) {
          console.error('Error fetching created order:', orderError);
          return NextResponse.json(
            { error: 'Order created but could not fetch details', details: orderError.message },
            { status: 500 }
          );
        }

        console.log('Order fetched with all related data:', {
          id: fetchedOrderData.id,
          order_number: fetchedOrderData.order_number,
          items_count: fetchedOrderData.order_items?.length || 0
        });

        // If we have a cart ID, mark the cart items as ordered
        if (body.cartId) {
          try {
            console.log('Updating cart items with order reference for cart ID:', body.cartId);
            const { error: updateError } = await supabase
              .from('cart_items')
              .update({ order_id: orderId })
              .eq('cart_id', body.cartId);

            if (updateError) {
              console.error('Error updating cart items with order reference:', updateError);
            } else {
              console.log('Successfully updated cart items with order reference');
            }
          } catch (cartErr) {
            console.error('Exception updating cart items:', cartErr);
            // Continue anyway - this is not critical
          }
        }

        return NextResponse.json({
          success: true,
          isMultiBusiness: false,
          orderId: orderId,
          order: fetchedOrderData
        });
      }
    } catch (transactionError) {
      console.error('Error in order transaction:', transactionError);
      console.error('Error stack:', transactionError.stack);

      // Log more details about the error
      if (transactionError.cause) {
        console.error('Error cause:', transactionError.cause);
      }

      // Return detailed error information in development mode
      return NextResponse.json(
        {
          error: 'Failed to create order',
          details: transactionError.message,
          stack: process.env.NODE_ENV === 'development' ? transactionError.stack : undefined,
          orderData: process.env.NODE_ENV === 'development' ? {
            itemCount: validOrderItems.length,
            businessCount: validOrderBusinesses.length,
            businesses: validOrderBusinesses.map(b => ({
              business_id: b.business_id,
              business_name: b.business_name
            }))
          } : undefined
        },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('Unexpected error in orders API:', error);
    console.error('Error stack trace:', error?.stack);

    // Log more details about the error
    if (error.cause) {
      console.error('Error cause:', error.cause);
    }

    if (error.response) {
      console.error('Error response:', error.response);
    }

    return NextResponse.json(
      {
        error: 'An unexpected error occurred',
        details: error?.message || 'Unknown error',
        stack: process.env.NODE_ENV === 'development' ? error?.stack : undefined
      },
      { status: 500 }
    );
  }
}
