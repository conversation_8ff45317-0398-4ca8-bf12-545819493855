const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function createWorkingDummyData() {
  console.log('🗄️ Creating Working Dummy Messaging Data\n');

  try {
    // Get existing users
    const { data: users } = await supabase.auth.admin.listUsers();

    if (users.users.length < 2) {
      console.log('❌ Need at least 2 users');
      return;
    }

    const user1 = users.users[0]; // <EMAIL>
    const user2 = users.users[1]; // <EMAIL>
    const user3 = users.users.length > 2 ? users.users[2] : user1;

    console.log(`Using users: ${user1.email}, ${user2.email}, ${user3.email}\n`);

    // Create realistic conversations using working channel types
    const conversations = [
      {
        thread_id: 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
        channel_type: 'customer_enquiries',
        messages: [
          {
            sender_id: user1.id,
            recipient_id: user2.id,
            content: 'Hi! Do you have any vegan options on your menu?',
            subject: 'Vegan Options Inquiry',
            message_type: 'chat',
            created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString() // 2 hours ago
          },
          {
            sender_id: user2.id,
            recipient_id: user1.id,
            content: 'Yes! We have several delicious vegan dishes. Our vegan curry is very popular!',
            message_type: 'chat',
            created_at: new Date(Date.now() - 1.5 * 60 * 60 * 1000).toISOString() // 1.5 hours ago
          },
          {
            sender_id: user1.id,
            recipient_id: user2.id,
            content: 'Perfect! Could you tell me more about the ingredients in the vegan curry?',
            message_type: 'chat',
            created_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString() // 1 hour ago
          }
        ]
      },
      {
        thread_id: 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb',
        channel_type: 'active_order_delivery',
        messages: [
          {
            sender_id: user2.id,
            recipient_id: user1.id,
            content: 'Your order has been confirmed and is being prepared.',
            subject: 'Order Confirmed',
            message_type: 'status_update',
            is_automated: true,
            priority: 1,
            created_at: new Date(Date.now() - 45 * 60 * 1000).toISOString() // 45 minutes ago
          },
          {
            sender_id: user2.id,
            recipient_id: user1.id,
            content: 'Your order is ready for pickup!',
            subject: 'Order Ready',
            message_type: 'status_update',
            is_automated: true,
            is_urgent: true,
            priority: 1,
            created_at: new Date(Date.now() - 15 * 60 * 1000).toISOString() // 15 minutes ago
          },
          {
            sender_id: user1.id,
            recipient_id: user2.id,
            content: 'Great! I\'ll be there in 10 minutes to pick it up.',
            message_type: 'chat',
            created_at: new Date(Date.now() - 12 * 60 * 1000).toISOString() // 12 minutes ago
          }
        ]
      },
      {
        thread_id: 'cccccccc-cccc-cccc-cccc-cccccccccccc',
        channel_type: 'pre_order_planning',
        messages: [
          {
            sender_id: user1.id,
            recipient_id: user2.id,
            content: 'Hi! I\'m planning a party for next weekend. Do you do catering orders for about 20 people?',
            subject: 'Catering Inquiry',
            message_type: 'chat',
            created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString() // 3 days ago
          },
          {
            sender_id: user2.id,
            recipient_id: user1.id,
            content: 'Absolutely! We love catering orders. For 20 people, we can offer a special menu. Would you like to see our catering options?',
            message_type: 'chat',
            created_at: new Date(Date.now() - 2.5 * 24 * 60 * 60 * 1000).toISOString() // 2.5 days ago
          }
        ]
      },
      {
        thread_id: 'dddddddd-dddd-dddd-dddd-dddddddddddd',
        channel_type: 'business_networking',
        messages: [
          {
            sender_id: user2.id,
            recipient_id: user3.id,
            content: 'Hey! I noticed you\'re also in the food business. Would you be interested in a partnership for lunch deliveries?',
            subject: 'Partnership Opportunity',
            message_type: 'chat',
            created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString() // 5 days ago
          },
          {
            sender_id: user3.id,
            recipient_id: user2.id,
            content: 'That sounds interesting! What kind of partnership did you have in mind?',
            message_type: 'chat',
            created_at: new Date(Date.now() - 4.5 * 24 * 60 * 60 * 1000).toISOString() // 4.5 days ago
          }
        ]
      },
      {
        thread_id: 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee',
        channel_type: 'post_order_feedback',
        messages: [
          {
            sender_id: user1.id,
            recipient_id: user2.id,
            content: 'The food was absolutely delicious! Thank you for the excellent service.',
            subject: 'Great Experience!',
            message_type: 'chat',
            created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString() // 1 day ago
          },
          {
            sender_id: user2.id,
            recipient_id: user1.id,
            content: 'Thank you so much for the feedback! We\'re thrilled you enjoyed your meal. Looking forward to serving you again!',
            message_type: 'chat',
            created_at: new Date(Date.now() - 20 * 60 * 60 * 1000).toISOString() // 20 hours ago
          }
        ]
      }
    ];

    console.log('Creating conversations and messages...\n');

    // Insert all messages
    for (const conversation of conversations) {
      console.log(`📝 Creating conversation: ${conversation.channel_type}`);

      for (let i = 0; i < conversation.messages.length; i++) {
        const message = conversation.messages[i];

        const messageData = {
          sender_id: message.sender_id,
          recipient_id: message.recipient_id,
          thread_id: conversation.thread_id,
          channel_type: conversation.channel_type,
          message_type: message.message_type,
          subject: message.subject,
          content: message.content,
          is_automated: message.is_automated || false,
          is_urgent: message.is_urgent || false,
          is_read: i < conversation.messages.length - 1, // Mark all but last message as read
          priority: message.priority || 0,
          created_at: message.created_at
        };

        const { data, error } = await supabase
          .from('communications')
          .insert(messageData)
          .select();

        if (error) {
          console.log(`     ❌ Message error: ${error.message}`);
        } else {
          console.log(`     ✅ Message created: "${message.content.substring(0, 40)}..."`);
        }
      }
    }

    // Create some connection profiles for better display names (without profile_type)
    console.log('\n👤 Creating connection profiles...');

    const profiles = [
      {
        user_id: user1.id,
        display_name: 'John Customer',
        bio: 'Food lover and regular customer',
        is_public: true,
        allow_direct_messages: true
      },
      {
        user_id: user2.id,
        display_name: 'Bengal Spice Restaurant',
        bio: 'Authentic Indian cuisine with fresh ingredients',
        is_public: true,
        allow_direct_messages: true
      },
      {
        user_id: user3.id,
        display_name: 'Dominos Pizza',
        bio: 'Fresh pizza delivered fast',
        is_public: true,
        allow_direct_messages: true
      }
    ];

    for (const profile of profiles) {
      const { error } = await supabase
        .from('connection_profiles')
        .upsert(profile, { onConflict: 'user_id' });

      if (error) {
        console.log(`   ⚠️  Profile error: ${error.message}`);
      } else {
        console.log(`   ✅ Profile created: ${profile.display_name}`);
      }
    }

    console.log('\n🎉 Working dummy data creation completed!');
    console.log('\n📊 Summary:');
    console.log(`   👥 Used ${users.users.length} existing users`);
    console.log(`   💬 Created ${conversations.length} conversation threads`);
    console.log('   📝 Multiple messages per conversation');
    console.log('   🤖 Automated and manual messages');
    console.log('   📋 All channel types: customer_enquiries, active_order_delivery, pre_order_planning, business_networking, post_order_feedback');
    console.log('\n🎯 You can now test the messaging system!');
    console.log('   Go to: http://localhost:3000/messages');

  } catch (error) {
    console.error('❌ Error creating dummy data:', error);
  }
}

createWorkingDummyData();
