"use client"

/**
 * Generic Business Detail Page
 *
 * This page displays details for any type of business.
 * It uses a simplified layout that avoids the section with image, business details,
 * More Info and View Map buttons to prevent caching issues.
 */

import { useState, useEffect, useMemo, useRef, useCallback } from "react"
import React from "react"
import Link from "next/link"
import { MapPin, Phone, Mail, Info, Star, UtensilsCrossed, ShoppingBag, ChevronLeft, ChevronRight, Search, Tag, X, Map as MapIcon, Clock, Bike, DollarSign, Truck, AlertCircle } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { getBusinessById } from "@/services/generic-business-service"
import { notFound } from "next/navigation"
import MenuCategory from "@/components/menu-category"
import ProductItem from "@/components/product-item"
import { useRealtimeCart } from "@/context/realtime-cart-context"
import { CopyButton } from "@/components/copy-button"
import { useBusinessRealtime } from "@/hooks/use-business-realtime"
import BusinessInfoDialog from "@/components/business-info-dialog"
import ClickablePhone from "@/components/clickable-phone"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import CategoryScroller from "@/components/category-scroller"
import OSMRestaurantMap from "@/components/osm-restaurant-map"
import FallbackImage from "@/components/fallback-image"
import { useSupabaseRealtime } from "@/hooks/use-supabase-realtime"
import { supabase, supabaseRealtime } from "@/lib/supabase"
import CheckoutLink from "@/components/checkout-link"
import {
  calculateDistance,
  calculateDeliveryFeeWithDistance,
  getUserCoordinates,
  formatDeliveryFee
} from '@/lib/distance-calculation'
import { useDeliveryCalculation } from '@/hooks/use-delivery-calculation'

// Default coordinates for St Helier (center of Jersey)
const DEFAULT_COORDINATES: [number, number] = [-2.1053, 49.1805];

// Default delivery radius in kilometers
const DEFAULT_DELIVERY_RADIUS = 5;

export default function BusinessPage({ params }: { params: { id: string } }) {
  // Unwrap params with React.use() as recommended by Next.js
  const unwrappedParams = React.use(params as any) as { id: string }
  const id = unwrappedParams.id
  const [business, setBusiness] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const {
    cart,
    getItemsByBusiness,
    getDeliveryMethod,
    setDeliveryMethod: setCartDeliveryMethod,
    getDeliveryFee,
    setDeliveryFee: setCartDeliveryFee,
    setPreparationTime,
    setDeliveryTime,
    getDeliveryTime,
    getDeliveryType,
    setDeliveryType,
    getScheduledTime,
    setScheduledTime
  } = useRealtimeCart()
  const [businessItems, setBusinessItems] = useState<any[]>([])
  const [businessSubtotal, setBusinessSubtotal] = useState(0)
  const [activeCategory, setActiveCategory] = useState<string>("")
  const [searchQuery, setSearchQuery] = useState("")
  const [showInfoDialog, setShowInfoDialog] = useState(false)
  const [showOSMMapDialog, setShowOSMMapDialog] = useState(false)

  // Ref to track if we're programmatically scrolling
  const isScrolling = useRef(false)

  // State for coordinates and delivery radius with default values
  const [coordinates, setCoordinates] = useState<[number, number]>(DEFAULT_COORDINATES)
  const [deliveryRadius, setDeliveryRadius] = useState<number>(DEFAULT_DELIVERY_RADIUS)

  // State for business coordinates from the database
  const [businessCoordinates, setBusinessCoordinates] = useState<[number, number] | null>(null)

  // State for preparation time with direct management
  const [prepTime, setPrepTime] = useState<number | null>(null)
  const [prepTimeLoading, setPrepTimeLoading] = useState(true)
  const [prepTimeError, setPrepTimeError] = useState<string | null>(null)

  // Get real-time business data updates
  const { prepTime: realtimePrepTime, deliveryAvailable: realtimeDeliveryAvailable } = useBusinessRealtime(id)

  // State for delivery calculation results
  const [deliveryData, setDeliveryData] = useState({
    fee: 'Calculating...',
    feeNumeric: 0,
    time: 'Calculating...',
    timeRange: 'Calculating...',
    travelTimeMinutes: 0,
    totalTimeMinutes: 0,
    isLoading: true
  })

  // Calculate delivery fee when business data loads or fee model changes
  useEffect(() => {
    const calculateDeliveryFee = async () => {
      if (!business || !businessCoordinates) {
        console.log('Missing business data or coordinates for delivery calculation')
        return
      }

      try {
        setDeliveryData(prev => ({ ...prev, isLoading: true }))

        const { getUserLocationData, calculateDeliveryEstimates } = await import('@/lib/delivery-calculation-service')

        const { postcode, coordinates } = getUserLocationData()

        if (!postcode || !coordinates) {
          setDeliveryData({
            fee: 'Location required',
            feeNumeric: 0,
            time: 'Unknown',
            timeRange: 'Set location first',
            travelTimeMinutes: 0,
            totalTimeMinutes: 0,
            isLoading: false
          })
          return
        }

        const params = {
          businessId: business.id || id,
          businessName: business.name || '',
          businessCoordinates,
          preparationTimeMinutes: realtimePrepTime !== null ? realtimePrepTime : (business.preparationTimeMinutes || business.preparation_time_minutes || 15),
          deliveryFeeModel: business.delivery_fee_model || 'fixed',
          deliveryFee: business.deliveryFee || business.delivery_fee || 2.50,
          deliveryFeePerKm: business.delivery_fee_per_km || 0.50,
          customerPostcode: postcode,
          customerCoordinates: coordinates
        }

        console.log('Calculating delivery fee with params:', params)
        const result = await calculateDeliveryEstimates(params)

        setDeliveryData({
          ...result,
          isLoading: false
        })

        console.log('Delivery calculation result:', result)

        // Automatically save the calculated delivery fee to cart context if delivery method is set to delivery
        if (result.fee && typeof result.fee === 'number' && business?.id) {
          const currentDeliveryMethod = getDeliveryMethod(business.id.toString())
          if (currentDeliveryMethod === 'delivery') {
            console.log(`💰 Auto-saving calculated delivery fee £${result.fee.toFixed(2)} to cart for business ${business.id}`)
            setCartDeliveryFee(business.id.toString(), result.fee)
          } else {
            console.log(`💰 Delivery fee calculated (£${result.fee.toFixed(2)}) but not saved because delivery method is ${currentDeliveryMethod}`)
          }
        }

      } catch (error) {
        console.error('Error calculating delivery fee:', error)
        setDeliveryData(prev => ({
          ...prev,
          fee: 'Error calculating',
          isLoading: false
        }))
      }
    }

    calculateDeliveryFee()
  }, [
    business?.id,
    business?.name,
    business?.delivery_fee_model,
    business?.deliveryFee,
    business?.delivery_fee,
    business?.delivery_fee_per_km,
    business?.preparationTimeMinutes,
    business?.preparation_time_minutes,
    businessCoordinates,
    realtimePrepTime,
    id
  ])

  // Recalculate when user location changes
  useEffect(() => {
    const handleLocationChange = () => {
      console.log('User location changed, recalculating delivery fee...')
      // Trigger recalculation by updating a dependency
      if (business && businessCoordinates) {
        // The calculation will be triggered by the effect above
        setDeliveryData(prev => ({ ...prev, isLoading: true }))
      }
    }

    // Listen for location changes
    window.addEventListener('userLocationChanged', handleLocationChange)

    return () => {
      window.removeEventListener('userLocationChanged', handleLocationChange)
    }
  }, [business, businessCoordinates])



  // TEMPORARILY DISABLED - Update cart context when delivery data changes
  // This useEffect was causing continuous API calls to /api/cart-direct
  // TODO: Re-enable after fixing the continuous syncing issue
  /*
  useEffect(() => {
    if (deliveryData && !deliveryData.isLoading && business?.id) {
      const deliveryTimeValue = deliveryData.totalTimeMinutes;

      console.log(`🚚 BUSINESS PAGE: Updating cart context with delivery time: ${deliveryTimeValue} for business ID: ${business.id}`);
      // Only update delivery time - fee should only be set when user chooses delivery method
      setDeliveryTime(business.id.toString(), deliveryTimeValue);
    }
  }, [deliveryData, business?.id, setDeliveryTime]);
  */

  // Update preparation time in cart context when it changes
  useEffect(() => {
    if (prepTime !== null && business?.id) {
      console.log(`Preparation time updated: ${prepTime}, updating cart context for business ID: ${business.id}`);
      setPreparationTime(business.id.toString(), prepTime);
    }
  }, [prepTime, business?.id, setPreparationTime])

  // Fetch and subscribe to preparation time directly
  useEffect(() => {
    if (!id) {
      setPrepTimeLoading(false)
      return
    }

    setPrepTimeLoading(true)
    setPrepTimeError(null)

    // No initial API fetch needed - real-time subscription will provide the data
    console.log('Setting up prep time real-time subscription without initial fetch...');

    // Set up a dedicated channel for preparation time updates
    const prepTimeChannel = supabaseRealtime
      .channel(`prep-time-${id}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'businesses',
          filter: `slug=eq.${id}`
        },
        (payload) => {
          console.log('Received real-time update for preparation time:', payload)
          if (payload.new && payload.new.preparation_time_minutes !== undefined) {
            console.log(`Setting prep time to ${payload.new.preparation_time_minutes}`)
            setPrepTime(payload.new.preparation_time_minutes)
          }
        }
      )
      .subscribe(async (status) => {
        console.log(`Prep time subscription status: ${status}`)

        // When subscription is established, fetch initial prep time data once
        if (status === 'SUBSCRIBED') {
          console.log('Prep time subscription established, fetching initial data...');
          try {
            const { data, error } = await supabaseRealtime
              .from('businesses')
              .select('preparation_time_minutes')
              .eq('slug', id)
              .single()

            if (error) {
              console.error('Error fetching preparation time:', error)
              setPrepTimeError('Failed to fetch preparation time')
            } else if (data) {
              console.log('Initial preparation time:', data.preparation_time_minutes)
              setPrepTime(data.preparation_time_minutes)
            }
          } catch (err) {
            console.error('Exception fetching preparation time:', err)
            setPrepTimeError('An error occurred while fetching preparation time')
          } finally {
            setPrepTimeLoading(false)
          }
        }
      })

    // Clean up subscription on unmount
    return () => {
      console.log(`Cleaning up prep time subscription for business ${id}...`)
      supabaseRealtime.removeChannel(prepTimeChannel)
    }
  }, [id])

  // Generate a discount code based on business name
  const discountCode = useMemo(() => {
    if (!business) return "WELCOME25";
    const name = business.name.toUpperCase().replace(/[^A-Z]/g, "")
    return `${name.substring(0, 4)}25`
  }, [business])



  // Create a flat list of all menu items for search functionality
  const allMenuItems = useMemo(() => {
    if (!business || !business.menuCategories) return [];
    return business.menuCategories.flatMap(category =>
      category.items.map(item => ({
        ...item,
        categoryId: category.id,
        categoryName: category.name
      }))
    )
  }, [business])

  // Filter menu items based on search query
  const filteredMenuItems = useMemo(() => {
    if (!searchQuery.trim() || !allMenuItems.length) return null;

    const query = searchQuery.toLowerCase().trim()
    return allMenuItems.filter(item =>
      item.name.toLowerCase().includes(query) ||
      (item.description && item.description.toLowerCase().includes(query)) ||
      item.categoryName.toLowerCase().includes(query)
    )
  }, [searchQuery, allMenuItems])

  // No initial API fetch needed - real-time subscription will handle all data loading
  useEffect(() => {
    console.log(`Setting up real-time-only data loading for business ${id}...`);
    // Real-time subscriptions will handle all data loading
    // Just set a timeout to stop loading if no data comes through
    const timer = setTimeout(() => {
      if (!business) {
        console.log("No business data received from real-time subscriptions, stopping loading...");
        setLoading(false);
      }
    }, 5000); // 5 second timeout

    return () => clearTimeout(timer);
  }, [id, business])

  // Set up a real-time subscription for business updates
  useEffect(() => {
    if (!id) return;

    console.log(`Setting up real-time subscription for business ${id}...`);

    // Set up real-time subscription for this business
    const businessChannel = supabaseRealtime
      .channel(`business-${id}`)
      .on(
        'postgres_changes',
        {
          event: '*', // Listen for all events (INSERT, UPDATE, DELETE)
          schema: 'public',
          table: 'businesses',
          filter: `slug=eq.${id}`
        },
        async (payload) => {
          console.log('Received real-time update for business:', payload);

          // Immediately update specific fields that we know have changed
          if (payload.new) {
            // Create a shallow copy of the current business
            const updatedBusiness = { ...business };

            // Update delivery fee if it changed
            if (payload.new.delivery_fee !== undefined) {
              console.log(`Updating delivery fee from ${updatedBusiness.deliveryFee} to ${payload.new.delivery_fee}`);
              updatedBusiness.deliveryFee = payload.new.delivery_fee;

              // Also update the formatted version
              if (payload.new.delivery_fee === 0) {
                updatedBusiness.deliveryFeeFormatted = "Free";
              } else {
                updatedBusiness.deliveryFeeFormatted = `£${payload.new.delivery_fee.toFixed(2)}`;
              }
            }

            // Update delivery fee model if it changed
            if (payload.new.delivery_fee_model !== undefined) {
              console.log(`Updating delivery fee model from ${updatedBusiness.delivery_fee_model} to ${payload.new.delivery_fee_model}`);
              updatedBusiness.delivery_fee_model = payload.new.delivery_fee_model;
            }

            // Update delivery fee per km if it changed
            if (payload.new.delivery_fee_per_km !== undefined) {
              console.log(`Updating delivery fee per km from ${updatedBusiness.delivery_fee_per_km} to ${payload.new.delivery_fee_per_km}`);
              updatedBusiness.delivery_fee_per_km = payload.new.delivery_fee_per_km;
            }

            // Update preparation time if it changed
            if (payload.new.preparation_time_minutes !== undefined) {
              console.log(`Updating preparation time from ${updatedBusiness.preparationTimeMinutes} to ${payload.new.preparation_time_minutes}`);
              updatedBusiness.preparationTimeMinutes = payload.new.preparation_time_minutes;

              // Note: We don't need to update prepTime here as we have a dedicated subscription for it
            }

            // Update rating if it changed
            if (payload.new.rating !== undefined) {
              updatedBusiness.rating = payload.new.rating;
            }

            // Update review count if it changed
            if (payload.new.review_count !== undefined) {
              updatedBusiness.reviewCount = payload.new.review_count;
            }

            // Update coordinates if they changed
            if (payload.new.coordinates) {
              setCoordinates(payload.new.coordinates);

              // Also update business coordinates for delivery fee calculation
              try {
                // Parse coordinates if they're in string format
                if (typeof payload.new.coordinates === 'string') {
                  if (payload.new.coordinates.startsWith('[') && payload.new.coordinates.endsWith(']')) {
                    // JSON array format
                    const coords = JSON.parse(payload.new.coordinates);
                    if (Array.isArray(coords) && coords.length === 2) {
                      setBusinessCoordinates([parseFloat(coords[0]), parseFloat(coords[1])]);
                    }
                  } else if (payload.new.coordinates.startsWith('(') && payload.new.coordinates.endsWith(')')) {
                    // Parentheses format like "(lng,lat)"
                    const coordsStr = payload.new.coordinates.substring(1, payload.new.coordinates.length - 1);
                    const parts = coordsStr.split(',').map(part => parseFloat(part.trim()));
                    if (parts.length === 2 && !isNaN(parts[0]) && !isNaN(parts[1])) {
                      setBusinessCoordinates([parts[0], parts[1]]);
                    }
                  } else {
                    // Simple comma-separated format
                    const parts = payload.new.coordinates.split(',').map(part => parseFloat(part.trim()));
                    if (parts.length === 2 && !isNaN(parts[0]) && !isNaN(parts[1])) {
                      setBusinessCoordinates([parts[0], parts[1]]);
                    }
                  }
                } else if (Array.isArray(payload.new.coordinates) && payload.new.coordinates.length === 2) {
                  // Array format
                  setBusinessCoordinates([
                    parseFloat(payload.new.coordinates[0]),
                    parseFloat(payload.new.coordinates[1])
                  ]);
                }
              } catch (parseError) {
                console.error('Error parsing business coordinates:', parseError);
              }
            }

            // Update delivery radius if it changed
            if (payload.new.delivery_radius) {
              setDeliveryRadius(payload.new.delivery_radius);
            }

            // Update the business state with our changes
            setBusiness(updatedBusiness);
          }

          // Real-time update complete - no additional API call needed
        }
      )
      .subscribe(async (status) => {
        console.log(`Supabase channel status for business: ${status}`);

        // When subscription is established, fetch initial data once
        if (status === 'SUBSCRIBED') {
          console.log('Business subscription established, fetching initial data...');
          try {
            const freshData = await getBusinessById(id);
            if (freshData) {
              console.log("Initial business data from real-time subscription:", freshData);
              setBusiness(freshData);
              setLoading(false);

              // Menu items will be automatically computed via useMemo when business data updates
              console.log("Business data loaded, menu items will be computed automatically");

              // Set coordinates from database if available
              if (freshData.coordinates) {
                // Parse coordinates if they're in string format
                if (typeof freshData.coordinates === 'string') {
                  try {
                    // Try to parse PostgreSQL POINT format: "POINT(longitude latitude)"
                    const pointMatch = freshData.coordinates.match(/POINT\s*\(\s*([^\s]+)\s+([^\s]+)\s*\)/i);
                    if (pointMatch && pointMatch.length === 3) {
                      const lng = parseFloat(pointMatch[1]);
                      const lat = parseFloat(pointMatch[2]);
                      if (!isNaN(lng) && !isNaN(lat)) {
                        setCoordinates([lng, lat]);
                        setBusinessCoordinates([lng, lat]);
                        console.log(`Parsed POINT format coordinates: [${lng}, ${lat}]`);
                      }
                    } else {
                      // Try to parse parentheses format: "(longitude,latitude)"
                      const parenMatch = freshData.coordinates.match(/\(\s*([-\d.]+)\s*,\s*([-\d.]+)\s*\)/);
                      if (parenMatch && parenMatch.length === 3) {
                        const lng = parseFloat(parenMatch[1]);
                        const lat = parseFloat(parenMatch[2]);
                        if (!isNaN(lng) && !isNaN(lat)) {
                          setCoordinates([lng, lat]);
                          setBusinessCoordinates([lng, lat]);
                          console.log(`Parsed parentheses format coordinates: [${lng}, ${lat}]`);
                        }
                      }
                    }
                  } catch (error) {
                    console.error("Error parsing coordinates string:", error);
                  }
                } else {
                  setCoordinates(freshData.coordinates);
                  setBusinessCoordinates(freshData.coordinates);
                }
              }

              // Set delivery radius from database if available
              if (freshData.delivery_radius) {
                setDeliveryRadius(freshData.delivery_radius)
              }
            }
          } catch (error) {
            console.error("Error fetching initial business data:", error);
            setLoading(false);
          }
        }
      });

    // Clean up subscription on unmount
    return () => {
      console.log(`Cleaning up real-time subscription for business ${id}...`);
      supabaseRealtime.removeChannel(businessChannel);
    };
  }, [id]); // Removed 'business' dependency to prevent infinite loop

  // Set up a real-time subscription for product updates
  useEffect(() => {
    if (!id || !business || !business.id) return;

    console.log(`Setting up real-time subscription for products of business ${id}...`);

    // Set up real-time subscription for products of this business
    const productsChannel = supabaseRealtime
      .channel(`products-${id}`)
      .on(
        'postgres_changes',
        {
          event: '*', // Listen for all events (INSERT, UPDATE, DELETE)
          schema: 'public',
          table: 'products',
          filter: `business_id=eq.${business.id}`
        },
        async (payload) => {
          console.log('Received real-time update for product:', payload);

          // Product updates should trigger a re-fetch of business data to get updated menu
          // But we'll handle this through the business real-time subscription instead
          console.log('Product update received - business subscription will handle menu updates');
        }
      )
      .subscribe((status) => {
        console.log(`Supabase channel status for products: ${status}`);
      });

    // Clean up subscription on unmount
    return () => {
      console.log(`Cleaning up real-time subscription for products of business ${id}...`);
      supabaseRealtime.removeChannel(productsChannel);
    };
  }, [id, business?.id]);





  // Get items and calculate subtotal for this specific business
  useEffect(() => {
    if (!business) return;

    const itemsByBusiness = getItemsByBusiness()
    const items = itemsByBusiness[business.id] || []
    setBusinessItems(items)

    const subtotal = items.reduce((total, item) => {
      return total + item.price * item.quantity
    }, 0)
    setBusinessSubtotal(subtotal)
  }, [business, cart, getItemsByBusiness])

  // Set initial active category when component mounts
  useEffect(() => {
    if (!business || !business.menuCategories) return;

    if (business.menuCategories.length > 0) {
      setActiveCategory(business.menuCategories[0].id)
    }
  }, [business])

  // Get the delivery method from cart context
  const deliveryMethod = getDeliveryMethod(id)



  // Handle delivery method toggle - simplified since centralized service handles calculations
  const handleDeliveryMethodChange = useCallback(async (method: 'delivery' | 'pickup') => {
    if (method !== deliveryMethod) {
      setCartDeliveryMethod(id, method);

      // If switching to delivery and we have a calculated delivery fee, save it to cart context
      if (method === 'delivery' && deliveryData?.fee && typeof deliveryData.fee === 'number' && business?.id) {
        console.log(`💰 Saving delivery fee £${deliveryData.fee.toFixed(2)} to cart after switching to delivery for business ${business.id}`)
        setCartDeliveryFee(business.id.toString(), deliveryData.fee)
      }

      // If switching to pickup, set delivery fee to 0
      if (method === 'pickup' && business?.id) {
        console.log(`💰 Setting delivery fee to £0.00 after switching to pickup for business ${business.id}`)
        setCartDeliveryFee(business.id.toString(), 0)
      }
    }
  }, [id, deliveryMethod, deliveryData, business?.id, setCartDeliveryFee]);

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // Handle category change from the category scroller
  const handleCategoryChange = (categoryId: string) => {
    setActiveCategory(categoryId);

    // Scroll to the selected category
    const element = document.getElementById(`category-${categoryId}`);
    if (element) {
      isScrolling.current = true;
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });

      // Reset the scrolling flag after animation completes
      setTimeout(() => {
        isScrolling.current = false;
      }, 1000);
    }
  };

  // Update delivery method when delivery availability changes
  useEffect(() => {
    // If delivery is not available, switch to pickup
    if (realtimeDeliveryAvailable === false || (realtimeDeliveryAvailable === null && business?.deliveryAvailable === false)) {
      setCartDeliveryMethod(id, 'pickup')
    }
  }, [realtimeDeliveryAvailable, business?.deliveryAvailable, id])

  // Ensure delivery fee is saved to cart when delivery method is set to delivery
  useEffect(() => {
    if (deliveryMethod === 'delivery' && deliveryData?.fee && typeof deliveryData.fee === 'number' && business?.id) {
      const currentCartFee = getDeliveryFee(business.id.toString())
      // Only update if the cart fee is different from the calculated fee
      if (Math.abs(currentCartFee - deliveryData.fee) > 0.01) {
        console.log(`💰 Syncing delivery fee to cart: £${deliveryData.fee.toFixed(2)} (was £${currentCartFee.toFixed(2)})`)
        setCartDeliveryFee(business.id.toString(), deliveryData.fee)
      }
    }
  }, [deliveryMethod, deliveryData?.fee, business?.id, getDeliveryFee, setCartDeliveryFee])

  // Scroll spy functionality to update active category based on scroll position
  useEffect(() => {
    if (!business || !business.menuCategories) return;

    const handleScroll = () => {
      // Don't run if we're programmatically scrolling
      if (isScrolling.current) return

      const categoryElements = business.menuCategories.map(category =>
        document.getElementById(`category-${category.id}`)
      )

      // Find the category that is currently most visible in the viewport
      let mostVisibleCategory = null
      let maxVisibleHeight = 0

      categoryElements.forEach((element, index) => {
        if (!element) return

        const rect = element.getBoundingClientRect()
        const visibleTop = Math.max(rect.top, 180) // 180px is the navbar + sticky header height
        const visibleBottom = Math.min(rect.bottom, window.innerHeight)
        const visibleHeight = Math.max(0, visibleBottom - visibleTop)

        if (visibleHeight > maxVisibleHeight) {
          maxVisibleHeight = visibleHeight
          mostVisibleCategory = business.menuCategories[index].id
        }
      })

      if (mostVisibleCategory && mostVisibleCategory !== activeCategory) {
        setActiveCategory(mostVisibleCategory)
      }
    }

    window.addEventListener('scroll', handleScroll)
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [business, activeCategory])

  // Show loading state while fetching data
  if (loading || prepTimeLoading) {
    return (
      <div className="container-fluid py-12 text-center">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-100 rounded w-1/3 mx-auto mb-4"></div>
          <div className="h-64 bg-gray-100 rounded mb-6 border border-gray-100"></div>
          <div className="h-4 bg-gray-100 rounded w-1/2 mx-auto mb-2"></div>
          <div className="h-4 bg-gray-100 rounded w-1/3 mx-auto"></div>
        </div>
      </div>
    )
  }

  // Show error state if any errors occurred
  if (prepTimeError) {
    return (
      <div className="container-fluid py-12 text-center">
        <div className="bg-red-50 p-4 rounded-lg border border-red-200 mb-4">
          <h2 className="text-red-700 text-lg font-semibold mb-2">Error Loading Data</h2>
          <p className="text-red-600">{prepTimeError}</p>
        </div>
        <Button onClick={() => window.location.reload()} className="mt-4">
          Retry
        </Button>
      </div>
    )
  }

  if (!business) {
    notFound()
  }

  // Function to scroll to a category section
  const scrollToCategory = (categoryId: string) => {
    const element = document.getElementById(`category-${categoryId}`)
    if (element) {
      // Set flag to prevent scroll spy from running during programmatic scroll
      isScrolling.current = true

      // Offset for the navbar + sticky header (search + categories)
      const offset = 180
      const elementPosition = element.getBoundingClientRect().top
      const offsetPosition = elementPosition + window.pageYOffset - offset

      window.scrollTo({
        top: offsetPosition,
        behavior: "smooth"
      })

      // Reset the flag after animation completes (roughly 500ms)
      setTimeout(() => {
        isScrolling.current = false
      }, 500)
    }
  }

  return (
    <div>
      {/* Business Header Section - Using the same container structure as other sections */}
      <div className="container-fluid mb-4 mt-2">
        <div className="flex flex-col md:flex-row gap-6">
          {/* Left Column - Square Restaurant Image */}
          <div className="w-full md:w-1/3">
            <div className="relative rounded-lg overflow-hidden shadow-md aspect-square">
              <FallbackImage
                src={business.coverImage}
                alt={business.name}
                fallbackSrc="/placeholder.svg"
                className="w-full h-full object-contain"
              />
              <div className="absolute inset-0 bg-black/30" />
            </div>
          </div>

          {/* Right Column - Business Details */}
          <div className="w-full md:w-2/3 flex flex-col justify-between">
            {/* Business Name, Cuisines, and Rating */}
            <div>
              <div className="flex flex-wrap items-center justify-between mb-2">
                <div>
                  <h1 className="text-2xl font-bold">{business.name}</h1>
                  {business.businessTypeName && (
                    <div className="mt-1">
                      <Badge variant="outline" className="bg-emerald-50 text-emerald-700 border-emerald-200">
                        {business.businessTypeName}
                      </Badge>
                    </div>
                  )}
                </div>

                {/* More Info and Map Buttons */}
                <div className="flex gap-2 mt-2 md:mt-0">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1 text-sm"
                    onClick={() => setShowInfoDialog(true)}
                  >
                    <Info size={14} />
                    More Info
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1 text-sm"
                    onClick={() => setShowOSMMapDialog(true)}
                  >
                    <MapIcon size={14} />
                    View Map
                  </Button>
                </div>

                {/* Delivery Method Selector */}
                <div className="flex gap-2 mt-2 md:mt-0">
                  <div className="flex rounded-md border overflow-hidden">
                    <Button
                      variant={deliveryMethod === 'delivery' ? "default" : "ghost"}
                      size="sm"
                      className={`rounded-none ${deliveryMethod === 'delivery' ? 'bg-emerald-600 hover:bg-emerald-700' : 'hover:bg-gray-100'}`}
                      onClick={() => handleDeliveryMethodChange('delivery')}
                      disabled={!business.deliveryAvailable}
                    >
                      <Bike size={14} className="mr-1" />
                      Delivery
                    </Button>
                    <Button
                      variant={deliveryMethod === 'pickup' ? "default" : "ghost"}
                      size="sm"
                      className={`rounded-none ${deliveryMethod === 'pickup' ? 'bg-emerald-600 hover:bg-emerald-700' : 'hover:bg-gray-100'}`}
                      onClick={() => handleDeliveryMethodChange('pickup')}
                    >
                      <ShoppingBag size={14} className="mr-1" />
                      Pickup
                    </Button>
                  </div>
                </div>
              </div>

              {/* Business Attributes (Cuisines, Store Types, etc.) */}
              {(() => {
                // Determine which attributes to display based on business type
                let attributes: string[] = [];
                let attributeLabel = "Categories";

                if (business.cuisines && business.cuisines.length > 0) {
                  attributes = business.cuisines;
                  attributeLabel = "Cuisines";
                } else if (business.storeTypes && business.storeTypes.length > 0) {
                  attributes = business.storeTypes;
                  attributeLabel = "Store Types";
                } else if (business.pharmacyTypes && business.pharmacyTypes.length > 0) {
                  attributes = business.pharmacyTypes;
                  attributeLabel = "Pharmacy Types";
                } else if (business.cafeTypes && business.cafeTypes.length > 0) {
                  attributes = business.cafeTypes;
                  attributeLabel = "Cafe Types";
                } else if (business.errandTypes && business.errandTypes.length > 0) {
                  attributes = business.errandTypes;
                  attributeLabel = "Errand Types";
                } else if (business.genericTypes && business.genericTypes.length > 0) {
                  attributes = business.genericTypes;
                }

                return attributes.length > 0 ? (
                  <div className="flex flex-wrap gap-2 mt-2">
                    {attributes.map((attribute: string) => (
                      <span key={attribute} className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">
                        {attribute}
                      </span>
                    ))}
                  </div>
                ) : null;
              })()}

              {/* Rating and Location */}
              <div className="flex flex-wrap items-center gap-4 mt-3 text-sm">
                {business.rating && (
                  <div className="flex items-center">
                    <Star className="h-4 w-4 text-yellow-400 fill-yellow-400 mr-1" />
                    <span className="font-medium">{business.rating.toFixed(1)}</span>
                    {business.reviewCount && (
                      <span className="text-gray-500 ml-1">({business.reviewCount})</span>
                    )}
                  </div>
                )}

                <div className="flex items-center">
                  <MapPin className="h-4 w-4 text-rose-400 mr-1" />
                  <span>{business.location}, Jersey</span>
                </div>

                {business.phone && (
                  <div className="flex items-center">
                    <ClickablePhone phoneNumber={business.phone} />
                  </div>
                )}
              </div>

              {/* Description (truncated) */}
              {business.description && (
                <div className="mt-3">
                  <p className="text-sm text-gray-600 line-clamp-2">{business.description}</p>
                </div>
              )}
            </div>

            {/* Delivery/Pickup Info Card */}
            <div className="bg-white rounded-lg shadow-sm p-4 mt-4 border border-gray-100">
              {/* Header with title */}
              <div className="mb-4">
                <h3 className="font-medium text-center text-lg">Choose your delivery option</h3>

                {/* Pickup Only Badge - Shown if delivery is not available */}
                {!(realtimeDeliveryAvailable !== null ? realtimeDeliveryAvailable : business.deliveryAvailable) && (
                  <div className="flex justify-center mt-2">
                    <Badge variant="outline" className="bg-orange-50 text-orange-600 border-orange-200">
                      Pickup only
                    </Badge>
                  </div>
                )}
              </div>

              {/* Clickable Cards - Pickup on left, Delivery on right */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {/* Pickup Card */}
                <div
                  className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                    deliveryMethod === 'pickup'
                      ? 'bg-orange-50 border-orange-300 shadow-md'
                      : 'bg-white border-gray-200 hover:border-orange-200 hover:bg-orange-25'
                  }`}
                  onClick={() => handleDeliveryMethodChange('pickup')}
                >
                  <div className="flex items-center mb-3">
                    <div className={`p-2 rounded-full mr-3 ${
                      deliveryMethod === 'pickup' ? 'bg-orange-100' : 'bg-gray-100'
                    }`}>
                      <MapPin className={`h-5 w-5 ${
                        deliveryMethod === 'pickup' ? 'text-orange-600' : 'text-gray-500'
                      }`} />
                    </div>
                    <span className={`font-semibold text-lg ${
                      deliveryMethod === 'pickup' ? 'text-orange-800' : 'text-gray-700'
                    }`}>Pickup</span>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className={`text-sm ${
                        deliveryMethod === 'pickup' ? 'text-orange-700' : 'text-gray-600'
                      }`}>Ready in</span>
                      <span className={`text-sm font-medium ${
                        deliveryMethod === 'pickup' ? 'text-orange-800' : 'text-gray-700'
                      }`}>
                        {realtimePrepTime !== null
                          ? `${realtimePrepTime} min`
                          : prepTime !== null
                            ? `${prepTime} min`
                            : business.preparationTimeMinutes
                              ? `${business.preparationTimeMinutes} min`
                              : "15 min"}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className={`text-sm ${
                        deliveryMethod === 'pickup' ? 'text-orange-700' : 'text-gray-600'
                      }`}>Fee</span>
                      <span className={`text-sm font-medium ${
                        deliveryMethod === 'pickup' ? 'text-orange-800' : 'text-gray-700'
                      }`}>Free</span>
                    </div>
                  </div>
                </div>

                {/* Delivery Card - Only shown if available */}
                {(realtimeDeliveryAvailable !== null ? realtimeDeliveryAvailable : business.deliveryAvailable) && (
                  <div
                    className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                      deliveryMethod === 'delivery'
                        ? 'bg-blue-50 border-blue-300 shadow-md'
                        : 'bg-white border-gray-200 hover:border-blue-200 hover:bg-blue-25'
                    }`}
                    onClick={() => handleDeliveryMethodChange('delivery')}
                  >
                    <div className="flex items-center mb-3">
                      <div className={`p-2 rounded-full mr-3 ${
                        deliveryMethod === 'delivery' ? 'bg-blue-100' : 'bg-gray-100'
                      }`}>
                        <Truck className={`h-5 w-5 ${
                          deliveryMethod === 'delivery' ? 'text-blue-600' : 'text-gray-500'
                        }`} />
                      </div>
                      <span className={`font-semibold text-lg ${
                        deliveryMethod === 'delivery' ? 'text-blue-800' : 'text-gray-700'
                      }`}>Delivery</span>
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className={`text-sm ${
                          deliveryMethod === 'delivery' ? 'text-blue-700' : 'text-gray-600'
                        }`}>Fee</span>
                        <span className={`text-sm font-medium ${
                          deliveryMethod === 'delivery' ? 'text-blue-800' : 'text-gray-700'
                        }`}>
                          {deliveryData.fee}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className={`text-sm ${
                          deliveryMethod === 'delivery' ? 'text-blue-700' : 'text-gray-600'
                        }`}>Time</span>
                        <span className={`text-sm font-medium ${
                          deliveryMethod === 'delivery' ? 'text-blue-800' : 'text-gray-700'
                        }`}>
                          {deliveryData.timeRange}
                        </span>
                      </div>
                      {deliveryData.distance && (
                        <div className="flex justify-between items-center">
                          <span className={`text-sm ${
                            deliveryMethod === 'delivery' ? 'text-blue-700' : 'text-gray-600'
                          }`}>Distance</span>
                          <span className={`text-sm font-medium ${
                            deliveryMethod === 'delivery' ? 'text-blue-800' : 'text-gray-700'
                          }`}>
                            {deliveryData.distance}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Debug info for development */}
              {process.env.NODE_ENV === 'development' && (
                <div className="text-xs text-gray-500 mt-4 p-2 bg-gray-50 rounded">
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <strong>Business:</strong> {business.delivery_fee_model || 'fixed'} model,
                      Base: £{business.deliveryFee?.toFixed(2) || '0.00'},
                      Per km: £{business.delivery_fee_per_km?.toFixed(2) || '0.00'}
                    </div>
                    <div>
                      <strong>Centralized:</strong> Fee: {deliveryData.fee},
                      Time: {deliveryData.time}min,
                      Distance: {deliveryData.distance || 'N/A'}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Sticky Search and Categories Container */}
      <div className="sticky top-[64px] z-30 bg-white shadow-sm border-b border-gray-100">
        <div className="container-fluid py-2">
          {/* Search Input */}
          <div className="relative mb-2">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-4 w-4 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search menu items..."
              value={searchQuery}
              onChange={handleSearchChange}
              className="w-full pl-10 pr-10 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-transparent"
            />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery("")}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
              >
                ×
              </button>
            )}
          </div>

          {/* Horizontal Category Scroller - Hide when searching */}
          {!searchQuery && (
            <div>
              <CategoryScroller
                categories={business.menuCategories}
                activeCategory={activeCategory}
                onCategoryChange={handleCategoryChange}
              />
            </div>
          )}
        </div>
      </div>

      {/* Main Content Area with Menu and Order - Fixed Layout */}
      <div className="relative">
        {/* Mobile Order Section - Only visible on small screens */}
        <div className="lg:hidden container-fluid py-3 mb-4">
          <div className="bg-white rounded-lg shadow-md p-4 border border-gray-100">
            <div className="flex items-center mb-2">
              <ShoppingBag className="mr-2 text-emerald-600" />
              <h3 className="text-lg font-semibold">Your Order with {business.name}</h3>
            </div>

            {businessItems.length === 0 ? (
              <div className="border-t border-b py-3 my-3">
                <p className="text-center text-gray-500 text-sm">Add items from this business to your order</p>
              </div>
            ) : (
              <div className="border-t pt-3 my-3">
                <div className="max-h-40 overflow-y-auto mb-3">
                  {businessItems.map((item) => (
                    <div key={`${item.id}-${item.options?.join("-")}`} className="flex justify-between mb-2">
                      <div>
                        <p className="font-medium text-sm">
                          {item.quantity}x {item.name}
                        </p>
                      </div>
                      <p className="font-medium text-sm">£{(item.price * item.quantity).toFixed(2)}</p>
                    </div>
                  ))}
                </div>
                <div className="flex justify-between text-sm mt-2">
                  <span>Subtotal</span>
                  <span>£{businessSubtotal.toFixed(2)}</span>
                </div>
                {deliveryMethod === 'delivery' ? (
                  <div className="flex justify-between text-sm mt-1">
                    <span>Delivery Fee</span>
                    <span>
                      {deliveryData.fee}
                    </span>
                  </div>
                ) : (
                  <div className="flex justify-between text-sm mt-1">
                    <span>Pickup Fee</span>
                    <span>£0.00</span>
                  </div>
                )}
                <div className="flex justify-between text-sm mt-1">
                  <span>Service Fee</span>
                  <span>£0.50</span>
                </div>
                <div className="flex justify-between font-semibold pt-2 border-t text-sm">
                  <span>{business.name} Total</span>
                  <span>£{(businessSubtotal + (deliveryMethod === 'delivery' ? deliveryData.feeNumeric : 0) + 0.5).toFixed(2)}</span>
                </div>
                <CheckoutLink
                  className="block mt-3"
                  buttonClassName={`w-full text-sm py-1 ${
                    deliveryMethod === 'delivery'
                      ? 'bg-blue-600 hover:bg-blue-700'
                      : 'bg-orange-500 hover:bg-orange-600'
                  }`}
                >
                  Go to checkout
                </CheckoutLink>
                {deliveryMethod === 'delivery' && (realtimeDeliveryAvailable !== null ? realtimeDeliveryAvailable : business.deliveryAvailable) && (
                  <div className="mt-2 text-xs text-gray-500 flex items-start">
                    <Info size={12} className="mr-1 flex-shrink-0 mt-0.5" />
                    <p>Minimum order of £{business.minimum_order_amount?.toFixed(2) || '15.00'} required for delivery from {business.name}</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Two-column layout with sticky right column - Using the same container as the search/categories */}
        <div className="container-fluid py-3">
          <div className="flex flex-col lg:flex-row lg:items-start lg:gap-6">
            {/* Left Column - Menu Content */}
            <div className="w-full lg:w-[calc(100%-340px)]">
              {/* Discount Card with Copy Button */}
              <Card className="mb-4 bg-emerald-50 border-emerald-200">
                <CardContent className="p-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-start gap-2">
                      <div className="bg-emerald-100 p-1.5 rounded-full">
                        <UtensilsCrossed className="h-4 w-4 text-emerald-600" />
                      </div>
                      <div>
                        <h3 className="font-medium text-emerald-800 text-sm">First-time customer?</h3>
                        <p className="text-xs text-emerald-700">
                          Use code <span className="font-bold">{discountCode}</span> for 25% off your first order
                        </p>
                      </div>
                    </div>
                    <CopyButton
                      value={discountCode}
                      tooltipText="Copy discount code"
                      successText="Code copied!"
                      variant="outline"
                      size="sm"
                      className="border-emerald-200 hover:bg-emerald-100"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Search Results */}
              {filteredMenuItems && filteredMenuItems.length > 0 ? (
                <div>
                  <h2 className="text-lg font-bold mb-3">Search Results</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-6">
                    {filteredMenuItems.map((item) => (
                      <ProductItem
                        key={item.id}
                        product={item}
                        businessId={business.id} // This is now the numeric ID
                        businessSlug={business.slug} // Pass the slug for routing
                        businessName={business.name}
                        businessType={business.businessType || 'restaurant'}
                        categoryId={item.categoryId}
                        layout="compact"
                      />
                    ))}
                  </div>
                </div>
              ) : filteredMenuItems && filteredMenuItems.length === 0 ? (
                <div className="p-4 text-center bg-white rounded-lg border border-gray-100 shadow-sm">
                  <p className="text-gray-600">No menu items found matching "{searchQuery}"</p>
                </div>
              ) : (
                /* Menu Categories - Show when not searching */
                <div>
                  {business.menuCategories.map((category) => (
                    <div
                      key={category.id}
                      id={`category-${category.id}`}
                      className="scroll-mt-[180px] mb-6"
                    >
                      <h2 className="text-lg font-bold mb-3">{category.name}</h2>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {category.items.map((item) => (
                          <ProductItem
                            key={item.id}
                            product={item}
                            businessId={business.id} // This is now the numeric ID
                            businessSlug={business.slug} // Pass the slug for routing
                            businessName={business.name}
                            businessType={business.businessType || 'restaurant'}
                            categoryId={category.id}
                            layout="compact"
                          />
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Right Column - Sticky Order Section that stops at the bottom of the category section */}
            <div className="hidden lg:block sticky top-[180px] w-[320px] z-20 self-start ml-auto mb-8">
              <div className="bg-white rounded-lg shadow-md p-6 border border-gray-100 max-h-[calc(100vh-200px)] overflow-y-auto">
                <div className="flex items-center mb-4">
                  <ShoppingBag className="mr-2 text-emerald-600" />
                  <h3 className="text-lg font-semibold">Your Order with {business.name}</h3>
                </div>

                {businessItems.length === 0 ? (
                  <div className="border-t border-b py-4 my-4">
                    <p className="text-center text-gray-500">Add items from this business to your order</p>
                    <p className="text-center text-sm text-emerald-600 mt-2">
                      You can order from multiple businesses at once!
                    </p>
                  </div>
                ) : (
                  <div className="border-t pt-4 my-4">
                    <div className="max-h-60 overflow-y-auto mb-4">
                      {businessItems.map((item) => (
                        <div key={`${item.id}-${item.options?.join("-")}`} className="flex justify-between mb-3">
                          <div>
                            <p className="font-medium">
                              {item.quantity}x {item.name}
                            </p>
                            {item.options && item.options.length > 0 && (
                              <p className="text-sm text-gray-500">{item.options.join(", ")}</p>
                            )}
                          </div>
                          <p className="font-medium">£{(item.price * item.quantity).toFixed(2)}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="space-y-4">
                  <div className="flex justify-between text-sm">
                    <span>Subtotal</span>
                    <span>£{businessSubtotal.toFixed(2)}</span>
                  </div>
                  {deliveryMethod === 'delivery' ? (
                    <div className="flex justify-between text-sm">
                      <span>Delivery Fee</span>
                      <span>
                        {deliveryData.fee}
                      </span>
                    </div>
                  ) : (
                    <div className="flex justify-between text-sm">
                      <span>Pickup Fee</span>
                      <span>£0.00</span>
                    </div>
                  )}
                  <div className="flex justify-between text-sm">
                    <span>Service Fee</span>
                    <span>£0.50</span>
                  </div>
                  <div className="flex justify-between font-semibold pt-4 border-t">
                    <span>{business.name} Total</span>
                    <span>£{(businessSubtotal + (deliveryMethod === 'delivery' ? deliveryData.feeNumeric : 0) + 0.5).toFixed(2)}</span>
                  </div>
                </div>

                {businessItems.length > 0 ? (
                  <CheckoutLink
                    buttonClassName={`w-full mt-6 ${
                      deliveryMethod === 'delivery'
                        ? 'bg-blue-600 hover:bg-blue-700'
                        : 'bg-orange-500 hover:bg-orange-600'
                    }`}
                  >
                    Go to checkout
                  </CheckoutLink>
                ) : (
                  <Button className={`w-full mt-6 ${
                    deliveryMethod === 'delivery'
                      ? 'bg-blue-600 hover:bg-blue-700'
                      : 'bg-orange-500 hover:bg-orange-600'
                  }`} disabled>
                    Go to checkout
                  </Button>
                )}

                {deliveryMethod === 'delivery' && (realtimeDeliveryAvailable !== null ? realtimeDeliveryAvailable : business.deliveryAvailable) && (
                  <div className="mt-4 text-xs text-gray-500 flex items-start">
                    <Info size={14} className="mr-1 flex-shrink-0 mt-0.5" />
                    <p>Minimum order of £{business.minimum_order_amount?.toFixed(2) || '15.00'} required for delivery from {business.name}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Business Info Dialog */}
      <BusinessInfoDialog
        business={business}
        open={showInfoDialog}
        onOpenChange={setShowInfoDialog}
      />

      {/* Business Map Dialog */}
      <Dialog open={showOSMMapDialog} onOpenChange={setShowOSMMapDialog}>
        <DialogContent className="sm:max-w-3xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <MapIcon size={18} className="text-emerald-600" />
              {business.name} Delivery Map
            </DialogTitle>
          </DialogHeader>
          <div className="py-2">
            <OSMRestaurantMap
              restaurantId={business.id}
              name={business.name}
              location={business.location}
              coordinates={coordinates}
              deliveryTime={business.deliveryTime || 30}
              deliveryRadius={deliveryRadius}
            />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
