import { createClient } from "@supabase/supabase-js";
import { NextRequest } from "next/server";

// Get environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

// Create a Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

/**
 * Verify user access for API routes
 * @param request - The Next.js request object
 * @param options - Options for access verification
 * @returns Object with authorization status and user if authorized
 */
export async function verifyUserAccess(
  request: NextRequest,
  options: {
    requiredRole?: string;
    allowAnonymous?: boolean;
  } = {}
) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');

    if (!authHeader && !options.allowAnonymous) {
      // In development mode, allow requests without authentication for easier testing
      if (process.env.NODE_ENV === 'development') {
        console.log('Development mode: Skipping auth check in verifyUserAccess')
        return {
          authorized: true,
          user: {
            id: 'dev-user-id',
            email: '<EMAIL>',
            // Add other required user properties for development
            aud: 'authenticated',
            role: 'authenticated',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        };
      }

      return {
        authorized: false,
        error: 'Authorization header missing',
        status: 401
      };
    }

    // If anonymous access is allowed and no auth header, return authorized with no user
    if (!authHeader && options.allowAnonymous) {
      return {
        authorized: true,
        user: null
      };
    }

    // Extract the token
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return {
        authorized: false,
        error: 'Invalid authorization header format',
        status: 401
      };
    }

    // Create an admin client to verify the token
    const adminClient = createClient(
      supabaseUrl,
      process.env.SUPABASE_SERVICE_ROLE_KEY || '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Verify the token
    const { data: { user }, error } = await adminClient.auth.getUser(token);

    if (error || !user) {
      // In development mode, allow requests with invalid tokens for easier testing
      if (process.env.NODE_ENV === 'development') {
        console.log('Development mode: Allowing invalid token in verifyUserAccess')
        return {
          authorized: true,
          user: {
            id: 'dev-user-id',
            email: '<EMAIL>',
            // Add other required user properties for development
            aud: 'authenticated',
            role: 'authenticated',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        };
      }

      return {
        authorized: false,
        error: error?.message || 'Invalid token',
        status: 401
      };
    }

    // If a specific role is required, check user roles
    if (options.requiredRole) {
      const { data: roles, error: rolesError } = await adminClient
        .from('user_roles')
        .select('role')
        .eq('user_id', user.id);

      if (rolesError) {
        return {
          authorized: false,
          error: 'Error checking user roles',
          status: 500
        };
      }

      const hasRequiredRole = roles?.some(r => r.role === options.requiredRole);

      if (!hasRequiredRole) {
        return {
          authorized: false,
          error: `Requires ${options.requiredRole} role`,
          status: 403
        };
      }
    }

    // User is authorized
    return {
      authorized: true,
      user
    };
  } catch (error: any) {
    console.error('Error verifying user access:', error);
    return {
      authorized: false,
      error: 'Error verifying access',
      status: 500
    };
  }
}

/**
 * Ensures the Supabase session is properly stored in localStorage
 * This helps with session persistence issues
 */
export const persistSession = (session: any) => {
  if (!session || typeof window === 'undefined') return;

  try {
    console.log("Persisting session to localStorage");

    // Don't try to manually store the session anymore - let Supabase handle it
    // This avoids conflicts with Supabase's internal session management

    // Instead, just store a flag indicating we have a session
    localStorage.setItem('loop_jersey_session_active', 'true');
    localStorage.setItem('loop_jersey_session_timestamp', Date.now().toString());

    // Store user info separately for quick access
    if (session.user) {
      localStorage.setItem('loop_jersey_user', JSON.stringify({
        id: session.user.id,
        email: session.user.email,
        last_sign_in: new Date().toISOString()
      }));
    }

    console.log("Session flag stored in localStorage");
  } catch (e) {
    console.error("Failed to persist session flag:", e);
  }
};

/**
 * Attempts to recover a session from Supabase
 */
export const recoverSession = async () => {
  if (typeof window === 'undefined') return null;

  try {
    console.log("Attempting to recover session");

    // Let Supabase handle session recovery
    const { data, error } = await supabase.auth.getSession();

    if (error) {
      console.error("Error getting session from Supabase:", error);
      return null;
    }

    if (data?.session) {
      console.log("Found active session from Supabase");

      // Update our session flag
      localStorage.setItem('loop_jersey_session_active', 'true');
      localStorage.setItem('loop_jersey_session_timestamp', Date.now().toString());

      return data.session;
    }

    // Check if we have a session flag but no actual session
    const sessionActive = localStorage.getItem('loop_jersey_session_active') === 'true';
    const sessionTimestamp = localStorage.getItem('loop_jersey_session_timestamp');

    if (sessionActive && sessionTimestamp) {
      const timestamp = parseInt(sessionTimestamp);
      const now = Date.now();
      const sessionAge = now - timestamp;

      // If the session flag is less than 24 hours old, try to refresh the session
      if (sessionAge < 24 * 60 * 60 * 1000) {
        console.log("Session flag found but no active session, attempting to refresh");

        try {
          const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();

          if (refreshError) {
            console.error("Error refreshing session:", refreshError);
            // Clear the session flag since it's invalid
            localStorage.removeItem('loop_jersey_session_active');
            localStorage.removeItem('loop_jersey_session_timestamp');
            return null;
          }

          if (refreshData?.session) {
            console.log("Successfully refreshed session");
            return refreshData.session;
          }
        } catch (refreshErr) {
          console.error("Exception refreshing session:", refreshErr);
        }
      } else {
        console.log("Session flag is too old, clearing");
        localStorage.removeItem('loop_jersey_session_active');
        localStorage.removeItem('loop_jersey_session_timestamp');
      }
    }

    console.log("No valid session found");
    return null;
  } catch (e) {
    console.error("Error recovering session:", e);
    return null;
  }
};
