const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function verifyTables() {
  try {
    console.log('🔍 Verifying push notifications tables...');
    
    // Test push_subscriptions table
    const { data: pushData, error: pushError } = await supabase
      .from('push_subscriptions')
      .select('*')
      .limit(1);
    
    if (pushError) {
      console.log('❌ push_subscriptions table error:', pushError.message);
    } else {
      console.log('✅ push_subscriptions table exists and accessible');
    }
    
    // Test notification_log table
    const { data: logData, error: logError } = await supabase
      .from('notification_log')
      .select('*')
      .limit(1);
    
    if (logError) {
      console.log('❌ notification_log table error:', logError.message);
    } else {
      console.log('✅ notification_log table exists and accessible');
    }
    
    // Test inserting a sample push subscription
    console.log('\n🧪 Testing table functionality...');
    
    const testSubscription = {
      user_id: '00000000-0000-0000-0000-000000000000', // Dummy UUID
      subscription_data: {
        endpoint: 'https://test.example.com',
        keys: {
          p256dh: 'test-key',
          auth: 'test-auth'
        }
      },
      device_type: 'desktop',
      browser_name: 'test'
    };
    
    const { data: insertData, error: insertError } = await supabase
      .from('push_subscriptions')
      .insert(testSubscription)
      .select();
    
    if (insertError) {
      console.log('❌ Insert test failed:', insertError.message);
    } else {
      console.log('✅ Insert test successful');
      
      // Clean up test data
      if (insertData && insertData.length > 0) {
        await supabase
          .from('push_subscriptions')
          .delete()
          .eq('id', insertData[0].id);
        console.log('🧹 Test data cleaned up');
      }
    }
    
    console.log('\n🎉 Push notifications tables are ready!');
    
  } catch (err) {
    console.error('❌ Verification error:', err);
  }
}

verifyTables();
