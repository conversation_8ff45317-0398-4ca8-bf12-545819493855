import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import { cookies } from "next/headers";
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs";

// Function to log detailed information for debugging
function logDebug(message: string, data?: any) {
  console.log(`[MANAGER-DATA-API] ${message}`, data ? data : '');
}

// Function to log errors
function logError(message: string, error?: any) {
  console.error(`[MANAGER-DATA-API ERROR] ${message}`, error ? error : '');
}

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const adminClient = createClient(supabaseUrl, supabaseServiceKey);

export async function GET(request: Request) {
  try {
    logDebug("Starting business-admin manager-data API request");

    // Get the URL parameters
    const url = new URL(request.url);
    const businessId = url.searchParams.get('businessId');
    logDebug("Request parameters", { businessId });

    // Get the authorization header
    const authHeader = request.headers.get('Authorization');
    logDebug("Authorization header", {
      present: !!authHeader,
      length: authHeader ? authHeader.length : 0,
      start: authHeader ? authHeader.substring(0, 15) + '...' : 'none'
    });

    // Create a client with the user's session
    const cookieStore = cookies();
    const authClient = createServerComponentClient({ cookies: () => cookieStore });
    logDebug("Created server component client");

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession();

    // Check for custom token in cookies or headers
    const customToken = cookieStore.get('loop_jersey_auth_token')?.value;
    const userEmailCookie = cookieStore.get('loop_jersey_user_email')?.value;

    // Check for token in Authorization header
    const headerToken = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null;

    logDebug("Auth check result", {
      hasSession: !!session,
      hasCustomToken: !!customToken,
      hasHeaderToken: !!headerToken,
      userEmailCookie: userEmailCookie || null,
      sessionError: sessionError ? sessionError.message : null,
      userEmail: session?.user?.email || null
    });

    // If no session, try to use custom token
    let userEmail = session?.user?.email;

    if (!userEmail && userEmailCookie) {
      userEmail = decodeURIComponent(userEmailCookie);
      logDebug("Using email from cookie", userEmail);
    }

    if (!userEmail && !customToken && !headerToken) {
      logError("No authentication found");
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    logDebug("Authentication found for user", { userEmail });

    // Use the admin client to bypass RLS and check the user's role
    logDebug("Fetching user profile from database");
    const { data: userProfile, error: profileError } = await adminClient
      .from("users")
      .select("id, role, name")
      .eq("email", userEmail)
      .single();

    if (profileError) {
      logError("Error fetching user profile", profileError);
      return NextResponse.json(
        { error: "Error fetching user profile" },
        { status: 500 }
      );
    }

    if (!userProfile) {
      logError("User profile not found for email", { email: userEmail });
      return NextResponse.json(
        { error: "User profile not found" },
        { status: 403 }
      );
    }

    logDebug("Found user profile", {
      name: userProfile.name,
      role: userProfile.role,
      id: userProfile.id
    });

    // No special cases - use the role from the database

    // Check if the user has appropriate role
    const allowedRoles = ['business_staff', 'business_manager', 'admin', 'super_admin'];
    logDebug("Checking user role against allowed roles", {
      userRole: userProfile.role,
      allowedRoles,
      isAllowed: allowedRoles.includes(userProfile.role)
    });

    if (!allowedRoles.includes(userProfile.role)) {
      logError("Unauthorized access attempt", {
        email: userEmail,
        role: userProfile.role,
        allowedRoles
      });

      // No special cases - enforce consistent permissions
      return NextResponse.json(
        { error: "You do not have permission to access this resource" },
        { status: 403 }
      );
    }

    // For admin users, return a default business
    if (userProfile.role === 'admin' || userProfile.role === 'super_admin') {
      logDebug("Admin user detected, returning default admin view", {
        role: userProfile.role,
        businessId: businessId
      });

      // If a specific business ID was requested, try to get that business
      if (businessId) {
        logDebug("Admin user requested specific business", { businessId });

        try {
          const { data: business, error: businessError } = await adminClient
            .from("businesses")
            .select("*, business_types(name)")
            .eq("id", businessId)
            .single();

          if (businessError) {
            logError("Error fetching specific business for admin", {
              businessId,
              error: businessError
            });
          } else if (business) {
            logDebug("Found requested business for admin", business);
            return NextResponse.json({
              isAdminUser: true,
              businessData: {
                ...business,
                business_type: business.business_types?.name || "Business"
              }
            });
          }
        } catch (err) {
          logError("Exception fetching specific business for admin", err);
        }
      }

      // Default admin view if no specific business was found
      logDebug("Returning default admin view");
      return NextResponse.json({
        isAdminUser: true,
        businessData: {
          id: 0,
          name: "Admin View",
          business_type_id: 1,
          business_type: "Admin",
          logo_url: null
        }
      });
    }

    // No special cases - all users are treated consistently

    // For other business users, get their business data
    // First try to get the business manager relationship
    const { data: managerData, error: managerError } = await adminClient
      .from("business_managers")
      .select("business_id")
      .eq("user_id", userProfile.id)
      .maybeSingle();

    if (managerError) {
      console.error("Error fetching business manager data:", managerError);

      // No special cases - consistent error handling for all users

      // Try to find any approved business directly
      const { data: directBusinesses, error: directError } = await adminClient
        .from("businesses")
        .select("*, business_types(name)")
        .eq("is_approved", true)
        .limit(20);

      if (directError || !directBusinesses || directBusinesses.length === 0) {
        console.error("Error fetching businesses directly:", directError);

        // Check for pending business registrations
        const { data: pendingBusiness, error: pendingError } = await adminClient
          .from("business_registrations")
          .select("*")
          .eq("user_id", userProfile.id)
          .maybeSingle();

        if (!pendingError && pendingBusiness) {
          console.log("Found pending business registration:", pendingBusiness);
          return NextResponse.json({
            isPendingApproval: true,
            pendingBusiness
          });
        }

        return NextResponse.json({
          error: "No business found for this user",
          errorDetails: managerError
        }, { status: 404 });
      }

      // Find an approved business
      const approvedBusiness = directBusinesses.find(b => b.is_approved === true);
      if (approvedBusiness) {
        console.log("Found approved business:", approvedBusiness);
        return NextResponse.json({
          businessData: {
            ...approvedBusiness,
            business_type: approvedBusiness.business_types?.name || "Business"
          }
        });
      }
    }

    // If we found a manager relationship, get the business details
    if (managerData && managerData.business_id) {
      const { data: business, error: businessError } = await adminClient
        .from("businesses")
        .select("*, business_types(name)")
        .eq("id", managerData.business_id)
        .single();

      if (businessError || !business) {
        console.error("Error fetching business details:", businessError);
        return NextResponse.json({
          error: "Failed to fetch business details",
          errorDetails: businessError
        }, { status: 500 });
      }

      // Check if the business is approved
      if (business.is_approved === false) {
        console.log("Business exists but is not approved:", business);
        return NextResponse.json({
          isPendingApproval: true,
          business
        });
      }

      return NextResponse.json({
        businessData: {
          ...business,
          business_type: business.business_types?.name || "Business"
        }
      });
    }

    // If we get here, no business was found
    logError("No business found for this user", {
      userId: userProfile.id,
      userEmail: userEmail,
      userRole: userProfile.role
    });

    // Return a detailed error response
    return NextResponse.json({
      error: "No business found for this user",
      details: {
        userId: userProfile.id,
        userEmail: userEmail,
        userRole: userProfile.role
      }
    }, { status: 404 });

  } catch (error: any) {
    // Ensure we have a valid error message
    const errorMessage = error.message || "An unexpected error occurred";
    const errorStack = error.stack || "No stack trace available";

    logError("Unhandled exception in manager-data API", {
      message: errorMessage,
      stack: errorStack
    });

    // Return a detailed error response
    return NextResponse.json(
      {
        error: errorMessage,
        details: {
          timestamp: new Date().toISOString(),
          path: "/api/business-admin/manager-data",
          type: error.name || "UnknownError"
        }
      },
      { status: 500 }
    );
  }
}
