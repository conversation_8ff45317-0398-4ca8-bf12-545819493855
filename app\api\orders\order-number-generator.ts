import { createClient } from '@supabase/supabase-js';

// Create a Supabase client with admin privileges
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
);

/**
 * Generates a sequential order number with JE prefix
 * Format: JE-XXXX (e.g., JE-0001, JE-0002, etc.)
 */
export async function generateSequentialOrderNumber(): Promise<string> {
  try {
    console.log('Generating sequential order number');

    // Get the current highest order number from the orders table
    const { data, error } = await supabaseAdmin
      .from('orders')
      .select('order_number')
      .order('created_at', { ascending: false })
      .limit(10);

    if (error) {
      console.error('Error fetching latest order number:', error);
      // Fallback to a timestamp-based number if we can't get the latest
      const fallbackNumber = `JE-${new Date().getTime().toString().slice(-6)}`;
      console.log(`Using fallback order number due to error: ${fallbackNumber}`);
      return fallbackNumber;
    }

    console.log(`Found ${data?.length || 0} recent order numbers`);

    let nextNumber = 1; // Default starting number

    if (data && data.length > 0) {
      // Log the recent order numbers for debugging
      data.forEach((order, index) => {
        console.log(`Recent order number ${index + 1}: ${order.order_number}`);
      });

      // Find the highest numeric part from the recent order numbers
      let highestNumber = 0;

      for (const order of data) {
        if (order.order_number) {
          const match = order.order_number.match(/JE-(\d+)/);
          if (match && match[1]) {
            const num = parseInt(match[1], 10);
            if (!isNaN(num) && num > highestNumber) {
              highestNumber = num;
            }
          }
        }
      }

      if (highestNumber > 0) {
        nextNumber = highestNumber + 1;
        console.log(`Found highest order number: JE-${highestNumber}, next will be JE-${nextNumber}`);
      } else {
        console.log('No valid order numbers found, starting from JE-0001');
      }
    } else {
      console.log('No recent orders found, starting from JE-0001');
    }

    // Format the number with leading zeros (4 digits)
    const formattedNumber = nextNumber.toString().padStart(4, '0');
    const orderNumber = `JE-${formattedNumber}`;
    console.log(`Generated order number: ${orderNumber}`);
    return orderNumber;
  } catch (error) {
    console.error('Error generating sequential order number:', error);
    // Fallback to a timestamp-based number
    const fallbackNumber = `JE-${new Date().getTime().toString().slice(-6)}`;
    console.log(`Using fallback order number due to exception: ${fallbackNumber}`);
    return fallbackNumber;
  }
}

/**
 * Generates a business-specific order number based on the main order number
 * Format: JE-XXXX-YY (e.g., JE-0001-01, JE-0001-02, etc.)
 */
export function generateSubOrderNumber(mainOrderNumber: string, businessIndex: number): string {
  // Format the business index with leading zeros (2 digits)
  const formattedIndex = (businessIndex + 1).toString().padStart(2, '0');
  return `${mainOrderNumber}-${formattedIndex}`;
}

/**
 * Generates a unique order number for a specific business
 * Format: B{businessId}-{YY}{MM}{DD}-{random} (e.g., B4-230701-1234)
 */
export async function generateBusinessOrderNumber(businessId: number): Promise<string> {
  // Get current date components
  const now = new Date();
  const year = now.getFullYear().toString().slice(-2);
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');

  // Generate a random 4-digit number
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');

  // Format: B{businessId}-{YY}{MM}{DD}-{random}
  return `B${businessId}-${year}${month}${day}-${random}`;
}
