"use client";

import React from "react";
import { User, MapPin, Clock, CreditCard, CheckCircle } from "lucide-react";
import { useCheckout } from "../checkout-context";

export const CheckoutSteps: React.FC = () => {
  const { currentStep, stepsCompleted, goToStep } = useCheckout();

  const steps = [
    {
      id: 1,
      name: "Customer Info",
      icon: User,
      completed: stepsCompleted.customerInfo
    },
    {
      id: 2,
      name: "Delivery Address",
      icon: MapPin,
      completed: stepsCompleted.deliveryAddress
    },
    {
      id: 3,
      name: "Delivery Time",
      icon: Clock,
      completed: stepsCompleted.deliveryTime
    },
    {
      id: 4,
      name: "Payment",
      icon: CreditCard,
      completed: stepsCompleted.paymentMethod
    }
  ];

  return (
    <div className="bg-white rounded-lg shadow-md p-4 mb-6">
      <div className="flex flex-col md:flex-row justify-between">
        {steps.map((step, index) => (
          <React.Fragment key={step.id}>
            <div 
              className={`flex items-center ${
                currentStep === step.id ? 'text-emerald-600 font-medium' : 
                step.completed ? 'text-gray-600' : 'text-gray-400'
              } cursor-pointer`}
              onClick={() => {
                // Only allow navigation to completed steps or the current step
                if (step.completed || step.id <= currentStep) {
                  goToStep(step.id);
                }
              }}
            >
              <div className={`
                flex items-center justify-center h-8 w-8 rounded-full mr-2
                ${currentStep === step.id ? 'bg-emerald-100 text-emerald-600' : 
                  step.completed ? 'bg-gray-100 text-emerald-600' : 'bg-gray-100 text-gray-400'}
              `}>
                {step.completed ? (
                  <CheckCircle className="h-5 w-5" />
                ) : (
                  <step.icon className="h-5 w-5" />
                )}
              </div>
              <span className="hidden md:inline">{step.name}</span>
              <span className="md:hidden">{step.id}</span>
            </div>
            
            {/* Connector line between steps */}
            {index < steps.length - 1 && (
              <div className="hidden md:block h-px w-12 bg-gray-200 self-center mx-2"></div>
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};
