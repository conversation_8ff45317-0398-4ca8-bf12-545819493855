"use client"

import { useState } from 'react'
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Star,
  MessageSquare,
  Heart,
  MoreHorizontal,
  Building2,
  Truck,
  ShoppingBag,
  Clock,
  MapPin
} from "lucide-react"
import { Connection, UserRole } from '../types'
import { getRoleColor, getRoleBadgeVariant, getUserRoleInConnection } from '../mock-data'

interface ConnectionCardProps {
  connection: Connection
  currentUserRole?: UserRole
  onMessage?: (connection: Connection) => void
  onToggleFavorite?: (connectionId: string, isFavorite: boolean) => void
  onViewProfile?: (userId: string) => void
}

export function ConnectionCard({
  connection,
  currentUserRole = 'customer',
  onMessage,
  onToggleFavorite,
  onViewProfile
}: ConnectionCardProps) {
  const [isFavorite, setIsFavorite] = useState(connection.is_favorite)
  const [isLoading, setIsLoading] = useState(false)

  const otherUser = connection.other_user
  if (!otherUser) return null

  // Determine the other user's role in this connection
  const otherUserRole = getUserRoleInConnection(connection.connection_type, true)
  const roleColor = getRoleColor(otherUserRole)
  const roleBadgeVariant = getRoleBadgeVariant(otherUserRole)

  const getRoleIcon = (role: UserRole) => {
    switch (role) {
      case 'business': return <Building2 className="h-4 w-4" />
      case 'rider': return <Truck className="h-4 w-4" />
      case 'customer': return <ShoppingBag className="h-4 w-4" />
      default: return null
    }
  }

  const handleToggleFavorite = async () => {
    setIsLoading(true)
    try {
      const newFavoriteStatus = !isFavorite
      setIsFavorite(newFavoriteStatus)
      onToggleFavorite?.(connection.id, newFavoriteStatus)
    } catch (error) {
      // Revert on error
      setIsFavorite(isFavorite)
    } finally {
      setIsLoading(false)
    }
  }

  const getSpecialtyDisplay = () => {
    if (!otherUser.specialties) return null

    switch (otherUserRole) {
      case 'business':
        return otherUser.specialties.cuisine_types?.slice(0, 2).join(', ')
      case 'rider':
        return `${otherUser.specialties.vehicle_type} • ${otherUser.specialties.areas?.slice(0, 2).join(', ')}`
      case 'customer':
        return otherUser.specialties.favorite_cuisines?.slice(0, 2).join(', ')
      default:
        return null
    }
  }

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3 p-4 sm:p-6">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-2 sm:space-x-3 flex-1 min-w-0">
            <Avatar className="h-10 w-10 sm:h-12 sm:w-12 shrink-0">
              <AvatarImage src={otherUser.avatar_url} alt={otherUser.display_name} />
              <AvatarFallback className={`bg-${roleColor}-100 text-${roleColor}-700`}>
                {otherUser.display_name.split(' ').map(n => n[0]).join('').toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 mb-1">
                <h3 className="font-semibold text-sm sm:text-base truncate">{otherUser.display_name}</h3>
                <Badge variant={roleBadgeVariant} className="text-xs w-fit">
                  <span className="flex items-center gap-1">
                    {getRoleIcon(otherUserRole)}
                    <span className="hidden sm:inline">{otherUserRole}</span>
                    <span className="sm:hidden">{otherUserRole.charAt(0).toUpperCase()}</span>
                  </span>
                </Badge>
              </div>
              {otherUser.average_rating && (
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                  <span>{otherUser.average_rating.toFixed(1)}</span>
                  <span className="hidden sm:inline">({otherUser.total_ratings} reviews)</span>
                  <span className="sm:hidden">({otherUser.total_ratings})</span>
                </div>
              )}
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleToggleFavorite}
            disabled={isLoading}
            className="shrink-0 p-2"
          >
            <Heart
              className={`h-4 w-4 ${isFavorite ? 'fill-red-500 text-red-500' : 'text-gray-400'}`}
            />
          </Button>
        </div>
      </CardHeader>

      <CardContent className="pt-0 p-4 sm:p-6">
        {otherUser.bio && (
          <p className="text-xs sm:text-sm text-muted-foreground mb-3 line-clamp-2">
            {otherUser.bio}
          </p>
        )}

        {getSpecialtyDisplay() && (
          <div className="flex items-center gap-1 text-xs text-muted-foreground mb-3">
            <MapPin className="h-3 w-3 shrink-0" />
            <span className="truncate">{getSpecialtyDisplay()}</span>
          </div>
        )}

        {connection.notes && (
          <div className="bg-muted/50 rounded-md p-2 mb-3">
            <p className="text-xs text-muted-foreground italic">
              "{connection.notes}"
            </p>
          </div>
        )}

        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <Clock className="h-3 w-3 shrink-0" />
            <span className="hidden sm:inline">Connected {new Date(connection.created_at).toLocaleDateString()}</span>
            <span className="sm:hidden">{new Date(connection.created_at).toLocaleDateString()}</span>
          </div>

          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onViewProfile?.(otherUser.user_id)}
              className="text-xs flex-1 sm:flex-none"
            >
              <span className="hidden sm:inline">View Profile</span>
              <span className="sm:hidden">Profile</span>
            </Button>
            <Button
              size="sm"
              onClick={() => onMessage?.(connection)}
              className="text-xs bg-emerald-600 hover:bg-emerald-700 flex-1 sm:flex-none"
            >
              <MessageSquare className="h-3 w-3 mr-1" />
              Message
            </Button>
          </div>
        </div>

        {connection.status === 'pending' && (
          <div className="mt-3 pt-3 border-t">
            <div className="flex gap-2">
              <Button size="sm" className="flex-1 text-xs">
                Accept
              </Button>
              <Button variant="outline" size="sm" className="flex-1 text-xs">
                Decline
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
