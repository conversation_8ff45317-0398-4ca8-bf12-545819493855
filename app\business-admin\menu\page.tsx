"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import {
  Search,
  Plus,
  Filter,
  ChevronDown,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  ArrowUpDown,
  Star,
  DollarSign,
  RefreshCcw,
  AlertCircle,
  Tag,
  Percent,
  Clock,
  Settings,
  FileText,
  Utensils,
  ShoppingBag
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/components/ui/use-toast"
import { useAuthDirect } from "@/context/auth-context-direct"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import ProductForm from "./product-form"

interface Category {
  id: number
  name: string
  description?: string
  display_order: number
  product_count?: number
}

interface ProductVariant {
  id: number
  name: string
  price_adjustment: number
  is_default: boolean
  is_available: boolean
  display_order: number
}

interface Product {
  id: number
  name: string
  description?: string
  price: number
  image_url?: string
  category_id: number
  is_available: boolean
  is_featured: boolean
  variants?: ProductVariant[]
  created_at: string
  updated_at: string
}

export default function BusinessMenuPage() {
  const { toast } = useToast()
  const { user, userProfile } = useAuthDirect()
  const [categories, setCategories] = useState<Category[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("all")
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [productToDelete, setProductToDelete] = useState<Product | null>(null)
  const [isAddEditDialogOpen, setIsAddEditDialogOpen] = useState(false)
  const [currentProduct, setCurrentProduct] = useState<Product | null>(null)
  const [currentCategoryId, setCurrentCategoryId] = useState<number | null>(null)
  const [isAddCategoryDialogOpen, setIsAddCategoryDialogOpen] = useState(false)
  const [currentCategory, setCurrentCategory] = useState<Category | null>(null)
  const [isCategoryDeleteDialogOpen, setIsCategoryDeleteDialogOpen] = useState(false)
  const [categoryToDelete, setCategoryToDelete] = useState<Category | null>(null)

  // Fetch categories and products
  useEffect(() => {
    fetchCategoriesAndProducts()
  }, [])

  const fetchCategoriesAndProducts = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // Initialize the API (create buckets if needed)
      await fetch("/api/init", {
        method: "GET",
        credentials: "include"
      })

      // Fetch categories
      console.log("Fetching categories...")
      const categoriesResponse = await fetch("/api/business-admin/categories", {
        method: "GET",
        headers: {
          "Content-Type": "application/json"
        },
        credentials: "include"
      })

      console.log("Categories response status:", categoriesResponse.status)

      if (!categoriesResponse.ok) {
        const errorText = await categoriesResponse.text()
        console.error("Categories response error:", errorText)
        throw new Error(`Failed to fetch categories: ${categoriesResponse.status} ${errorText}`)
      }

      const categoriesData = await categoriesResponse.json()
      console.log("Categories data:", categoriesData)
      setCategories(categoriesData.categories || [])

      // Fetch products
      console.log("Fetching products...")
      const productsResponse = await fetch("/api/business-admin/products", {
        method: "GET",
        headers: {
          "Content-Type": "application/json"
        },
        credentials: "include"
      })

      console.log("Products response status:", productsResponse.status)

      if (!productsResponse.ok) {
        const errorText = await productsResponse.text()
        console.error("Products response error:", errorText)
        throw new Error(`Failed to fetch products: ${productsResponse.status} ${errorText}`)
      }

      const productsData = await productsResponse.json()
      console.log("Products data:", productsData)
      setProducts(productsData.products || [])
    } catch (err: any) {
      console.error("Error fetching menu data:", err)
      setError(err.message || "Failed to load menu data")
      toast({
        variant: "destructive",
        title: "Error",
        description: err.message || "Failed to load menu data"
      })
    } finally {
      setIsLoading(false)
      setIsRefreshing(false)
    }
  }

  // Handle refresh
  const handleRefresh = () => {
    setIsRefreshing(true)
    fetchCategoriesAndProducts()
  }

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value)
  }

  // Filter products based on search query and active tab
  const filteredProducts = products.filter(product => {
    // Filter by search query
    if (searchQuery && !product.name.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false
    }

    // Filter by category tab
    if (activeTab !== "all" && activeTab !== "featured") {
      return product.category_id === parseInt(activeTab)
    }

    // Filter featured products
    if (activeTab === "featured") {
      return product.is_featured
    }

    return true
  })

  // Group products by category
  const productsByCategory = filteredProducts.reduce((acc, product) => {
    const categoryId = product.category_id
    if (!acc[categoryId]) {
      acc[categoryId] = []
    }
    acc[categoryId].push(product)
    return acc
  }, {} as Record<number, Product[]>)

  // Handle product availability toggle
  const handleAvailabilityToggle = async (productId: number, isAvailable: boolean) => {
    try {
      const response = await fetch(`/api/business-admin/products/${productId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ is_available: isAvailable }),
        credentials: "include"
      })

      if (!response.ok) {
        throw new Error("Failed to update product availability")
      }

      // Update local state
      setProducts(products.map(product =>
        product.id === productId ? { ...product, is_available: isAvailable } : product
      ))

      toast({
        title: "Product Updated",
        description: `Product is now ${isAvailable ? "available" : "unavailable"}`,
        duration: 3000
      })
    } catch (err: any) {
      console.error("Error updating product availability:", err)
      toast({
        variant: "destructive",
        title: "Error",
        description: err.message || "Failed to update product availability"
      })
    }
  }

  // Handle featured toggle
  const handleFeaturedToggle = async (productId: number, isFeatured: boolean) => {
    try {
      const response = await fetch(`/api/business-admin/products/${productId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ is_featured: isFeatured }),
        credentials: "include"
      })

      if (!response.ok) {
        throw new Error("Failed to update product featured status")
      }

      // Update local state
      setProducts(products.map(product =>
        product.id === productId ? { ...product, is_featured: isFeatured } : product
      ))

      toast({
        title: "Product Updated",
        description: `Product is ${isFeatured ? "now featured" : "no longer featured"}`,
        duration: 3000
      })
    } catch (err: any) {
      console.error("Error updating product featured status:", err)
      toast({
        variant: "destructive",
        title: "Error",
        description: err.message || "Failed to update product featured status"
      })
    }
  }

  // Open delete dialog
  const openDeleteDialog = (product: Product) => {
    setProductToDelete(product)
    setIsDeleteDialogOpen(true)
  }

  // Handle product delete
  const handleDeleteProduct = async () => {
    if (!productToDelete) return

    try {
      const response = await fetch(`/api/business-admin/products/${productToDelete.id}`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json"
        },
        credentials: "include"
      })

      if (!response.ok) {
        throw new Error("Failed to delete product")
      }

      // Update local state
      setProducts(products.filter(product => product.id !== productToDelete.id))

      toast({
        title: "Product Deleted",
        description: "Product has been deleted successfully",
        duration: 3000
      })

      setIsDeleteDialogOpen(false)
      setProductToDelete(null)
    } catch (err: any) {
      console.error("Error deleting product:", err)
      toast({
        variant: "destructive",
        title: "Error",
        description: err.message || "Failed to delete product"
      })
    }
  }

  // Open add/edit dialog
  const openAddEditDialog = (product?: Product, categoryId?: number) => {
    setCurrentProduct(product || null)
    setCurrentCategoryId(categoryId || null)
    setIsAddEditDialogOpen(true)
  }

  // Loading state
  if (isLoading && !isRefreshing) {
    return (
      <div>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold">Menu Management</h1>
            <p className="text-gray-500">Manage your products, categories, and pricing</p>
          </div>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading menu data...</p>
          </div>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold">Menu Management</h1>
            <p className="text-gray-500">Manage your products, categories, and pricing</p>
          </div>
        </div>
        <Card>
          <CardHeader>
            <CardTitle>Error</CardTitle>
            <CardDescription>There was a problem loading the menu data</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col space-y-4">
              <div className="flex items-center text-red-600">
                <AlertCircle className="mr-2 h-5 w-5 flex-shrink-0" />
                <p>{error}</p>
              </div>

              <div className="bg-gray-100 p-4 rounded-md">
                <h3 className="font-medium mb-2">Troubleshooting Steps:</h3>
                <ol className="list-decimal list-inside space-y-1 text-sm">
                  <li>Check if you're logged in with a business manager account</li>
                  <li>Verify that your account has the correct permissions</li>
                  <li>Make sure your business is properly set up in the system</li>
                  <li>Check your network connection</li>
                  <li>Try refreshing the page</li>
                </ol>
              </div>

              <div className="text-sm text-gray-500">
                <p>If the problem persists, please contact support with the error message above.</p>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" onClick={() => window.location.href = "/business-admin"}>
              Back to Dashboard
            </Button>
            <Button onClick={handleRefresh}>
              <RefreshCcw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
          </CardFooter>
        </Card>
      </div>
    )
  }

  return (
    <div>
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">Menu Management</h1>
          <p className="text-gray-500">Manage your products, categories, and pricing</p>
        </div>
        <div className="mt-4 md:mt-0 flex flex-wrap gap-2">
          <div className="relative w-full md:w-64">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search products..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8"
            />
          </div>
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="flex items-center gap-2"
          >
            <RefreshCcw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            variant="outline"
            onClick={() => setIsAddCategoryDialogOpen(true)}
            className="flex items-center gap-2"
          >
            <Tag className="h-4 w-4" />
            Add Category
          </Button>
          <Button
            className="bg-emerald-600 hover:bg-emerald-700"
            onClick={() => openAddEditDialog()}
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Product
          </Button>
          <Link href="/business-admin/menu/test">
            <Button
              variant="outline"
              className="flex items-center gap-2"
            >
              <Settings className="h-4 w-4" />
              API Test
            </Button>
          </Link>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={handleTabChange} className="mb-6">
        <TabsList className="mb-4 overflow-x-auto flex w-full">
          <TabsTrigger value="all" className="flex-shrink-0">All Products</TabsTrigger>
          <TabsTrigger value="featured" className="flex-shrink-0">Featured</TabsTrigger>
          {categories.map((category) => (
            <TabsTrigger
              key={category.id}
              value={category.id.toString()}
              className="flex-shrink-0"
            >
              {category.name}
            </TabsTrigger>
          ))}
        </TabsList>

        <TabsContent value="all" className="space-y-6">
          {categories.length === 0 ? (
            <Alert>
              <AlertDescription>
                No categories found. Create a category to organize your products.
              </AlertDescription>
            </Alert>
          ) : (
            categories.map((category) => {
              const categoryProducts = productsByCategory[category.id] || []
              if (categoryProducts.length === 0 && searchQuery) return null

              return (
                <div key={category.id} className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <h2 className="text-xl font-semibold">{category.name}</h2>
                      <Badge variant="outline" className="text-gray-500">
                        {categoryProducts.length} {categoryProducts.length === 1 ? 'product' : 'products'}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-emerald-600"
                        onClick={() => openAddEditDialog(undefined, category.id)}
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Add to {category.name}
                      </Button>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => {
                            setCurrentCategory(category)
                            setIsAddCategoryDialogOpen(true)
                          }}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Category
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="text-red-600"
                            onClick={() => {
                              setCategoryToDelete(category)
                              setIsCategoryDeleteDialogOpen(true)
                            }}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete Category
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>

                  {categoryProducts.length === 0 ? (
                    <Alert>
                      <AlertDescription>
                        No products in this category. Add a product to get started.
                      </AlertDescription>
                    </Alert>
                  ) : (
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                      {categoryProducts.map((product) => (
                        <ProductCard
                          key={product.id}
                          product={product}
                          onEdit={() => openAddEditDialog(product)}
                          onDelete={() => openDeleteDialog(product)}
                          onToggleAvailability={(isAvailable) =>
                            handleAvailabilityToggle(product.id, isAvailable)
                          }
                          onToggleFeatured={(isFeatured) =>
                            handleFeaturedToggle(product.id, isFeatured)
                          }
                        />
                      ))}
                    </div>
                  )}
                </div>
              )
            })
          )}
        </TabsContent>

        <TabsContent value="featured" className="space-y-6">
          {filteredProducts.length === 0 ? (
            <Alert>
              <AlertDescription>
                No featured products found. Mark products as featured to display them here.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
              {filteredProducts.map((product) => (
                <ProductCard
                  key={product.id}
                  product={product}
                  onEdit={() => openAddEditDialog(product)}
                  onDelete={() => openDeleteDialog(product)}
                  onToggleAvailability={(isAvailable) =>
                    handleAvailabilityToggle(product.id, isAvailable)
                  }
                  onToggleFeatured={(isFeatured) =>
                    handleFeaturedToggle(product.id, isFeatured)
                  }
                />
              ))}
            </div>
          )}
        </TabsContent>

        {categories.map((category) => (
          <TabsContent key={category.id} value={category.id.toString()} className="space-y-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h2 className="text-xl font-semibold">{category.name}</h2>
                {category.description && (
                  <p className="text-gray-500 mt-1">{category.description}</p>
                )}
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="text-emerald-600"
                  onClick={() => openAddEditDialog(undefined, category.id)}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Add Product
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => {
                      setCurrentCategory(category)
                      setIsAddCategoryDialogOpen(true)
                    }}>
                      <Edit className="mr-2 h-4 w-4" />
                      Edit Category
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      className="text-red-600"
                      onClick={() => {
                        setCategoryToDelete(category)
                        setIsCategoryDeleteDialogOpen(true)
                      }}
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete Category
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>

            {filteredProducts.length === 0 ? (
              <Alert>
                <AlertDescription>
                  No products found in this category. Add a product or adjust your search.
                </AlertDescription>
              </Alert>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                {filteredProducts.map((product) => (
                  <ProductCard
                    key={product.id}
                    product={product}
                    onEdit={() => openAddEditDialog(product)}
                    onDelete={() => openDeleteDialog(product)}
                    onToggleAvailability={(isAvailable) =>
                      handleAvailabilityToggle(product.id, isAvailable)
                    }
                    onToggleFeatured={(isFeatured) =>
                      handleFeaturedToggle(product.id, isFeatured)
                    }
                  />
                ))}
              </div>
            )}
          </TabsContent>
        ))}
      </Tabs>

      {/* Delete Product Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Product</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this product? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteProduct}
            >
              Delete
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Category Dialog */}
      <Dialog open={isCategoryDeleteDialogOpen} onOpenChange={setIsCategoryDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Category</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this category? All products in this category will be moved to uncategorized.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setIsCategoryDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={async () => {
                if (!categoryToDelete) return

                try {
                  const response = await fetch(`/api/business-admin/categories/${categoryToDelete.id}`, {
                    method: "DELETE",
                    headers: {
                      "Content-Type": "application/json"
                    },
                    credentials: "include"
                  })

                  if (!response.ok) {
                    throw new Error("Failed to delete category")
                  }

                  // Update local state
                  setCategories(categories.filter(c => c.id !== categoryToDelete.id))

                  // Update products that were in this category
                  setProducts(products.map(p =>
                    p.category_id === categoryToDelete.id
                      ? { ...p, category_id: undefined }
                      : p
                  ))

                  toast({
                    title: "Category Deleted",
                    description: "Category has been deleted successfully",
                    duration: 3000
                  })

                  setIsCategoryDeleteDialogOpen(false)
                  setCategoryToDelete(null)
                } catch (err: any) {
                  console.error("Error deleting category:", err)
                  toast({
                    variant: "destructive",
                    title: "Error",
                    description: err.message || "Failed to delete category"
                  })
                }
              }}
            >
              Delete
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Add/Edit Product Dialog */}
      <Dialog open={isAddEditDialogOpen} onOpenChange={setIsAddEditDialogOpen}>
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle>{currentProduct ? "Edit Product" : "Add New Product"}</DialogTitle>
            <DialogDescription>
              {currentProduct
                ? "Update the details of this product"
                : "Fill in the details to add a new product to your menu"}
            </DialogDescription>
          </DialogHeader>
          <ProductForm
            product={currentProduct || undefined}
            categoryId={currentCategoryId || undefined}
            categories={categories}
            onSubmit={async (productData) => {
              try {
                if (currentProduct) {
                  // Update existing product
                  const response = await fetch(`/api/business-admin/products/${currentProduct.id}`, {
                    method: "PATCH",
                    headers: {
                      "Content-Type": "application/json"
                    },
                    body: JSON.stringify(productData),
                    credentials: "include"
                  })

                  if (!response.ok) {
                    throw new Error("Failed to update product")
                  }

                  const data = await response.json()

                  // Update local state
                  setProducts(products.map(p =>
                    p.id === data.product.id ? data.product : p
                  ))

                  toast({
                    title: "Product Updated",
                    description: "Product has been updated successfully",
                    duration: 3000
                  })
                } else {
                  // Create new product
                  const response = await fetch("/api/business-admin/products", {
                    method: "POST",
                    headers: {
                      "Content-Type": "application/json"
                    },
                    body: JSON.stringify(productData),
                    credentials: "include"
                  })

                  if (!response.ok) {
                    throw new Error("Failed to create product")
                  }

                  const data = await response.json()

                  // Update local state
                  setProducts([...products, data.product])

                  toast({
                    title: "Product Created",
                    description: "Product has been created successfully",
                    duration: 3000
                  })
                }

                // Close the dialog
                setIsAddEditDialogOpen(false)
                setCurrentProduct(null)
                setCurrentCategoryId(null)
              } catch (err: any) {
                console.error("Error saving product:", err)
                toast({
                  variant: "destructive",
                  title: "Error",
                  description: err.message || "Failed to save product"
                })
              }
            }}
            onCancel={() => {
              setIsAddEditDialogOpen(false)
              setCurrentProduct(null)
              setCurrentCategoryId(null)
            }}
          />
        </DialogContent>
      </Dialog>

      {/* Add/Edit Category Dialog */}
      <Dialog open={isAddCategoryDialogOpen} onOpenChange={setIsAddCategoryDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{currentCategory ? "Edit Category" : "Add New Category"}</DialogTitle>
            <DialogDescription>
              {currentCategory
                ? "Update the details of this category"
                : "Create a new category to organize your products"}
            </DialogDescription>
          </DialogHeader>
          <form
            onSubmit={async (e) => {
              e.preventDefault()

              const formData = new FormData(e.currentTarget)
              const name = formData.get("categoryName") as string
              const description = formData.get("categoryDescription") as string
              const displayOrder = parseInt(formData.get("displayOrder") as string) || 0

              if (!name.trim()) {
                toast({
                  variant: "destructive",
                  title: "Error",
                  description: "Category name is required"
                })
                return
              }

              try {
                if (currentCategory) {
                  // Update existing category
                  const response = await fetch(`/api/business-admin/categories/${currentCategory.id}`, {
                    method: "PATCH",
                    headers: {
                      "Content-Type": "application/json"
                    },
                    body: JSON.stringify({
                      name,
                      description,
                      display_order: displayOrder
                    }),
                    credentials: "include"
                  })

                  if (!response.ok) {
                    throw new Error("Failed to update category")
                  }

                  const data = await response.json()

                  // Update local state
                  setCategories(categories.map(c =>
                    c.id === data.category.id ? data.category : c
                  ))

                  toast({
                    title: "Category Updated",
                    description: "Category has been updated successfully",
                    duration: 3000
                  })
                } else {
                  // Create new category
                  const response = await fetch("/api/business-admin/categories", {
                    method: "POST",
                    headers: {
                      "Content-Type": "application/json"
                    },
                    body: JSON.stringify({
                      name,
                      description,
                      display_order: displayOrder
                    }),
                    credentials: "include"
                  })

                  if (!response.ok) {
                    throw new Error("Failed to create category")
                  }

                  const data = await response.json()

                  // Update local state
                  setCategories([...categories, data.category])

                  toast({
                    title: "Category Created",
                    description: "Category has been created successfully",
                    duration: 3000
                  })
                }

                // Close the dialog
                setIsAddCategoryDialogOpen(false)
                setCurrentCategory(null)
              } catch (err: any) {
                console.error("Error saving category:", err)
                toast({
                  variant: "destructive",
                  title: "Error",
                  description: err.message || "Failed to save category"
                })
              }
            }}
            className="space-y-4 py-4"
          >
            <div className="space-y-2">
              <Label htmlFor="categoryName">Category Name</Label>
              <Input
                id="categoryName"
                name="categoryName"
                placeholder="e.g., Appetizers, Main Courses, Desserts"
                defaultValue={currentCategory?.name || ""}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="categoryDescription">Description (Optional)</Label>
              <Input
                id="categoryDescription"
                name="categoryDescription"
                placeholder="Brief description of this category"
                defaultValue={currentCategory?.description || ""}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="displayOrder">Display Order</Label>
              <Input
                id="displayOrder"
                name="displayOrder"
                type="number"
                min="0"
                placeholder="0"
                defaultValue={currentCategory?.display_order.toString() || "0"}
              />
              <p className="text-sm text-gray-500">Lower numbers appear first</p>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsAddCategoryDialogOpen(false)
                  setCurrentCategory(null)
                }}
              >
                Cancel
              </Button>
              <Button type="submit">Save Category</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  )
}

// Product Card Component
function ProductCard({
  product,
  onEdit,
  onDelete,
  onToggleAvailability,
  onToggleFeatured
}: {
  product: Product
  onEdit: () => void
  onDelete: () => void
  onToggleAvailability: (isAvailable: boolean) => void
  onToggleFeatured: (isFeatured: boolean) => void
}) {
  return (
    <Card className="overflow-hidden">
      <CardContent className="p-0">
        {product.image_url && (
          <div className="h-40 w-full relative">
            <img
              src={product.image_url}
              alt={product.name}
              className="h-full w-full object-contain"
              onError={(e) => {
                (e.target as HTMLImageElement).src = '/placeholder-food.jpg'
              }}
            />
            {product.is_featured && (
              <Badge className="absolute top-2 right-2 bg-orange-500">
                <Star className="mr-1 h-3 w-3" />
                Featured
              </Badge>
            )}
          </div>
        )}
        <div className="p-4">
          <div className="flex justify-between items-start mb-2">
            <div>
              <h3 className="font-medium text-lg">{product.name}</h3>
              <div className="flex items-center mt-1">
                <DollarSign className="h-4 w-4 text-gray-500 mr-1" />
                <span className="font-medium">{product.price.toFixed(2)}</span>
                {product.variants && product.variants.length > 0 && (
                  <Badge variant="outline" className="ml-2 text-xs">
                    {product.variants.length} variants
                  </Badge>
                )}
              </div>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={onEdit}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onToggleFeatured(!product.is_featured)}>
                  <Star className="mr-2 h-4 w-4" />
                  {product.is_featured ? "Remove from Featured" : "Mark as Featured"}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onToggleAvailability(!product.is_available)}>
                  <Eye className="mr-2 h-4 w-4" />
                  {product.is_available ? "Mark as Unavailable" : "Mark as Available"}
                </DropdownMenuItem>
                <DropdownMenuItem className="text-red-600" onClick={onDelete}>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {product.description && (
            <p className="text-gray-500 text-sm line-clamp-2 mb-3">{product.description}</p>
          )}

          <div className="flex items-center justify-between mt-2">
            <div className="flex items-center">
              <Switch
                id={`available-${product.id}`}
                checked={product.is_available}
                onCheckedChange={onToggleAvailability}
              />
              <Label
                htmlFor={`available-${product.id}`}
                className="ml-2 text-sm"
              >
                {product.is_available ? "Available" : "Unavailable"}
              </Label>
            </div>
            <Button variant="outline" size="sm" onClick={onEdit}>
              Manage
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
