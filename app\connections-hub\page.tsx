"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useAuth } from "@/context/unified-auth-context"
import { addAuthHeaders } from '@/utils/auth-token'
import { useRouter } from "next/navigation"
import { ConnectionsHub as ConnectionsHubComponent } from './components/ConnectionsHub'
import {
  Building2,
  ShieldCheck,
  Loader2,
  Truck,
  ShoppingBag,
  HelpCircle,
  Info
} from "lucide-react"

type UserRole = "customer" | "business" | "rider" | "admin"

interface BusinessData {
  id: string
  name: string
  business_type: string
}

export default function ConnectionsHub() {
  const router = useRouter()
  const { user, userProfile, isAdmin, isSuperAdmin } = useAuth()
  const [business, setBusiness] = useState<BusinessData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [userRole, setUserRole] = useState<UserRole>("customer")

  useEffect(() => {
    const initializeData = async () => {
      if (!user) {
        setIsLoading(false)
        return
      }

      try {
        // Determine user role
        let role: UserRole = "customer"
        if (isAdmin || isSuperAdmin) {
          role = "admin"
        } else if (userProfile?.role === "business_manager") {
          role = "business"
          // Fetch business data for business users
          const response = await fetch('/api/business/profile', {
            headers: addAuthHeaders({})
          })
          if (response.ok) {
            const businessData = await response.json()
            setBusiness(businessData)
          }
        } else if (userProfile?.role === "rider") {
          role = "rider"
        }

        setUserRole(role)
      } catch (error) {
        console.error('Error initializing connections hub:', error)
        setError('Failed to load user data')
      } finally {
        setIsLoading(false)
      }
    }

    initializeData()
  }, [user, userProfile, isAdmin, isSuperAdmin])

  const getUserDisplayName = () => {
    if (userProfile?.name) return userProfile.name
    if (userProfile?.first_name && userProfile?.last_name) {
      return `${userProfile.first_name} ${userProfile.last_name}`
    }
    if (user?.email) return user.email.split('@')[0]
    return 'User'
  }

  const getRoleIcon = () => {
    switch (userRole) {
      case "business":
        return <Building2 className="h-5 w-5 text-blue-600" />
      case "rider":
        return <Truck className="h-5 w-5 text-green-600" />
      case "admin":
        return <ShieldCheck className="h-5 w-5 text-purple-600" />
      default:
        return <ShoppingBag className="h-5 w-5 text-orange-600" />
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-white">
        <main className="container-fluid py-6">
          <div className="flex items-center justify-center h-full min-h-[400px]">
            <Loader2 className="h-8 w-8 animate-spin text-emerald-600" />
          </div>
        </main>
      </div>
    )
  }

  if (!user) {
    router.push('/auth/login')
    return null
  }

  return (
    <div className="min-h-screen bg-white">
      <main className="container-fluid py-6">
        <div className="space-y-6">
          {/* Header with user info and help link */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 gap-4">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold flex items-center gap-2">
                Connections Hub
                {getRoleIcon()}
              </h1>
              <p className="text-gray-500 text-sm sm:text-base">
                Welcome, {getUserDisplayName()}
              </p>
              <Badge variant="outline" className="mt-2 capitalize text-xs sm:text-sm">
                {userRole} Account
              </Badge>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/connections-hub/help')}
                className="text-xs sm:text-sm"
              >
                <HelpCircle className="h-4 w-4 mr-1 sm:mr-2" />
                <span className="hidden sm:inline">Help & Info</span>
                <span className="sm:hidden">Help</span>
              </Button>
            </div>
          </div>

          {/* Main Connections Hub Component */}
          <ConnectionsHubComponent />
        </div>
      </main>
    </div>
  )
}
