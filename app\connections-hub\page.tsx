"use client"

import { useState, useEffect } from "react"
import { Card, CardHeader, CardTitle, CardContent, CardFooter, CardDescription } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON><PERSON>Content } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { useAuth } from "@/context/unified-auth-context"
import { addAuthHeaders } from '@/utils/auth-token'
import { useRouter } from "next/navigation"
import { ConnectionsHub as ConnectionsHubComponent } from './components/ConnectionsHub'
import {
  MessageSquare,
  Star,
  UserCircle,
  Building2,
  Users,
  ShieldCheck,
  ArrowRight,
  CheckCircle2,
  Clock,
  MapPin,
  Coffee,
  Loader2,
  Truck,
  ShoppingBag
} from "lucide-react"

interface BusinessData {
  id: number
  name: string
  slug: string
  description: string | null
  logo_url: string | null
  banner_url: string | null
  address: string
  location: string
  business_type_id: number
  business_type?: string
  business_types?: {
    id: number
    name: string
    slug: string
  }
}

type UserRole = "customer" | "business" | "rider" | "admin"

export default function ConnectionsHub() {
  const router = useRouter()
  const { user, userProfile, isAdmin, isSuperAdmin } = useAuth()
  const [activeTab, setActiveTab] = useState("overview")
  const [business, setBusiness] = useState<BusinessData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [userRole, setUserRole] = useState<UserRole>("customer")

  // Determine user role based on profile and context
  useEffect(() => {
    if (isAdmin || isSuperAdmin) {
      setUserRole("admin")
    } else if (userProfile?.role) {
      // Map the auth context roles to our UserRole type
      const roleMapping: Record<string, UserRole> = {
        'customer': 'customer',
        'business_staff': 'business',
        'business_manager': 'business',
        'admin': 'admin',
        'super_admin': 'admin'
      }
      setUserRole(roleMapping[userProfile.role] || "customer")
    } else {
      // Default to customer if no specific role is found
      setUserRole("customer")
    }
  }, [userProfile, isAdmin, isSuperAdmin])

  // Fetch business data only if user is a business owner
  useEffect(() => {
    async function fetchBusinessData() {
      if (!user || userRole !== "business") {
        setIsLoading(false)
        return
      }

      try {
        setIsLoading(true)

        // Build the URL
        let url = '/api/business-admin/business-data'

        // Use the server API to fetch business data
        const response = await fetch(url, {
          method: 'GET',
          headers: addAuthHeaders()
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ error: response.statusText }));
          console.error("Error fetching business data:", errorData.error);
          // For business users, this might be normal if they don't have a business yet
          if (userRole === "business") {
            setBusiness(null);
          } else {
            setError("Failed to load business data. Please try again.");
          }
          setIsLoading(false);
          return;
        }

        const data = await response.json();

        if (data.business) {
          setBusiness(data.business);
        } else {
          console.log("No business data returned - user may not have a business yet");
          setBusiness(null);
        }
      } catch (err) {
        console.error("Error in fetchBusinessData:", err);
        setError("An error occurred while loading business data.");
      } finally {
        setIsLoading(false);
      }
    }

    fetchBusinessData();
  }, [user, userRole]);

  const getUserDisplayName = () => {
    if (userRole === "business" && business?.name) {
      return `${userProfile?.name || user?.email || "User"} | ${business.name}${business?.business_types?.name ? ` - ${business.business_types.name}` : ""}`
    }
    return userProfile?.name || user?.email || "User"
  }

  const getRoleIcon = () => {
    switch (userRole) {
      case "business":
        return <Building2 className="h-5 w-5 text-blue-500" />
      case "rider":
        return <Truck className="h-5 w-5 text-green-500" />
      case "customer":
        return <ShoppingBag className="h-5 w-5 text-purple-500" />
      case "admin":
        return <ShieldCheck className="h-5 w-5 text-red-500" />
      default:
        return <UserCircle className="h-5 w-5 text-gray-500" />
    }
  }

  const getRoleSpecificTabs = () => {
    const baseTabs = [
      { value: "overview", label: "Overview" },
      { value: "getting-started", label: "Getting Started" }
    ]

    switch (userRole) {
      case "business":
        return [
          ...baseTabs,
          { value: "for-businesses", label: "For Businesses" },
          { value: "connections", label: "My Connections" }
        ]
      case "rider":
        return [
          ...baseTabs,
          { value: "for-riders", label: "For Riders" },
          { value: "connections", label: "My Connections" }
        ]
      case "customer":
        return [
          ...baseTabs,
          { value: "for-customers", label: "For Customers" },
          { value: "connections", label: "My Connections" }
        ]
      case "admin":
        return [
          ...baseTabs,
          { value: "for-businesses", label: "For Businesses" },
          { value: "for-riders", label: "For Riders" },
          { value: "for-customers", label: "For Customers" },
          { value: "admin", label: "Admin" }
        ]
      default:
        return baseTabs
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-white">
        <main className="container-fluid py-6">
          <div className="flex items-center justify-center h-full min-h-[400px]">
            <Loader2 className="h-8 w-8 animate-spin text-emerald-600" />
          </div>
        </main>
      </div>
    )
  }

  const tabs = getRoleSpecificTabs()

  return (
    <div className="min-h-screen bg-white">
      <main className="container-fluid py-6">
        <div className="space-y-8">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold flex items-center gap-2">
                Connections Hub
                {getRoleIcon()}
              </h1>
              <p className="text-gray-500">
                Welcome, {getUserDisplayName()}
              </p>
              <Badge variant="outline" className="mt-2 capitalize">
                {userRole} Account
              </Badge>
            </div>
          </div>

      {/* Main Navigation */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className={`grid w-full max-w-4xl grid-cols-${Math.min(tabs.length, 4)}`}>
          {tabs.map((tab) => (
            <TabsTrigger key={tab.value} value={tab.value}>
              {tab.label}
            </TabsTrigger>
          ))}
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6 mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5 text-blue-500" />
                  Order Chat
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Secure, direct communication between all parties during active orders. Resolve issues faster and
                  improve delivery accuracy.
                </p>
              </CardContent>
              <CardFooter className="bg-muted/50 p-3 text-xs">
                <div className="flex items-start gap-2">
                  <CheckCircle2 className="h-4 w-4 text-green-500 mt-0.5" />
                  <p>Available during active orders only, for delivery-related communication</p>
                </div>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center gap-2">
                  <Star className="h-5 w-5 text-amber-500" />
                  Favorite Connections
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Save your preferred partners to build ongoing relationships. Continue communication even after
                  orders are completed.
                </p>
              </CardContent>
              <CardFooter className="bg-muted/50 p-3 text-xs">
                <div className="flex items-start gap-2">
                  <CheckCircle2 className="h-4 w-4 text-green-500 mt-0.5" />
                  <p>Add up to 20 favorite connections to your network</p>
                </div>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center gap-2">
                  <UserCircle className="h-5 w-5 text-purple-500" />
                  Profile Management
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Create your professional profile to share your information, preferences, and special requirements.
                  Build trust within the community.
                </p>
              </CardContent>
              <CardFooter className="bg-muted/50 p-3 text-xs">
                <div className="flex items-start gap-2">
                  <CheckCircle2 className="h-4 w-4 text-green-500 mt-0.5" />
                  <p>Customize your profile to highlight your strengths and preferences</p>
                </div>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-teal-500" />
                  Connection Requests
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Send and receive connection requests to establish trusted partnerships. Build a reliable network
                  within the Loop Jersey ecosystem.
                </p>
              </CardContent>
              <CardFooter className="bg-muted/50 p-3 text-xs">
                <div className="flex items-start gap-2">
                  <CheckCircle2 className="h-4 w-4 text-green-500 mt-0.5" />
                  <p>All connections are mutual and require acceptance from both parties</p>
                </div>
              </CardFooter>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>How It Works</CardTitle>
              <CardDescription>
                The Connections Hub creates a professional network within our delivery ecosystem
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                <div className="space-y-2">
                  <div className="bg-muted rounded-full w-12 h-12 flex items-center justify-center mx-auto">
                    <UserCircle className="h-6 w-6" />
                  </div>
                  <h3 className="font-medium">1. Create Your Profile</h3>
                  <p className="text-sm text-muted-foreground">
                    Set up your profile with your preferences and requirements
                  </p>
                </div>
                <div className="space-y-2">
                  <div className="bg-muted rounded-full w-12 h-12 flex items-center justify-center mx-auto">
                    <Users className="h-6 w-6" />
                  </div>
                  <h3 className="font-medium">2. Make Connections</h3>
                  <p className="text-sm text-muted-foreground">
                    Send connection requests to partners you work with regularly
                  </p>
                </div>
                <div className="space-y-2">
                  <div className="bg-muted rounded-full w-12 h-12 flex items-center justify-center mx-auto">
                    <MessageSquare className="h-6 w-6" />
                  </div>
                  <h3 className="font-medium">3. Communicate</h3>
                  <p className="text-sm text-muted-foreground">
                    Chat during orders and maintain relationships with your connections
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* For Businesses Tab */}
        {(userRole === "business" || userRole === "admin") && (
          <TabsContent value="for-businesses" className="space-y-6 mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Benefits for Your Business</CardTitle>
                <CardDescription>How the Connections Hub helps your business operations</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="border rounded-lg p-4 space-y-2">
                    <div className="flex items-center gap-2">
                      <Clock className="h-5 w-5 text-green-500" />
                      <h3 className="font-medium">Improved Timing</h3>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Coordinate with riders to ensure food is prepared just in time for pickup, improving food quality
                      and customer satisfaction.
                    </p>
                  </div>

                  <div className="border rounded-lg p-4 space-y-2">
                    <div className="flex items-center gap-2">
                      <Users className="h-5 w-5 text-purple-500" />
                      <h3 className="font-medium">Reliable Riders</h3>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Build relationships with trusted riders who understand your business and can represent your brand
                      well to customers.
                    </p>
                  </div>

                  <div className="border rounded-lg p-4 space-y-2">
                    <div className="flex items-center gap-2">
                      <MessageSquare className="h-5 w-5 text-blue-500" />
                      <h3 className="font-medium">Issue Resolution</h3>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Quickly resolve delivery issues through direct communication, reducing customer complaints and
                      refunds.
                    </p>
                  </div>

                  <div className="border rounded-lg p-4 space-y-2">
                    <div className="flex items-center gap-2">
                      <Star className="h-5 w-5 text-amber-500" />
                      <h3 className="font-medium">Rush Hour Support</h3>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Connect with riders who are available during your peak hours to ensure smooth operations during busy
                      periods.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}

        {/* For Riders Tab */}
        {(userRole === "rider" || userRole === "admin") && (
          <TabsContent value="for-riders" className="space-y-6 mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Benefits for Riders</CardTitle>
                <CardDescription>How the Connections Hub helps delivery riders</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="border rounded-lg p-4 space-y-2">
                    <div className="flex items-center gap-2">
                      <Building2 className="h-5 w-5 text-blue-500" />
                      <h3 className="font-medium">Preferred Businesses</h3>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Build relationships with businesses that value your service and offer consistent work opportunities.
                    </p>
                  </div>

                  <div className="border rounded-lg p-4 space-y-2">
                    <div className="flex items-center gap-2">
                      <Clock className="h-5 w-5 text-green-500" />
                      <h3 className="font-medium">Better Coordination</h3>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Communicate directly with businesses about pickup times and special requirements.
                    </p>
                  </div>

                  <div className="border rounded-lg p-4 space-y-2">
                    <div className="flex items-center gap-2">
                      <Star className="h-5 w-5 text-amber-500" />
                      <h3 className="font-medium">Higher Ratings</h3>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Better communication leads to smoother deliveries and higher customer satisfaction ratings.
                    </p>
                  </div>

                  <div className="border rounded-lg p-4 space-y-2">
                    <div className="flex items-center gap-2">
                      <MessageSquare className="h-5 w-5 text-blue-500" />
                      <h3 className="font-medium">Customer Communication</h3>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Communicate with customers about delivery updates and special instructions.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}

        {/* My Connections Tab - Functional UI */}
        <TabsContent value="connections" className="space-y-6 mt-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-2 mb-2">
              <Users className="h-5 w-5 text-blue-600" />
              <h3 className="font-semibold text-blue-900">Functional Connections Hub</h3>
            </div>
            <p className="text-sm text-blue-700">
              This is the working Connections Hub with real functionality. You can manage connections, search for users, and see how the channel-based role detection works.
            </p>
          </div>

          {/* Import and render our functional component */}
          <div className="min-h-screen">
            <ConnectionsHubComponent />
          </div>
        </TabsContent>

        {/* For Customers Tab */}
        {(userRole === "customer" || userRole === "admin") && (
          <TabsContent value="for-customers" className="space-y-6 mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Benefits for Customers</CardTitle>
                <CardDescription>How the Connections Hub improves your ordering experience</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="border rounded-lg p-4 space-y-2">
                    <div className="flex items-center gap-2">
                      <MessageSquare className="h-5 w-5 text-blue-500" />
                      <h3 className="font-medium">Direct Communication</h3>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Communicate directly with businesses about allergens, special requests, and order modifications.
                    </p>
                  </div>

                  <div className="border rounded-lg p-4 space-y-2">
                    <div className="flex items-center gap-2">
                      <Truck className="h-5 w-5 text-green-500" />
                      <h3 className="font-medium">Delivery Updates</h3>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Get real-time updates from your delivery rider about arrival times and any delays.
                    </p>
                  </div>

                  <div className="border rounded-lg p-4 space-y-2">
                    <div className="flex items-center gap-2">
                      <Star className="h-5 w-5 text-amber-500" />
                      <h3 className="font-medium">Favorite Partners</h3>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Save your favorite businesses and riders for faster ordering and better service.
                    </p>
                  </div>

                  <div className="border rounded-lg p-4 space-y-2">
                    <div className="flex items-center gap-2">
                      <ShieldCheck className="h-5 w-5 text-red-500" />
                      <h3 className="font-medium">Issue Resolution</h3>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Quickly resolve any issues with your order through direct communication channels.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}

        {/* My Connections Tab */}
        {userRole !== "admin" && (
          <TabsContent value="connections" className="space-y-6 mt-6">
            <Card>
              <CardHeader>
                <CardTitle>My Connections</CardTitle>
                <CardDescription>Manage your network of trusted partners</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  This section will show your active connections and allow you to manage them.
                </p>
                <Button className="bg-emerald-600 hover:bg-emerald-700">
                  Find New Connections
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        )}

        {/* Admin Tab */}
        {userRole === "admin" && (
          <TabsContent value="admin" className="space-y-6 mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Admin Dashboard</CardTitle>
                <CardDescription>Manage the Connections Hub system</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Administrative tools for managing connections, monitoring communications, and system settings.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button variant="outline">View All Connections</Button>
                  <Button variant="outline">Monitor Communications</Button>
                  <Button variant="outline">System Settings</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}

        {/* Getting Started Tab */}
        <TabsContent value="getting-started" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Getting Started</CardTitle>
              <CardDescription>Set up your profile and start making connections</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                This section will guide you through setting up your profile and making your first connections.
              </p>
              <div className="mt-4">
                <Button className="bg-emerald-600 hover:bg-emerald-700">
                  Set Up Your Profile
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        </Tabs>
        </div>
      </main>
    </div>
  )
}
