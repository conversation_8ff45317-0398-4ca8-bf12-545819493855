import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

// Create a Supabase client with the service role key
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function POST(request: Request) {
  try {
    console.log("Business registration API called");

    // Parse the request body
    let requestBody;
    try {
      requestBody = await request.json();
    } catch (parseError: any) {
      console.error("Error parsing request body:", parseError);
      return NextResponse.json(
        { error: `Invalid request format: ${parseError.message}` },
        { status: 400 }
      );
    }

    const {
      email,
      name,
      password, // Get the password for auth registration
      businessName,
      businessDescription = "",
      businessAddress = "",
      businessPostcode = "",
      businessPhone = "",
      businessTypeId = 1,
      deliveryFee = 2.50,
      minimumOrderAmount = 15.00,
      categories = [] // Get the selected categories
    } = requestBody;

    // Validate minimal required fields
    if (!email || !name || !businessName || !password) {
      return NextResponse.json(
        { error: "Missing required fields (email, name, password, businessName)" },
        { status: 400 }
      )
    }

    // Create a Supabase client with admin privileges
    if (!supabaseUrl || !supabaseServiceKey) {
      console.error("Missing Supabase URL or service role key");
      return NextResponse.json(
        { error: "Server configuration error" },
        { status: 500 }
      );
    }

    // Create client with explicit headers to bypass RLS
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      },
      global: {
        headers: {
          'X-Client-Info': 'business-registration-api',
        },
      },
    });

    console.log("Supabase admin client created");

    // Start a transaction by using a single batch operation
    try {
      // Step 1: Check if user exists or create a new user
      let userId;

      // First, check if the user exists in the auth system
      let authUser = null;
      try {
        const { data, error } = await supabase.auth.admin.getUserByEmail(email);
        if (error) {
          console.error("Error checking for existing auth user:", error);
        } else {
          authUser = data;
        }
      } catch (err) {
        console.error("Exception checking for existing auth user:", err);
        // Continue anyway, as we'll try to create the user
      }

      // Check if user exists in the public users table
      const { data: existingUser, error: getUserError } = await supabase
        .from("users")
        .select("id, role")
        .eq("email", email)
        .maybeSingle();

      if (getUserError) {
        console.error("Error checking for existing user:", getUserError);
        return NextResponse.json(
          { error: `Error checking user: ${getUserError.message}` },
          { status: 500 }
        );
      }

      if (existingUser) {
        console.log("User already exists in public users table:", existingUser.id);
        userId = existingUser.id;

        // Update user role to business_manager if they're currently a customer
        if (existingUser.role === 'customer') {
          const { error: updateRoleError } = await supabase
            .from("users")
            .update({
              role: "business_manager",
              updated_at: new Date().toISOString()
            })
            .eq("id", userId);

          if (updateRoleError) {
            console.error("Error updating user role:", updateRoleError);
            // Continue anyway, this is not critical
          } else {
            console.log("Updated user role to business_manager");
          }
        }

        // If user exists in public users table but not in auth, create auth user
        if (!authUser) {
          console.log("User exists in public users table but not in auth, creating auth user");

          // Create user in auth system
          try {
            const { data: newAuthUser, error: createAuthError } = await supabase.auth.admin.createUser({
              email,
              password,
              email_confirm: true, // Auto-confirm email
              user_metadata: {
                name,
                role: "business_manager"
              }
            });

            if (createAuthError) {
              console.error("Error creating auth user:", createAuthError);
              // Continue anyway, as the user exists in the public users table
            } else {
              console.log("Created auth user for existing public user");
            }
          } catch (err) {
            console.error("Exception creating auth user:", err);
            // Continue anyway, as the user exists in the public users table
          }
        }
      } else {
        // User doesn't exist in either system, create in both

        // 1. Create user in auth system first
        console.log("Creating new user in auth system");
        try {
          const { data: newAuthUser, error: createAuthError } = await supabase.auth.admin.createUser({
            email,
            password,
            email_confirm: true, // Auto-confirm email
            user_metadata: {
              name,
              role: "business_manager"
            }
          });

          if (createAuthError) {
            console.error("Error creating auth user:", createAuthError);
            // We'll continue and create the user in the public table anyway
            // This will allow the admin to approve the business, and the user
            // can be properly set up in auth later if needed
          } else {
            console.log("Created auth user:", newAuthUser?.user?.id);
          }
        } catch (err) {
          console.error("Exception creating auth user:", err);
          // Continue anyway and create the user in the public table
        }

        // 2. Create user in public users table
        const { data: newUser, error: createUserError } = await supabase
          .from("users")
          .insert({
            email,
            name,
            role: "business_manager", // Set role as business_manager directly
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select();

        if (createUserError) {
          console.error("Error creating user in public table:", createUserError);
          return NextResponse.json(
            { error: `Failed to create user in public table: ${createUserError.message}` },
            { status: 500 }
          );
        }

        userId = newUser[0].id;
        console.log("Created new user in public table with ID:", userId);
      }

      // Step 2: Create a business record with is_approved set to null (pending)
      // Generate a slug from the business name
      const slug = businessName
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');

      const { data: business, error: businessError } = await supabase
        .from("businesses")
        .insert({
          name: businessName,
          slug: slug,
          description: businessDescription,
          address: businessAddress,
          postcode: businessPostcode,
          phone: businessPhone,
          business_type_id: businessTypeId,
          delivery_fee: deliveryFee,
          minimum_order_amount: minimumOrderAmount,
          is_approved: null, // Pending approval
          location: "Jersey", // Default location
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select();

      if (businessError) {
        console.error("Error creating business:", businessError);
        return NextResponse.json(
          { error: `Failed to create business: ${businessError.message}` },
          { status: 500 }
        );
      }

      const businessId = business[0].id;
      console.log("Created business with ID:", businessId);

      // Step 3: Create a business_managers record to link the user to the business
      const { error: managerError } = await supabase
        .from("business_managers")
        .insert({
          user_id: userId,
          business_id: businessId,
          is_primary: true,
          created_at: new Date().toISOString()
        });

      if (managerError) {
        console.error("Error creating business manager link:", managerError);
        // This is not critical, so we'll continue even if it fails
        // But we should log it for debugging
      } else {
        console.log("Created business manager link for user", userId, "and business", businessId);
      }

      // Step 4: Assign categories to the business
      try {
        // Check if user selected any categories
        if (categories && categories.length > 0) {
          console.log("User selected categories:", categories);

          // Create category records for each selected category
          const categoryRecords = categories.map((categoryId: number, index: number) => ({
            business_id: businessId,
            category_id: categoryId,
            is_primary: index === 0, // First one is primary
            created_at: new Date().toISOString()
          }));

          const { error: categoriesError } = await supabase
            .from("business_categories")
            .insert(categoryRecords);

          if (categoriesError) {
            console.error("Error assigning user-selected categories:", categoriesError);
            // This is not critical, so we'll continue even if it fails
          } else {
            console.log("Assigned user-selected categories to business", businessId);
          }
        } else {
          // No categories selected, assign a default category based on business type
          console.log("No categories selected, assigning default category");

          // Map business types to default categories
          const businessTypeToCategory: Record<number, number> = {
            1: 7,  // Restaurant -> Restaurant Food
            2: 9,  // Shop -> Grocery
            3: 11, // Pharmacy -> Pharmacy Items
            4: 8,  // Cafe -> Cafe Items
            5: 3   // Errand -> Household (fallback)
          };

          const defaultCategoryId = businessTypeToCategory[businessTypeId] || 1;

          // Create a business_categories record
          const { error: categoryError } = await supabase
            .from("business_categories")
            .insert({
              business_id: businessId,
              category_id: defaultCategoryId,
              is_primary: true,
              created_at: new Date().toISOString()
            });

          if (categoryError) {
            console.error("Error assigning default category:", categoryError);
            // This is not critical, so we'll continue even if it fails
          } else {
            console.log("Assigned default category", defaultCategoryId, "to business", businessId);
          }
        }
      } catch (categoryErr) {
        console.error("Error in category assignment:", categoryErr);
        // Continue anyway as this is not critical
      }

      // Return success with the business ID
      return NextResponse.json({
        success: true,
        registrationId: businessId,
        message: "Business registration submitted successfully"
      });

    } catch (dbError: any) {
      console.error("Database operation error:", dbError);
      return NextResponse.json(
        { error: `Database error: ${dbError.message}` },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error("Unexpected error in business registration:", error);
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
