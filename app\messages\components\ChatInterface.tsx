"use client"

import { useState, useEffect, useRef, useCallback } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { cn } from "@/lib/utils"
import {
  Send,
  Paperclip,
  Smile,
  MoreVertical,
  Phone,
  Video,
  Info,
  Mic,
  MicOff,
  Play,
  Pause,
  Square
} from "lucide-react"
import { useRealtimeMessages, RealtimeMessage } from '@/hooks/useRealtimeMessages'
import { addAuthHeaders } from '@/utils/auth-token'

interface User {
  id: string
  email?: string
  user_metadata?: any
}

interface Message {
  id: string
  sender_id: string
  content: string
  timestamp: string
  is_own_message: boolean
  sender_name?: string
  message_type?: string
  audio_data?: string
}

interface ChatInterfaceProps {
  user: User
  threadId: string
  contactName: string
  contactType: 'business' | 'rider' | 'customer'
  channelType: string
  onBack: () => void
}

export function ChatInterface({
  user,
  threadId,
  contactName,
  contactType,
  channelType,
  onBack
}: ChatInterfaceProps) {
  const [messages, setMessages] = useState<Message[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [isSending, setIsSending] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Voice recording state
  const [isRecording, setIsRecording] = useState(false)
  const [isListening, setIsListening] = useState(false)
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null)
  const [audioChunks, setAudioChunks] = useState<Blob[]>([])
  const [recordingTime, setRecordingTime] = useState(0)
  const recordingIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const recognitionRef = useRef<any>(null)

  // Handle new messages from real-time subscription
  const handleNewMessage = useCallback((message: RealtimeMessage) => {
    console.log('New message in chat:', message)

    const newMsg: Message = {
      id: message.id,
      sender_id: message.sender_id,
      content: message.content,
      timestamp: message.created_at,
      is_own_message: message.sender_id === user.id,
      sender_name: message.sender_id === user.id ? 'You' : contactName,
      message_type: message.message_type,
      audio_data: message.audio_data
    }

    setMessages(prev => [...prev, newMsg])
  }, [user.id, contactName])

  // Handle message updates from real-time subscription
  const handleMessageUpdate = useCallback((message: RealtimeMessage) => {
    console.log('Message updated in chat:', message)

    setMessages(prev => prev.map(msg =>
      msg.id === message.id
        ? {
            ...msg,
            content: message.content,
            timestamp: message.updated_at || message.created_at
          }
        : msg
    ))
  }, [])

  // Set up real-time messaging for this thread
  const { isConnected, sendMessage, markAsRead } = useRealtimeMessages({
    userId: user.id,
    threadId,
    onNewMessage: handleNewMessage,
    onMessageUpdate: handleMessageUpdate
  })

  useEffect(() => {
    loadMessages()
  }, [threadId])

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const loadMessages = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/connections-hub/threads/${threadId}`, {
        headers: addAuthHeaders({
          'Content-Type': 'application/json',
        })
      })

      if (!response.ok) {
        throw new Error(`Failed to load messages: ${response.status}`)
      }

      const data = await response.json()
      const threadMessages = data.messages || []

      // Transform messages to component format
      const transformedMessages: Message[] = threadMessages.map((msg: any) => ({
        id: msg.id,
        sender_id: msg.sender_id,
        content: msg.content,
        timestamp: msg.created_at,
        is_own_message: msg.sender_id === user.id,
        sender_name: msg.sender_id === user.id ? 'You' : contactName,
        message_type: msg.message_type,
        audio_data: msg.audio_data
      }))

      setMessages(transformedMessages)

      // Mark unread messages as read
      const unreadMessages = threadMessages.filter((msg: any) =>
        !msg.is_read && msg.sender_id !== user.id
      )

      for (const msg of unreadMessages) {
        await markAsRead(msg.id)
      }

    } catch (error) {
      console.error('Error loading messages:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const handleSendMessage = async () => {
    if (!newMessage.trim() || isSending) return

    setIsSending(true)
    const messageContent = newMessage.trim()
    setNewMessage('')

    try {
      // Find recipient ID (the other user in the conversation)
      const recipientId = messages.length > 0
        ? messages.find(msg => msg.sender_id !== user.id)?.sender_id ||
          messages.find(msg => !msg.is_own_message)?.sender_id
        : null

      if (!recipientId) {
        throw new Error('Could not determine recipient')
      }

      await sendMessage({
        recipient_id: recipientId,
        content: messageContent,
        channel_type: channelType,
        message_type: 'chat',
        thread_id: threadId
      })

      // Message will be added via real-time subscription
    } catch (error) {
      console.error('Error sending message:', error)
      // Restore the message input on error
      setNewMessage(messageContent)
    } finally {
      setIsSending(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const formatTime = (timestamp: string): string => {
    const date = new Date(timestamp)
    return date.toLocaleTimeString('en-GB', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Voice recording functions
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      const recorder = new MediaRecorder(stream)

      setMediaRecorder(recorder)
      setAudioChunks([])
      setIsRecording(true)
      setRecordingTime(0)

      // Start recording timer
      recordingIntervalRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1)
      }, 1000)

      recorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          setAudioChunks(prev => [...prev, event.data])
        }
      }

      recorder.onstop = () => {
        stream.getTracks().forEach(track => track.stop())
        if (recordingIntervalRef.current) {
          clearInterval(recordingIntervalRef.current)
        }
      }

      recorder.start()
    } catch (error) {
      console.error('Error starting recording:', error)
      alert('Could not access microphone. Please check permissions.')
    }
  }

  const stopRecording = () => {
    if (mediaRecorder && isRecording) {
      mediaRecorder.stop()
      setIsRecording(false)
      setRecordingTime(0)
    }
  }

  const sendVoiceMessage = async () => {
    if (audioChunks.length === 0) return

    try {
      const audioBlob = new Blob(audioChunks, { type: 'audio/webm' })

      // Convert to base64 for sending
      const reader = new FileReader()
      reader.onloadend = async () => {
        const base64Audio = reader.result as string

        // Find recipient ID
        const recipientId = messages.length > 0
          ? messages.find(msg => msg.sender_id !== user.id)?.sender_id ||
            messages.find(msg => !msg.is_own_message)?.sender_id
          : null

        if (!recipientId) {
          throw new Error('Could not determine recipient')
        }

        await sendMessage({
          recipient_id: recipientId,
          content: '[Voice Message]',
          channel_type: channelType,
          message_type: 'voice',
          thread_id: threadId,
          audio_data: base64Audio
        })

        setAudioChunks([])
      }

      reader.readAsDataURL(audioBlob)
    } catch (error) {
      console.error('Error sending voice message:', error)
    }
  }

  // Voice-to-text functions
  const startListening = () => {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      alert('Speech recognition not supported in this browser')
      return
    }

    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition
    const recognition = new SpeechRecognition()

    recognition.continuous = true
    recognition.interimResults = true
    recognition.lang = 'en-US'

    recognition.onstart = () => {
      setIsListening(true)
    }

    recognition.onresult = (event: any) => {
      let finalTranscript = ''

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const transcript = event.results[i][0].transcript
        if (event.results[i].isFinal) {
          finalTranscript += transcript
        }
      }

      if (finalTranscript) {
        setNewMessage(prev => prev + finalTranscript + ' ')
      }
    }

    recognition.onerror = (event: any) => {
      console.error('Speech recognition error:', event.error)
      setIsListening(false)
    }

    recognition.onend = () => {
      setIsListening(false)
    }

    recognitionRef.current = recognition
    recognition.start()
  }

  const stopListening = () => {
    if (recognitionRef.current) {
      recognitionRef.current.stop()
      setIsListening(false)
    }
  }

  // Audio playback component
  const AudioMessage = ({ audioData, isOwnMessage }: { audioData: string, isOwnMessage: boolean }) => {
    const [isPlaying, setIsPlaying] = useState(false)
    const [duration, setDuration] = useState(0)
    const [currentTime, setCurrentTime] = useState(0)
    const audioRef = useRef<HTMLAudioElement>(null)

    useEffect(() => {
      const audio = audioRef.current
      if (!audio) return

      const updateTime = () => setCurrentTime(audio.currentTime)
      const updateDuration = () => setDuration(audio.duration)
      const handleEnded = () => setIsPlaying(false)

      audio.addEventListener('timeupdate', updateTime)
      audio.addEventListener('loadedmetadata', updateDuration)
      audio.addEventListener('ended', handleEnded)

      return () => {
        audio.removeEventListener('timeupdate', updateTime)
        audio.removeEventListener('loadedmetadata', updateDuration)
        audio.removeEventListener('ended', handleEnded)
      }
    }, [])

    const togglePlayback = () => {
      const audio = audioRef.current
      if (!audio) return

      if (isPlaying) {
        audio.pause()
        setIsPlaying(false)
      } else {
        audio.play()
        setIsPlaying(true)
      }
    }

    const formatDuration = (seconds: number) => {
      const mins = Math.floor(seconds / 60)
      const secs = Math.floor(seconds % 60)
      return `${mins}:${secs.toString().padStart(2, '0')}`
    }

    return (
      <div className={cn(
        "flex items-center space-x-3 p-3 rounded-lg max-w-xs",
        isOwnMessage ? "bg-emerald-600 text-white" : "bg-gray-100"
      )}>
        <audio ref={audioRef} src={audioData} preload="metadata" />

        <Button
          variant="ghost"
          size="sm"
          onClick={togglePlayback}
          className={cn(
            "h-8 w-8 p-0 rounded-full",
            isOwnMessage
              ? "text-white hover:bg-emerald-700"
              : "text-gray-700 hover:bg-gray-200"
          )}
        >
          {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
        </Button>

        <div className="flex-1 min-w-0">
          <div className={cn(
            "h-1 rounded-full",
            isOwnMessage ? "bg-emerald-700" : "bg-gray-300"
          )}>
            <div
              className={cn(
                "h-full rounded-full transition-all",
                isOwnMessage ? "bg-white" : "bg-emerald-600"
              )}
              style={{ width: `${duration ? (currentTime / duration) * 100 : 0}%` }}
            />
          </div>
          <div className={cn(
            "text-xs mt-1",
            isOwnMessage ? "text-emerald-100" : "text-gray-500"
          )}>
            {formatDuration(currentTime)} / {formatDuration(duration)}
          </div>
        </div>
      </div>
    )
  }

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (recordingIntervalRef.current) {
        clearInterval(recordingIntervalRef.current)
      }
      if (recognitionRef.current) {
        recognitionRef.current.stop()
      }
    }
  }, [])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[50vh]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading conversation...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-screen bg-white">
      {/* Chat Header */}
      <div className="sticky top-0 z-50 bg-white border-b px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Avatar className="h-10 w-10">
              <AvatarFallback className="bg-green-100 text-green-700">
                PP
              </AvatarFallback>
            </Avatar>
            <div>
              <h2 className="font-medium text-gray-900">Pizza Palace</h2>
              <div className="flex items-center space-x-2">
                <Badge variant="secondary" className="text-xs bg-green-100 text-green-800">
                  Customer Enquiries
                </Badge>
                <span className="text-xs text-gray-500">Online</span>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm" className="h-9 w-9 p-0">
              <Phone className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" className="h-9 w-9 p-0">
              <Video className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" className="h-9 w-9 p-0">
              <MoreVertical className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto px-4 py-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={cn(
              "flex",
              message.is_own_message ? "justify-end" : "justify-start"
            )}
          >
            <div className="max-w-[80%]">
              {message.message_type === 'voice' && message.audio_data ? (
                <div>
                  <AudioMessage
                    audioData={message.audio_data}
                    isOwnMessage={message.is_own_message}
                  />
                  <p
                    className={cn(
                      "text-xs mt-1",
                      message.is_own_message ? "text-right text-gray-400" : "text-gray-500"
                    )}
                  >
                    {formatTime(message.timestamp)}
                  </p>
                </div>
              ) : (
                <div
                  className={cn(
                    "rounded-lg px-4 py-2",
                    message.is_own_message
                      ? "bg-emerald-600 text-white"
                      : "bg-gray-100 text-gray-900"
                  )}
                >
                  <p className="text-sm">{message.content}</p>
                  <p
                    className={cn(
                      "text-xs mt-1",
                      message.is_own_message
                        ? "text-emerald-100"
                        : "text-gray-500"
                    )}
                  >
                    {formatTime(message.timestamp)}
                  </p>
                </div>
              )}
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="sticky bottom-0 bg-white border-t px-4 py-3">
        {/* Voice Recording Indicator */}
        {isRecording && (
          <div className="mb-3 p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-red-700">Recording...</span>
                <span className="text-sm text-red-600">{Math.floor(recordingTime / 60)}:{(recordingTime % 60).toString().padStart(2, '0')}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={stopRecording}
                  className="text-red-600 border-red-300 hover:bg-red-50"
                >
                  <Square className="h-4 w-4 mr-1" />
                  Stop
                </Button>
                <Button
                  size="sm"
                  onClick={sendVoiceMessage}
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  <Send className="h-4 w-4 mr-1" />
                  Send
                </Button>
              </div>
            </div>
          </div>
        )}

        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm" className="h-9 w-9 p-0 flex-shrink-0">
            <Paperclip className="h-4 w-4" />
          </Button>

          <div className="flex-1 relative">
            <Input
              placeholder={isListening ? "Listening..." : "Type your message..."}
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              className={cn(
                "pr-20",
                isListening && "border-blue-300 bg-blue-50"
              )}
              disabled={isRecording}
            />

            {/* Voice-to-text button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={isListening ? stopListening : startListening}
              className={cn(
                "absolute right-8 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0",
                isListening && "text-blue-600 bg-blue-100"
              )}
              disabled={isRecording}
            >
              {isListening ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
            </Button>

            <Button
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0"
              disabled={isRecording}
            >
              <Smile className="h-4 w-4" />
            </Button>
          </div>

          {/* Voice recording button */}
          <Button
            size="sm"
            onClick={isRecording ? stopRecording : startRecording}
            className={cn(
              "h-9 w-9 p-0 flex-shrink-0",
              isRecording
                ? "bg-red-600 hover:bg-red-700"
                : "bg-blue-600 hover:bg-blue-700"
            )}
            disabled={isListening}
          >
            {isRecording ? <Square className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
          </Button>

          <Button
            size="sm"
            onClick={handleSendMessage}
            disabled={!newMessage.trim() || isSending || isRecording}
            className="bg-emerald-600 hover:bg-emerald-700 h-9 w-9 p-0 flex-shrink-0"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>

        {/* Role indicator */}
        <div className="flex items-center justify-between mt-2">
          <div className="flex items-center space-x-2">
            <span className="text-xs text-gray-500">Messaging as:</span>
            <Badge variant="outline" className="text-xs">
              Customer
            </Badge>
          </div>
          <span className="text-xs text-gray-400">
            {isSending ? 'Sending...' : 'Press Enter to send'}
          </span>
        </div>
      </div>
    </div>
  )
}
