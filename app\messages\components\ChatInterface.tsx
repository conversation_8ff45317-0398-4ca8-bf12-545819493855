"use client"

import { useState, useEffect, useRef, useCallback } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Send,
  Paperclip,
  Smile,
  MoreVertical,
  Phone,
  Video,
  Info
} from "lucide-react"
import { cn } from "@/lib/utils"
import { useRealtimeMessages, RealtimeMessage } from '@/hooks/useRealtimeMessages'
import { addAuthHeaders } from '@/utils/auth-token'

interface User {
  id: string
  email?: string
  user_metadata?: any
}

interface Message {
  id: string
  sender_id: string
  content: string
  timestamp: string
  is_own_message: boolean
  sender_name?: string
  message_type?: string
}

interface ChatInterfaceProps {
  user: User
  threadId: string
  contactName: string
  contactType: 'business' | 'rider' | 'customer'
  channelType: string
  onBack: () => void
}

export function ChatInterface({
  user,
  threadId,
  contactName,
  contactType,
  channelType,
  onBack
}: ChatInterfaceProps) {
  const [messages, setMessages] = useState<Message[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [isSending, setIsSending] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Handle new messages from real-time subscription
  const handleNewMessage = useCallback((message: RealtimeMessage) => {
    console.log('New message in chat:', message)

    const newMsg: Message = {
      id: message.id,
      sender_id: message.sender_id,
      content: message.content,
      timestamp: message.created_at,
      is_own_message: message.sender_id === user.id,
      sender_name: message.sender_id === user.id ? 'You' : contactName,
      message_type: message.message_type
    }

    setMessages(prev => [...prev, newMsg])
  }, [user.id, contactName])

  // Handle message updates from real-time subscription
  const handleMessageUpdate = useCallback((message: RealtimeMessage) => {
    console.log('Message updated in chat:', message)

    setMessages(prev => prev.map(msg =>
      msg.id === message.id
        ? {
            ...msg,
            content: message.content,
            timestamp: message.updated_at || message.created_at
          }
        : msg
    ))
  }, [])

  // Set up real-time messaging for this thread
  const { isConnected, sendMessage, markAsRead } = useRealtimeMessages({
    userId: user.id,
    threadId,
    onNewMessage: handleNewMessage,
    onMessageUpdate: handleMessageUpdate
  })

  useEffect(() => {
    loadMessages()
  }, [threadId])

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const loadMessages = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/connections-hub/threads/${threadId}`, {
        headers: addAuthHeaders({
          'Content-Type': 'application/json',
        })
      })

      if (!response.ok) {
        throw new Error(`Failed to load messages: ${response.status}`)
      }

      const data = await response.json()
      const threadMessages = data.messages || []

      // Transform messages to component format
      const transformedMessages: Message[] = threadMessages.map((msg: any) => ({
        id: msg.id,
        sender_id: msg.sender_id,
        content: msg.content,
        timestamp: msg.created_at,
        is_own_message: msg.sender_id === user.id,
        sender_name: msg.sender_id === user.id ? 'You' : contactName,
        message_type: msg.message_type
      }))

      setMessages(transformedMessages)

      // Mark unread messages as read
      const unreadMessages = threadMessages.filter((msg: any) =>
        !msg.is_read && msg.sender_id !== user.id
      )

      for (const msg of unreadMessages) {
        await markAsRead(msg.id)
      }

    } catch (error) {
      console.error('Error loading messages:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const handleSendMessage = async () => {
    if (!newMessage.trim() || isSending) return

    setIsSending(true)
    const messageContent = newMessage.trim()
    setNewMessage('')

    try {
      // Find recipient ID (the other user in the conversation)
      const recipientId = messages.length > 0
        ? messages.find(msg => msg.sender_id !== user.id)?.sender_id ||
          messages.find(msg => !msg.is_own_message)?.sender_id
        : null

      if (!recipientId) {
        throw new Error('Could not determine recipient')
      }

      await sendMessage({
        recipient_id: recipientId,
        content: messageContent,
        channel_type: channelType,
        message_type: 'chat',
        thread_id: threadId
      })

      // Message will be added via real-time subscription
    } catch (error) {
      console.error('Error sending message:', error)
      // Restore the message input on error
      setNewMessage(messageContent)
    } finally {
      setIsSending(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const formatTime = (timestamp: string): string => {
    const date = new Date(timestamp)
    return date.toLocaleTimeString('en-GB', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[50vh]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading conversation...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-screen bg-white">
      {/* Chat Header */}
      <div className="sticky top-0 z-50 bg-white border-b px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Avatar className="h-10 w-10">
              <AvatarFallback className="bg-green-100 text-green-700">
                PP
              </AvatarFallback>
            </Avatar>
            <div>
              <h2 className="font-medium text-gray-900">Pizza Palace</h2>
              <div className="flex items-center space-x-2">
                <Badge variant="secondary" className="text-xs bg-green-100 text-green-800">
                  Customer Enquiries
                </Badge>
                <span className="text-xs text-gray-500">Online</span>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm" className="h-9 w-9 p-0">
              <Phone className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" className="h-9 w-9 p-0">
              <Video className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" className="h-9 w-9 p-0">
              <MoreVertical className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto px-4 py-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={cn(
              "flex",
              message.is_own_message ? "justify-end" : "justify-start"
            )}
          >
            <div
              className={cn(
                "max-w-[80%] rounded-lg px-4 py-2",
                message.is_own_message
                  ? "bg-emerald-600 text-white"
                  : "bg-gray-100 text-gray-900"
              )}
            >
              <p className="text-sm">{message.content}</p>
              <p
                className={cn(
                  "text-xs mt-1",
                  message.is_own_message
                    ? "text-emerald-100"
                    : "text-gray-500"
                )}
              >
                {formatTime(message.timestamp)}
              </p>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="sticky bottom-0 bg-white border-t px-4 py-3">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm" className="h-9 w-9 p-0 flex-shrink-0">
            <Paperclip className="h-4 w-4" />
          </Button>

          <div className="flex-1 relative">
            <Input
              placeholder="Type your message..."
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              className="pr-10"
            />
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0"
            >
              <Smile className="h-4 w-4" />
            </Button>
          </div>

          <Button
            size="sm"
            onClick={handleSendMessage}
            disabled={!newMessage.trim() || isSending}
            className="bg-emerald-600 hover:bg-emerald-700 h-9 w-9 p-0 flex-shrink-0"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>

        {/* Role indicator */}
        <div className="flex items-center justify-between mt-2">
          <div className="flex items-center space-x-2">
            <span className="text-xs text-gray-500">Messaging as:</span>
            <Badge variant="outline" className="text-xs">
              Customer
            </Badge>
          </div>
          <span className="text-xs text-gray-400">
            {isSending ? 'Sending...' : 'Press Enter to send'}
          </span>
        </div>
      </div>
    </div>
  )
}
