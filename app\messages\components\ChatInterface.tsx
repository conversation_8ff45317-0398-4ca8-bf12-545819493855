"use client"

import { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  Send, 
  Paperclip, 
  Smile,
  MoreVertical,
  Phone,
  Video,
  Info
} from "lucide-react"
import { cn } from "@/lib/utils"

interface User {
  id: string
  email?: string
  user_metadata?: any
}

interface Message {
  id: string
  sender_id: string
  content: string
  timestamp: string
  is_own_message: boolean
  sender_name?: string
  message_type?: string
}

interface ChatInterfaceProps {
  user: User
  conversationId: string
  onBack: () => void
}

export function ChatInterface({ user, conversationId, onBack }: ChatInterfaceProps) {
  const [messages, setMessages] = useState<Message[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [isSending, setIsSending] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    loadMessages()
  }, [conversationId])

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const loadMessages = async () => {
    setIsLoading(true)
    try {
      // Simulate API call
      const mockMessages: Message[] = [
        {
          id: 'msg-1',
          sender_id: user.id,
          content: 'Hi! Can I get extra cheese on my margherita pizza? I have a dairy allergy so please use the vegan cheese.',
          timestamp: '2024-02-15T12:30:00Z',
          is_own_message: true,
          sender_name: 'You'
        },
        {
          id: 'msg-2',
          sender_id: 'other-user',
          content: 'Of course! We have excellent vegan cheese. That will be £2 extra. Your pizza will be ready in 15 minutes.',
          timestamp: '2024-02-15T12:32:00Z',
          is_own_message: false,
          sender_name: 'Pizza Palace'
        },
        {
          id: 'msg-3',
          sender_id: user.id,
          content: 'Perfect! Thank you so much. What time should I expect delivery?',
          timestamp: '2024-02-15T12:33:00Z',
          is_own_message: true,
          sender_name: 'You'
        }
      ]

      setMessages(mockMessages)
    } catch (error) {
      console.error('Error loading messages:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const handleSendMessage = async () => {
    if (!newMessage.trim() || isSending) return

    setIsSending(true)
    const messageContent = newMessage.trim()
    setNewMessage('')

    try {
      // Optimistically add message
      const tempMessage: Message = {
        id: 'temp-' + Date.now(),
        sender_id: user.id,
        content: messageContent,
        timestamp: new Date().toISOString(),
        is_own_message: true,
        sender_name: 'You'
      }

      setMessages(prev => [...prev, tempMessage])

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Replace temp message with real one
      setMessages(prev => 
        prev.map(msg => 
          msg.id === tempMessage.id 
            ? { ...msg, id: 'msg-' + Date.now() }
            : msg
        )
      )
    } catch (error) {
      console.error('Error sending message:', error)
      // Remove failed message
      setMessages(prev => prev.filter(msg => msg.id !== 'temp-' + Date.now()))
    } finally {
      setIsSending(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const formatTime = (timestamp: string): string => {
    const date = new Date(timestamp)
    return date.toLocaleTimeString('en-GB', { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[50vh]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading conversation...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-screen bg-white">
      {/* Chat Header */}
      <div className="sticky top-0 z-50 bg-white border-b px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Avatar className="h-10 w-10">
              <AvatarFallback className="bg-green-100 text-green-700">
                PP
              </AvatarFallback>
            </Avatar>
            <div>
              <h2 className="font-medium text-gray-900">Pizza Palace</h2>
              <div className="flex items-center space-x-2">
                <Badge variant="secondary" className="text-xs bg-green-100 text-green-800">
                  Customer Enquiries
                </Badge>
                <span className="text-xs text-gray-500">Online</span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm" className="h-9 w-9 p-0">
              <Phone className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" className="h-9 w-9 p-0">
              <Video className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" className="h-9 w-9 p-0">
              <MoreVertical className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto px-4 py-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={cn(
              "flex",
              message.is_own_message ? "justify-end" : "justify-start"
            )}
          >
            <div
              className={cn(
                "max-w-[80%] rounded-lg px-4 py-2",
                message.is_own_message
                  ? "bg-emerald-600 text-white"
                  : "bg-gray-100 text-gray-900"
              )}
            >
              <p className="text-sm">{message.content}</p>
              <p
                className={cn(
                  "text-xs mt-1",
                  message.is_own_message
                    ? "text-emerald-100"
                    : "text-gray-500"
                )}
              >
                {formatTime(message.timestamp)}
              </p>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="sticky bottom-0 bg-white border-t px-4 py-3">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm" className="h-9 w-9 p-0 flex-shrink-0">
            <Paperclip className="h-4 w-4" />
          </Button>
          
          <div className="flex-1 relative">
            <Input
              placeholder="Type your message..."
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              className="pr-10"
            />
            <Button 
              variant="ghost" 
              size="sm" 
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0"
            >
              <Smile className="h-4 w-4" />
            </Button>
          </div>
          
          <Button 
            size="sm" 
            onClick={handleSendMessage}
            disabled={!newMessage.trim() || isSending}
            className="bg-emerald-600 hover:bg-emerald-700 h-9 w-9 p-0 flex-shrink-0"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
        
        {/* Role indicator */}
        <div className="flex items-center justify-between mt-2">
          <div className="flex items-center space-x-2">
            <span className="text-xs text-gray-500">Messaging as:</span>
            <Badge variant="outline" className="text-xs">
              Customer
            </Badge>
          </div>
          <span className="text-xs text-gray-400">
            {isSending ? 'Sending...' : 'Press Enter to send'}
          </span>
        </div>
      </div>
    </div>
  )
}
