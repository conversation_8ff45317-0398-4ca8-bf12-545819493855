import { NextResponse } from 'next/server';
import { adminClient } from '@/lib/supabase-admin';
import { geocodeAddress } from '@/lib/geocoding';
import { formatPointString } from '@/lib/address-utils';

/**
 * API endpoint to update coordinates for all user addresses
 * This is an admin-only endpoint that should be protected
 */
export async function POST(request: Request) {
  try {
    // Get all user addresses that don't have coordinates
    const { data: addresses, error: fetchError } = await adminClient
      .from('user_addresses')
      .select('id, auth_user_id, address_line1, address_line2, parish, postcode, coordinates, latitude, longitude')
      .or('coordinates.is.null,latitude.is.null,longitude.is.null');

    if (fetchError) {
      console.error('Error fetching addresses:', fetchError);
      return NextResponse.json(
        { error: 'Failed to fetch addresses', details: fetchError.message },
        { status: 500 }
      );
    }

    if (!addresses || addresses.length === 0) {
      return NextResponse.json({
        message: 'No addresses found that need coordinate updates',
        updated: 0,
        total: 0
      });
    }

    console.log(`Found ${addresses.length} addresses that need coordinate updates`);

    // Process each address
    const results = {
      success: 0,
      failed: 0,
      total: addresses.length,
      failures: [] as any[]
    };

    for (const address of addresses) {
      try {
        // Skip addresses without address_line1 or postcode
        if (!address.address_line1 && !address.postcode) {
          console.log(`Skipping address ${address.id} due to missing address_line1 and postcode`);
          results.failed++;
          results.failures.push({
            id: address.id,
            reason: 'Missing address_line1 and postcode'
          });
          continue;
        }

        // Construct the full address for geocoding
        const addressComponents = [
          address.address_line1,
          address.address_line2,
          `${address.parish}, Jersey`,
          address.postcode
        ].filter(Boolean);

        const fullAddress = addressComponents.join(", ");
        console.log(`Geocoding address for ID ${address.id}: "${fullAddress}"`);

        // Geocode the address
        const coordinates = await geocodeAddress(fullAddress);

        if (!coordinates) {
          console.error(`Could not geocode address: ${fullAddress}`);
          results.failed++;
          results.failures.push({
            id: address.id,
            reason: 'Geocoding failed'
          });
          continue;
        }

        console.log(`Successfully geocoded address to coordinates: [${coordinates[0]}, ${coordinates[1]}]`);

        // Format coordinates as PostgreSQL POINT type
        const pointString = formatPointString(coordinates[0], coordinates[1]);

        // Update the address record
        const { error: updateError } = await adminClient
          .from('user_addresses')
          .update({
            coordinates: pointString,
            latitude: coordinates[1],
            longitude: coordinates[0],
            coordinates_updated_at: new Date().toISOString()
          })
          .eq('id', address.id);

        if (updateError) {
          console.error(`Error updating address ${address.id}:`, updateError);
          results.failed++;
          results.failures.push({
            id: address.id,
            reason: updateError.message
          });
          continue;
        }

        console.log(`Successfully updated coordinates for address ${address.id}`);
        results.success++;
      } catch (error: any) {
        console.error(`Error processing address ${address.id}:`, error);
        results.failed++;
        results.failures.push({
          id: address.id,
          reason: error.message || 'Unknown error'
        });
      }

      // Add a small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    return NextResponse.json({
      message: `Processed ${results.total} addresses: ${results.success} updated, ${results.failed} failed`,
      ...results
    });
  } catch (error: any) {
    console.error('Error in update-all-coordinates endpoint:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}
