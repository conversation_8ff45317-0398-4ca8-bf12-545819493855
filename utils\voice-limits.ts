// Voice message size and duration limits
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

// Voice message limits configuration
export const VOICE_LIMITS = {
  // Per-message limits
  MAX_MESSAGE_SIZE_MB: 5,           // 5MB per voice message
  MAX_MESSAGE_DURATION_SECONDS: 300, // 5 minutes per message
  MIN_MESSAGE_DURATION_SECONDS: 1,   // 1 second minimum
  
  // Per-conversation limits (24 hours)
  MAX_CONVERSATION_SIZE_MB_24H: 50,   // 50MB per conversation per day
  MAX_CONVERSATION_COUNT_24H: 100,    // 100 voice messages per conversation per day
  
  // Per-user limits (24 hours)
  MAX_USER_SIZE_MB_24H: 200,         // 200MB per user per day
  MAX_USER_COUNT_24H: 500,           // 500 voice messages per user per day
  
  // Storage limits
  MAX_TOTAL_USER_STORAGE_MB: 1000,   // 1GB total storage per user
}

export interface VoiceLimitCheck {
  allowed: boolean
  reason?: string
  currentUsage?: {
    messageSizeMB: number
    conversationSizeMB24h: number
    conversationCount24h: number
    userSizeMB24h: number
    userCount24h: number
    totalUserStorageMB: number
  }
  limits: typeof VOICE_LIMITS
}

/**
 * Check if a voice message can be recorded/uploaded based on size and duration
 */
export function checkMessageLimits(
  fileSizeBytes: number,
  durationSeconds: number
): VoiceLimitCheck {
  const fileSizeMB = fileSizeBytes / (1024 * 1024)
  
  // Check message size limit
  if (fileSizeMB > VOICE_LIMITS.MAX_MESSAGE_SIZE_MB) {
    return {
      allowed: false,
      reason: `Voice message too large. Maximum size is ${VOICE_LIMITS.MAX_MESSAGE_SIZE_MB}MB, but your message is ${fileSizeMB.toFixed(2)}MB.`,
      limits: VOICE_LIMITS
    }
  }
  
  // Check message duration limits
  if (durationSeconds > VOICE_LIMITS.MAX_MESSAGE_DURATION_SECONDS) {
    const maxMinutes = Math.floor(VOICE_LIMITS.MAX_MESSAGE_DURATION_SECONDS / 60)
    const currentMinutes = Math.floor(durationSeconds / 60)
    return {
      allowed: false,
      reason: `Voice message too long. Maximum duration is ${maxMinutes} minutes, but your message is ${currentMinutes} minutes.`,
      limits: VOICE_LIMITS
    }
  }
  
  if (durationSeconds < VOICE_LIMITS.MIN_MESSAGE_DURATION_SECONDS) {
    return {
      allowed: false,
      reason: `Voice message too short. Minimum duration is ${VOICE_LIMITS.MIN_MESSAGE_DURATION_SECONDS} second.`,
      limits: VOICE_LIMITS
    }
  }
  
  return {
    allowed: true,
    limits: VOICE_LIMITS
  }
}

/**
 * Check conversation and user limits before allowing upload
 */
export async function checkVoiceLimits(
  userId: string,
  threadId: string,
  fileSizeBytes: number,
  durationSeconds: number
): Promise<VoiceLimitCheck> {
  try {
    // First check basic message limits
    const messageCheck = checkMessageLimits(fileSizeBytes, durationSeconds)
    if (!messageCheck.allowed) {
      return messageCheck
    }
    
    const fileSizeMB = fileSizeBytes / (1024 * 1024)
    const now = new Date()
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)
    
    // Get conversation usage in last 24 hours
    const { data: conversationData, error: convError } = await supabase
      .from('communications')
      .select('audio_file_size, audio_duration')
      .eq('thread_id', threadId)
      .eq('message_type', 'voice')
      .not('audio_file_size', 'is', null)
      .gte('created_at', yesterday.toISOString())
    
    if (convError) {
      console.error('Error checking conversation limits:', convError)
      // Allow on error to avoid blocking users
      return { allowed: true, limits: VOICE_LIMITS }
    }
    
    const conversationSizeBytes = conversationData?.reduce((sum, msg) => sum + (msg.audio_file_size || 0), 0) || 0
    const conversationSizeMB = conversationSizeBytes / (1024 * 1024)
    const conversationCount = conversationData?.length || 0
    
    // Check conversation limits
    if (conversationSizeMB + fileSizeMB > VOICE_LIMITS.MAX_CONVERSATION_SIZE_MB_24H) {
      return {
        allowed: false,
        reason: `Conversation voice message limit reached. Maximum ${VOICE_LIMITS.MAX_CONVERSATION_SIZE_MB_24H}MB per day. Current usage: ${conversationSizeMB.toFixed(2)}MB.`,
        currentUsage: {
          messageSizeMB: fileSizeMB,
          conversationSizeMB24h: conversationSizeMB,
          conversationCount24h: conversationCount,
          userSizeMB24h: 0,
          userCount24h: 0,
          totalUserStorageMB: 0
        },
        limits: VOICE_LIMITS
      }
    }
    
    if (conversationCount >= VOICE_LIMITS.MAX_CONVERSATION_COUNT_24H) {
      return {
        allowed: false,
        reason: `Too many voice messages in this conversation today. Maximum ${VOICE_LIMITS.MAX_CONVERSATION_COUNT_24H} per day.`,
        currentUsage: {
          messageSizeMB: fileSizeMB,
          conversationSizeMB24h: conversationSizeMB,
          conversationCount24h: conversationCount,
          userSizeMB24h: 0,
          userCount24h: 0,
          totalUserStorageMB: 0
        },
        limits: VOICE_LIMITS
      }
    }
    
    // Get user usage in last 24 hours
    const { data: userData, error: userError } = await supabase
      .from('communications')
      .select('audio_file_size, audio_duration')
      .eq('sender_id', userId)
      .eq('message_type', 'voice')
      .not('audio_file_size', 'is', null)
      .gte('created_at', yesterday.toISOString())
    
    if (userError) {
      console.error('Error checking user limits:', userError)
      // Allow on error to avoid blocking users
      return { allowed: true, limits: VOICE_LIMITS }
    }
    
    const userSizeBytes = userData?.reduce((sum, msg) => sum + (msg.audio_file_size || 0), 0) || 0
    const userSizeMB = userSizeBytes / (1024 * 1024)
    const userCount = userData?.length || 0
    
    // Check user daily limits
    if (userSizeMB + fileSizeMB > VOICE_LIMITS.MAX_USER_SIZE_MB_24H) {
      return {
        allowed: false,
        reason: `Daily voice message limit reached. Maximum ${VOICE_LIMITS.MAX_USER_SIZE_MB_24H}MB per day. Current usage: ${userSizeMB.toFixed(2)}MB.`,
        currentUsage: {
          messageSizeMB: fileSizeMB,
          conversationSizeMB24h: conversationSizeMB,
          conversationCount24h: conversationCount,
          userSizeMB24h: userSizeMB,
          userCount24h: userCount,
          totalUserStorageMB: 0
        },
        limits: VOICE_LIMITS
      }
    }
    
    if (userCount >= VOICE_LIMITS.MAX_USER_COUNT_24H) {
      return {
        allowed: false,
        reason: `Too many voice messages today. Maximum ${VOICE_LIMITS.MAX_USER_COUNT_24H} per day.`,
        currentUsage: {
          messageSizeMB: fileSizeMB,
          conversationSizeMB24h: conversationSizeMB,
          conversationCount24h: conversationCount,
          userSizeMB24h: userSizeMB,
          userCount24h: userCount,
          totalUserStorageMB: 0
        },
        limits: VOICE_LIMITS
      }
    }
    
    // Get total user storage usage
    const { data: totalData, error: totalError } = await supabase
      .from('communications')
      .select('audio_file_size')
      .eq('sender_id', userId)
      .eq('message_type', 'voice')
      .not('audio_file_size', 'is', null)
    
    const totalSizeBytes = totalData?.reduce((sum, msg) => sum + (msg.audio_file_size || 0), 0) || 0
    const totalSizeMB = totalSizeBytes / (1024 * 1024)
    
    // Check total storage limit
    if (totalSizeMB + fileSizeMB > VOICE_LIMITS.MAX_TOTAL_USER_STORAGE_MB) {
      return {
        allowed: false,
        reason: `Total storage limit reached. Maximum ${VOICE_LIMITS.MAX_TOTAL_USER_STORAGE_MB}MB total. Current usage: ${totalSizeMB.toFixed(2)}MB.`,
        currentUsage: {
          messageSizeMB: fileSizeMB,
          conversationSizeMB24h: conversationSizeMB,
          conversationCount24h: conversationCount,
          userSizeMB24h: userSizeMB,
          userCount24h: userCount,
          totalUserStorageMB: totalSizeMB
        },
        limits: VOICE_LIMITS
      }
    }
    
    // All checks passed
    return {
      allowed: true,
      currentUsage: {
        messageSizeMB: fileSizeMB,
        conversationSizeMB24h: conversationSizeMB,
        conversationCount24h: conversationCount,
        userSizeMB24h: userSizeMB,
        userCount24h: userCount,
        totalUserStorageMB: totalSizeMB
      },
      limits: VOICE_LIMITS
    }
    
  } catch (error) {
    console.error('Error checking voice limits:', error)
    // Allow on error to avoid blocking users, but log for monitoring
    return { allowed: true, limits: VOICE_LIMITS }
  }
}

/**
 * Get user's current voice usage statistics
 */
export async function getVoiceUsageStats(userId: string) {
  try {
    const now = new Date()
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)
    
    // Get 24h usage
    const { data: dailyData } = await supabase
      .from('communications')
      .select('audio_file_size, thread_id')
      .eq('sender_id', userId)
      .eq('message_type', 'voice')
      .not('audio_file_size', 'is', null)
      .gte('created_at', yesterday.toISOString())
    
    // Get total usage
    const { data: totalData } = await supabase
      .from('communications')
      .select('audio_file_size')
      .eq('sender_id', userId)
      .eq('message_type', 'voice')
      .not('audio_file_size', 'is', null)
    
    const dailySizeBytes = dailyData?.reduce((sum, msg) => sum + (msg.audio_file_size || 0), 0) || 0
    const totalSizeBytes = totalData?.reduce((sum, msg) => sum + (msg.audio_file_size || 0), 0) || 0
    
    // Count unique conversations
    const uniqueThreads = new Set(dailyData?.map(msg => msg.thread_id) || [])
    
    return {
      daily: {
        sizeMB: dailySizeBytes / (1024 * 1024),
        count: dailyData?.length || 0,
        conversations: uniqueThreads.size
      },
      total: {
        sizeMB: totalSizeBytes / (1024 * 1024),
        count: totalData?.length || 0
      },
      limits: VOICE_LIMITS
    }
    
  } catch (error) {
    console.error('Error getting voice usage stats:', error)
    return null
  }
}
