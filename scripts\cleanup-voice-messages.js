// Cleanup script for voice messages
// Run this periodically to clean up orphaned files and old messages

const { cleanupOrphanedVoiceFiles, cleanupOldVoiceMessages, getVoiceStorageStats } = require('../utils/voice-cleanup');
require('dotenv').config();

async function runCleanup() {
  console.log('🎤 Voice Message Cleanup Started');
  console.log('================================');

  try {
    // 1. Get current storage stats
    console.log('📊 Getting storage statistics...');
    const statsBefore = await getVoiceStorageStats();
    
    if (statsBefore) {
      console.log(`📁 Files in storage: ${statsBefore.totalFiles}`);
      console.log(`💾 Database records: ${statsBefore.databaseRecords}`);
      console.log(`📦 Total size: ${statsBefore.totalSizeMB} MB`);
      console.log(`🗑️ Potential orphaned files: ${statsBefore.orphanedFiles}`);
    }

    console.log('');

    // 2. Clean up orphaned files
    console.log('🧹 Cleaning up orphaned files...');
    const orphanResult = await cleanupOrphanedVoiceFiles();
    
    if (orphanResult.success) {
      console.log(`✅ Deleted ${orphanResult.deletedFiles} orphaned files`);
    } else {
      console.log('❌ Orphan cleanup failed:');
      orphanResult.errors.forEach(error => console.log(`   - ${error}`));
    }

    console.log('');

    // 3. Clean up old messages (older than 30 days)
    console.log('🕒 Cleaning up old voice messages (30+ days)...');
    const oldResult = await cleanupOldVoiceMessages(30);
    
    if (oldResult.success) {
      console.log(`✅ Cleaned up ${oldResult.deletedFiles} old voice messages`);
    } else {
      console.log('❌ Old message cleanup failed:');
      oldResult.errors.forEach(error => console.log(`   - ${error}`));
    }

    console.log('');

    // 4. Get final storage stats
    console.log('📊 Final storage statistics...');
    const statsAfter = await getVoiceStorageStats();
    
    if (statsAfter) {
      console.log(`📁 Files in storage: ${statsAfter.totalFiles}`);
      console.log(`💾 Database records: ${statsAfter.databaseRecords}`);
      console.log(`📦 Total size: ${statsAfter.totalSizeMB} MB`);
      
      if (statsBefore) {
        const savedMB = statsBefore.totalSizeMB - statsAfter.totalSizeMB;
        const savedFiles = statsBefore.totalFiles - statsAfter.totalFiles;
        
        if (savedMB > 0 || savedFiles > 0) {
          console.log('');
          console.log('💰 Cleanup Results:');
          console.log(`   - Freed up: ${savedMB.toFixed(2)} MB`);
          console.log(`   - Removed: ${savedFiles} files`);
        }
      }
    }

    console.log('');
    console.log('🎉 Voice message cleanup completed successfully!');

  } catch (error) {
    console.error('❌ Cleanup script failed:', error);
    process.exit(1);
  }
}

// Check if script is run directly
if (require.main === module) {
  runCleanup()
    .then(() => {
      console.log('✅ Cleanup script finished');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Cleanup script error:', error);
      process.exit(1);
    });
}

module.exports = { runCleanup };
