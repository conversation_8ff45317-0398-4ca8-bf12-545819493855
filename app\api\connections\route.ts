import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { createServerSupabase } from '@/lib/supabase-server'

const adminClient = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// Helper function to get authenticated user from request
async function getAuthenticatedUser(request: Request) {
  try {
    // Try Authorization header first
    const authHeader = request.headers.get('Authorization')
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7)
      const { data: { user }, error } = await adminClient.auth.getUser(token)
      if (!error && user) {
        return user
      }
    }

    // Fall back to server client with cookies
    const supabase = await createServerSupabase()
    const { data: { session }, error } = await supabase.auth.getSession()
    if (!error && session?.user) {
      return session.user
    }

    return null
  } catch (error) {
    console.error("Error getting authenticated user:", error)
    return null
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const favoritesOnly = searchParams.get('favorites_only') === 'true'
    const type = searchParams.get('type')

    // Build the query
    let query = adminClient
      .from('connections')
      .select(`
        id,
        user1_id,
        user2_id,
        connection_type,
        status,
        user1_favorite,
        user2_favorite,
        created_at,
        updated_at,
        notes
      `)
      .or(`user1_id.eq.${user.id},user2_id.eq.${user.id}`)

    // Apply filters
    if (status && status !== 'all') {
      query = query.eq('status', status)
    }

    if (favoritesOnly) {
      // Filter for favorites based on which user is requesting
      query = query.or(`user1_favorite.eq.true.and.user1_id.eq.${user.id},user2_favorite.eq.true.and.user2_id.eq.${user.id}`)
    }

    if (type) {
      query = query.eq('connection_type', type)
    }

    // Execute query
    const { data: connections, error: connectionsError } = await query

    if (connectionsError) {
      console.error('Error fetching connections:', connectionsError)
      return NextResponse.json(
        { error: 'Failed to fetch connections' },
        { status: 500 }
      )
    }

    // Get connection profiles for each connection
    const connectionsWithProfiles = await Promise.all(
      (connections || []).map(async (connection) => {
        // Determine the other user ID
        const otherUserId = connection.user1_id === user.id
          ? connection.user2_id
          : connection.user1_id

        // Get the other user's profile
        const { data: profile } = await adminClient
          .from('connection_profiles')
          .select('*')
          .eq('user_id', otherUserId)
          .single()

        // Determine if this connection is marked as favorite by the current user
        const isFavorite = connection.user1_id === user.id
          ? connection.user1_favorite
          : connection.user2_favorite

        return {
          id: connection.id,
          connection_type: connection.connection_type,
          status: connection.status,
          other_user_id: otherUserId,
          is_favorite: isFavorite,
          created_at: connection.created_at,
          updated_at: connection.updated_at,
          notes: connection.notes,
          other_user: profile ? {
            id: profile.id,
            user_id: profile.user_id,
            display_name: profile.display_name,
            bio: profile.bio,
            avatar_url: profile.avatar_url,
            role_capabilities: {
              can_be_customer: true,
              can_be_rider: false,
              owns_business: false
            },
            specialties: {},
            average_rating: 4.5,
            total_ratings: 0,
            is_public: profile.is_public,
            allow_direct_messages: profile.allow_direct_messages
          } : {
            id: otherUserId,
            user_id: otherUserId,
            display_name: 'Unknown User',
            bio: '',
            avatar_url: null,
            role_capabilities: {
              can_be_customer: true,
              can_be_rider: false,
              owns_business: false
            },
            specialties: {},
            average_rating: 0,
            total_ratings: 0,
            is_public: true,
            allow_direct_messages: true
          }
        }
      })
    )

    return NextResponse.json({
      connections: connectionsWithProfiles,
      total: connectionsWithProfiles.length
    })

  } catch (error) {
    console.error('Error in connections API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { other_user_id, connection_type, notes } = body

    if (!other_user_id || !connection_type) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Ensure user1_id is always the smaller UUID for consistency
    const user1_id = user.id < other_user_id ? user.id : other_user_id
    const user2_id = user.id < other_user_id ? other_user_id : user.id

    // Create the connection
    const { data: connection, error } = await adminClient
      .from('connections')
      .insert({
        user1_id,
        user2_id,
        connection_type,
        status: 'active',
        created_by: user.id,
        notes: notes || null
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating connection:', error)
      return NextResponse.json(
        { error: 'Failed to create connection' },
        { status: 500 }
      )
    }

    return NextResponse.json({ connection }, { status: 201 })

  } catch (error) {
    console.error('Error in connections POST API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
