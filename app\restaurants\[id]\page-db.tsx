"use client"

import { useState, useEffect, useMemo, useRef } from "react"
import React from "react"
import Link from "next/link"
import { Clock, MapPin, Phone, Mail, Info, Star, UtensilsCrossed, ShoppingBag, ChevronLeft, ChevronRight, Search } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { notFound } from "next/navigation"
import MenuCategory from "@/components/menu-category"
import ProductItem from "@/components/product-item"
import OSMStaticMap from "@/components/osm-static-map"
// Removed import of deleted delivery-time-estimator
import { useRealtimeCart } from "@/context/realtime-cart-context"
import { CopyButton } from "@/components/copy-button"
import { Card, CardContent } from "@/components/ui/card"
import CategoryScroller from "@/components/category-scroller"
import { getRestaurantById } from "@/services/restaurant-service"
import type { Restaurant } from "@/types/restaurant"

// Add Jersey coordinates for each restaurant
const restaurantCoordinates: Record<string, [number, number]> = {
  "robin-hood": [-2.1053, 49.1805], // St Helier
  "jersey-grill": [-2.1053, 49.1805], // St Helier
  "st-brelade-bistro": [-2.1777, 49.1872], // St Brelade
  "spice-of-jersey": [-2.1053, 49.1805], // St Helier
  "jersey-pizza-co": [-2.0933, 49.1994], // St Saviour
  "gorey-castle-cafe": [-2.0158, 49.2003], // Gorey
  "wok-this-way": [-2.1053, 49.1805], // St Helier
  "jersey-bean": [-2.1672, 49.1876], // St Aubin
  "coastal-bites": [-2.2236, 49.2173], // St Ouen
}

// Add delivery radius for each restaurant (in kilometers)
const restaurantDeliveryRadius: Record<string, number> = {
  "robin-hood": 5,
  "jersey-grill": 5,
  "st-brelade-bistro": 4,
  "spice-of-jersey": 5,
  "jersey-pizza-co": 6,
  "gorey-castle-cafe": 3,
  "wok-this-way": 5,
  "jersey-bean": 4,
  "coastal-bites": 3,
}

export default function RestaurantPage({ params }: { params: { id: string } }) {
  // Unwrap params with React.use()
  const unwrappedParams = React.use(params)
  const [restaurant, setRestaurant] = useState<Restaurant | null>(null)
  const [loading, setLoading] = useState(true)
  const { cart, getItemsByRestaurant } = useRealtimeCart()
  const [restaurantItems, setRestaurantItems] = useState<any[]>([])
  const [restaurantSubtotal, setRestaurantSubtotal] = useState(0)
  const [showCopiedMessage, setShowCopiedMessage] = useState(false)

  // Fetch restaurant data from database
  useEffect(() => {
    async function fetchRestaurant() {
      setLoading(true)
      try {
        const data = await getRestaurantById(unwrappedParams.id)
        if (data) {
          setRestaurant(data)
        } else {
          notFound()
        }
      } catch (error) {
        console.error("Error fetching restaurant:", error)
        notFound()
      } finally {
        setLoading(false)
      }
    }

    fetchRestaurant()
  }, [unwrappedParams.id])

  // If still loading or no restaurant data, show loading state
  if (loading || !restaurant) {
    return (
      <div className="container-fluid py-12 text-center">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mx-auto mb-4"></div>
          <div className="h-64 bg-gray-200 rounded mb-6"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mx-auto mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/3 mx-auto"></div>
        </div>
      </div>
    )
  }

  // Get items and calculate subtotal for this specific restaurant
  useEffect(() => {
    if (!restaurant) return

    const itemsByRestaurant = getItemsByRestaurant()
    const items = itemsByRestaurant[restaurant.id] || []
    setRestaurantItems(items)

    const subtotal = items.reduce((total, item) => {
      return total + item.price * item.quantity
    }, 0)
    setRestaurantSubtotal(subtotal)
  }, [restaurant?.id, cart, getItemsByRestaurant, restaurant])

  // Get coordinates for this restaurant, default to St Helier if not found
  const coordinates = restaurantCoordinates[unwrappedParams.id] || [-2.1053, 49.1805]
  const deliveryRadius = restaurantDeliveryRadius[unwrappedParams.id] || 5

  // Generate a discount code based on restaurant name
  const discountCode = useMemo(() => {
    if (!restaurant) return ""
    const name = restaurant.name.toUpperCase().replace(/[^A-Z]/g, "")
    return `${name.substring(0, 4)}25`
  }, [restaurant])

  // State for active category
  const [activeCategory, setActiveCategory] = useState<string>("")

  // Set initial active category when component mounts
  useEffect(() => {
    if (restaurant && restaurant.menuCategories.length > 0) {
      setActiveCategory(restaurant.menuCategories[0].id)
    }
  }, [restaurant])

  // Scroll spy functionality to update active category based on scroll position
  useEffect(() => {
    if (!restaurant) return

    const handleScroll = () => {
      // Don't run if we're programmatically scrolling
      if (isScrolling.current) return

      const categoryElements = restaurant.menuCategories.map(category =>
        document.getElementById(`category-${category.id}`)
      )

      // Find the category that is currently most visible in the viewport
      let mostVisibleCategory = null
      let maxVisibleHeight = 0

      categoryElements.forEach((element, index) => {
        if (!element) return

        const rect = element.getBoundingClientRect()
        const visibleTop = Math.max(rect.top, 180) // 180px is the navbar + sticky header height
        const visibleBottom = Math.min(rect.bottom, window.innerHeight)
        const visibleHeight = Math.max(0, visibleBottom - visibleTop)

        if (visibleHeight > maxVisibleHeight) {
          maxVisibleHeight = visibleHeight
          mostVisibleCategory = restaurant.menuCategories[index].id
        }
      })

      if (mostVisibleCategory && mostVisibleCategory !== activeCategory) {
        setActiveCategory(mostVisibleCategory)
      }
    }

    window.addEventListener('scroll', handleScroll)
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [restaurant, activeCategory])

  // Ref to track if we're programmatically scrolling
  const isScrolling = useRef(false)

  // Function to scroll to a category section
  const scrollToCategory = (categoryId: string) => {
    const element = document.getElementById(`category-${categoryId}`)
    if (element) {
      // Set flag to prevent scroll spy from running during programmatic scroll
      isScrolling.current = true

      // Offset for the navbar + sticky header (search + categories)
      const offset = 180
      const elementPosition = element.getBoundingClientRect().top
      const offsetPosition = elementPosition + window.pageYOffset - offset

      window.scrollTo({
        top: offsetPosition,
        behavior: "smooth"
      })

      // Reset the flag after animation completes (roughly 500ms)
      setTimeout(() => {
        isScrolling.current = false
      }, 500)
    }
  }

  // Handle category change from the scroller
  const handleCategoryChange = (categoryId: string) => {
    setActiveCategory(categoryId)
    scrollToCategory(categoryId)
  }

  // State for search query
  const [searchQuery, setSearchQuery] = useState("")

  // Create a flat list of all menu items for search functionality
  const allMenuItems = useMemo(() => {
    if (!restaurant) return []

    return restaurant.menuCategories.flatMap(category =>
      category.items.map(item => ({
        ...item,
        categoryId: category.id,
        categoryName: category.name
      }))
    )
  }, [restaurant])

  // Filter menu items based on search query
  const filteredMenuItems = useMemo(() => {
    if (!searchQuery.trim()) return null

    const query = searchQuery.toLowerCase().trim()
    return allMenuItems.filter(item =>
      item.name.toLowerCase().includes(query) ||
      (item.description && item.description.toLowerCase().includes(query)) ||
      item.categoryName.toLowerCase().includes(query)
    )
  }, [searchQuery, allMenuItems])

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value)
  }

  return (
    <div>
      {/* Restaurant Header - Two Column Layout */}
      <div className="bg-white">
        <div className="container-fluid py-3">
          <div className="flex flex-col md:flex-row gap-3">
            {/* Left Column - Restaurant Image */}
            <div className="md:w-1/2">
              <div className="relative rounded-lg overflow-hidden shadow-md h-full min-h-[175px]">
                <img
                  src={restaurant.coverImage || "/placeholder.svg"}
                  alt={restaurant.name}
                  className="w-full h-full object-contain"
                />
                <div className="absolute inset-0 bg-black/30" />
                <div className="absolute bottom-0 left-0 right-0 p-6 bg-gradient-to-t from-black/80 to-transparent text-white">
                  <h1 className="text-3xl font-bold mb-2">{restaurant.name}</h1>
                  <div className="flex flex-wrap gap-4 text-sm">
                    <div className="flex items-center">
                      <Star className="mr-1 text-yellow-400" size={16} />
                      <span>
                        {restaurant.rating} ({restaurant.reviewCount} reviews)
                      </span>
                    </div>
                    <div className="flex items-center">
                      <Clock className="mr-1 text-yellow-400" size={16} />
                      <span className="font-medium">Est. Delivery: {restaurant.deliveryTime} min</span>
                    </div>
                    <div className="flex items-center">
                      <MapPin className="mr-1" size={16} />
                      <span>{restaurant.location}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Restaurant Info Tabs */}
            <div className="md:w-1/2">
              <div className="bg-white rounded-lg shadow-md p-3 h-full">
                <Tabs defaultValue="location" className="h-full flex flex-col">
                  <TabsList className="mb-2">
                    <TabsTrigger value="location">Location & Delivery</TabsTrigger>
                    <TabsTrigger value="map">Map</TabsTrigger>
                    <TabsTrigger value="reviews">Reviews</TabsTrigger>
                    <TabsTrigger value="info">Restaurant Info</TabsTrigger>
                  </TabsList>

                  <TabsContent value="location" className="flex-grow">
                    <div className="space-y-2">
                      <div className="flex items-start">
                        <MapPin className="h-5 w-5 text-emerald-600 mr-2 mt-0.5" />
                        <div>
                          <p className="font-medium">{restaurant.name}</p>
                          <p className="text-gray-600">{restaurant.location}</p>
                          <p className="text-gray-600">Jersey, Channel Islands</p>
                        </div>
                      </div>

                      <div className="flex items-start">
                        <Clock className="h-5 w-5 text-emerald-600 mr-2 mt-0.5" />
                        <div>
                          <p className="font-medium">Delivery Time</p>
                          <p className="text-gray-600">Estimated {restaurant.deliveryTime} minutes</p>
                          <p className="text-gray-600 text-sm">Preparation: {restaurant.preparationTimeMinutes || 15} min</p>
                        </div>
                      </div>

                      <div className="flex items-start">
                        <UtensilsCrossed className="h-5 w-5 text-emerald-600 mr-2 mt-0.5" />
                        <div>
                          <p className="font-medium">Delivery Fee</p>
                          <p className="text-gray-600">
                            {restaurant.deliveryFee === 0 ? "Free Delivery" : `£${restaurant.deliveryFee.toFixed(2)}`}
                          </p>
                          <p className="text-gray-600 text-sm">Minimum order: £15.00</p>
                        </div>
                      </div>

                      <div className="mt-4">
                        <a
                          href={`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(`${restaurant.name} ${restaurant.location} Jersey`)}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-emerald-600 text-sm hover:underline flex items-center"
                        >
                          <MapPin className="h-4 w-4 mr-1" /> View on Google Maps
                        </a>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="map" className="flex-grow">
                    <div className="h-full">
                      <OSMStaticMap
                        name={restaurant.name}
                        location={restaurant.location}
                        coordinates={coordinates}
                        deliveryRadius={deliveryRadius}
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="reviews" className="flex-grow">
                    <div className="space-y-2">
                      <div className="flex items-center">
                        <Star className="h-6 w-6 text-yellow-400 mr-2" />
                        <span className="text-xl font-bold">{restaurant.rating}</span>
                        <span className="text-gray-500 ml-2">({restaurant.reviewCount} reviews)</span>
                      </div>
                      <p className="text-gray-500">Detailed reviews coming soon...</p>
                    </div>
                  </TabsContent>

                  <TabsContent value="info" className="flex-grow">
                    <div className="space-y-2">
                      <div>
                        <h3 className="font-semibold mb-2">About {restaurant.name}</h3>
                        <p className="text-gray-600">{restaurant.description}</p>
                      </div>

                      <div>
                        <h3 className="font-semibold mb-2">Opening Hours</h3>
                        <p className="text-gray-600">Monday - Friday: 11:00 - 22:00</p>
                        <p className="text-gray-600">Saturday - Sunday: 12:00 - 23:00</p>
                      </div>

                      <div>
                        <h3 className="font-semibold mb-2">Contact</h3>
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center">
                            <Phone className="h-4 w-4 text-gray-500 mr-2" />
                            <p className="text-gray-600">{restaurant.phone}</p>
                          </div>
                          <CopyButton
                            value={restaurant.phone}
                            size="sm"
                            variant="ghost"
                            tooltipText="Copy phone number"
                          />
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Restaurant Content */}
      <div className="container mx-auto px-4 py-6">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Menu Section */}
          <div className="lg:w-2/3">
            <h2 className="text-2xl font-bold mb-6">Menu</h2>

            {/* New: Discount Card with Copy Button */}
            <Card className="mb-6 bg-emerald-50 border-emerald-200">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-start gap-3">
                    <div className="bg-emerald-100 p-2 rounded-full">
                      <UtensilsCrossed className="h-5 w-5 text-emerald-600" />
                    </div>
                    <div>
                      <h3 className="font-medium text-emerald-800">First-time customer?</h3>
                      <p className="text-sm text-emerald-700">
                        Use code <span className="font-bold">{discountCode}</span> for 25% off your first order
                      </p>
                    </div>
                  </div>
                  <CopyButton
                    value={discountCode}
                    tooltipText="Copy discount code"
                    successText="Code copied!"
                    variant="outline"
                    className="border-emerald-200 hover:bg-emerald-100"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Sticky Search and Categories Container */}
            <div className="sticky top-[64px] z-30 bg-white -mx-4 px-4 pt-3 pb-2 shadow-sm border-b border-gray-100">
              {/* Search Input */}
              <div className="mb-4 relative">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    placeholder="Search menu items..."
                    value={searchQuery}
                    onChange={handleSearchChange}
                    className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                  />
                  {searchQuery && (
                    <button
                      onClick={() => setSearchQuery("")}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                    >
                      ×
                    </button>
                  )}
                </div>
              </div>

              {/* Horizontal Category Scroller - Hide when searching */}
              {!searchQuery && (
                <div className="pb-1">
                  <CategoryScroller
                    categories={restaurant.menuCategories}
                    activeCategory={activeCategory}
                    onCategoryChange={handleCategoryChange}
                  />
                </div>
              )}
            </div>

            {/* Search Results */}
            {filteredMenuItems && filteredMenuItems.length > 0 ? (
              <div className="mt-8">
                <h2 className="text-xl font-bold mb-4">Search Results</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-10">
                  {filteredMenuItems.map((item) => (
                    <ProductItem
                      key={item.id}
                      product={item}
                      businessId={restaurant.id} // This is now the numeric ID
                      businessSlug={restaurant.slug || unwrappedParams.id} // Pass the slug for routing
                      businessName={restaurant.name}
                      businessType="restaurant"
                      categoryId={item.categoryId}
                      layout="compact"
                    />
                  ))}
                </div>
              </div>
            ) : filteredMenuItems && filteredMenuItems.length === 0 ? (
              <div className="mt-8 p-8 text-center bg-white rounded-lg border border-gray-100 shadow-sm">
                <p className="text-gray-600">No menu items found matching "{searchQuery}"</p>
              </div>
            ) : (
              /* Menu Categories - Show when not searching */
              <div className="mt-8">
                {restaurant.menuCategories.map((category) => (
                  <div
                    key={category.id}
                    id={`category-${category.id}`}
                    className="scroll-mt-48"
                  >
                    <h2 className="text-xl font-bold mb-4">{category.name}</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-10">
                      {category.items.map((item) => (
                        <ProductItem
                          key={item.id}
                          product={item}
                          businessId={restaurant.id} // This is now the numeric ID
                          businessSlug={restaurant.slug || unwrappedParams.id} // Pass the slug for routing
                          businessName={restaurant.name}
                          businessType="restaurant"
                          categoryId={category.id}
                          layout="compact"
                        />
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Order Section - Only for this restaurant */}
          <div className="lg:w-1/3">
            <div className="bg-white rounded-lg shadow-md p-6 sticky top-[180px]">
              <div className="flex items-center mb-4">
                <ShoppingBag className="mr-2 text-emerald-600" />
                <h3 className="text-lg font-semibold">Your Order</h3>
              </div>

              {restaurantItems.length === 0 ? (
                <div className="border-t border-b py-4 my-4">
                  <p className="text-center text-gray-500">Add items from this restaurant to your order</p>
                  <p className="text-center text-sm text-emerald-600 mt-2">
                    You can order from multiple businesses at once!
                  </p>
                </div>
              ) : (
                <div className="border-t pt-4 my-4">
                  <div className="max-h-60 overflow-y-auto mb-4">
                    {restaurantItems.map((item) => (
                      <div key={`${item.id}-${item.options?.join("-")}`} className="flex justify-between mb-3">
                        <div>
                          <p className="font-medium">
                            {item.quantity}x {item.name}
                          </p>
                          {item.options && item.options.length > 0 && (
                            <p className="text-sm text-gray-500">{item.options.join(", ")}</p>
                          )}
                        </div>
                        <p className="font-medium">£{(item.price * item.quantity).toFixed(2)}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="space-y-4">
                <div className="flex justify-between text-sm">
                  <span>Subtotal</span>
                  <span>£{restaurantSubtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Delivery Fee</span>
                  <span>£{restaurant.deliveryFee.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Service Fee</span>
                  <span>£0.50</span>
                </div>
                <div className="flex justify-between font-semibold pt-4 border-t">
                  <span>Restaurant Total</span>
                  <span>£{(restaurantSubtotal + restaurant.deliveryFee + 0.5).toFixed(2)}</span>
                </div>
              </div>

              {restaurantItems.length > 0 ? (
                <Link href="/checkout">
                  <Button className="w-full mt-6 bg-emerald-600 hover:bg-emerald-700">Go to Checkout</Button>
                </Link>
              ) : (
                <Button className="w-full mt-6 bg-emerald-600 hover:bg-emerald-700" disabled>
                  Go to Checkout
                </Button>
              )}

              <div className="mt-4 text-xs text-gray-500 flex items-start">
                <Info size={14} className="mr-1 flex-shrink-0 mt-0.5" />
                <p>Minimum order of £15 required for delivery</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
