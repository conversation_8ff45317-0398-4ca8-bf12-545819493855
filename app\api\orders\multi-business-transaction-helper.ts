// Multi-business transaction helper that creates separate order rows for each business
import { createClient } from '@supabase/supabase-js';

// Create a Supabase client with admin privileges for transaction support
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || '',
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

/**
 * Creates separate order rows for each business in a multi-business order
 * This version creates one order row per business
 */
export async function createMultiBusinessOrder(
  orderData: any,
  orderItems: any[],
  orderBusinesses: any[]
) {
  console.log('🔄 TRANSACTION: Starting multi-business order creation transaction');
  console.log('📦 TRANSACTION: Order data summary:', {
    customerName: orderData.customer_name,
    customerPhone: orderData.customer_phone,
    itemCount: orderItems.length,
    businessCount: orderBusinesses.length,
    total: orderData.total
  });

  // Log Supabase connection info
  console.log('🔌 TRANSACTION: Supabase connection info:', {
    url: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Set' : 'Not set',
    serviceKey: process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Set (length: ' + process.env.SUPABASE_SERVICE_ROLE_KEY.length + ')' : 'Not set',
    environment: process.env.NODE_ENV
  });

  // Log the first business for debugging
  if (orderBusinesses.length > 0) {
    console.log('🏪 TRANSACTION: First business:', {
      business_id: orderBusinesses[0].business_id,
      business_name: orderBusinesses[0].business_name,
      business_type: orderBusinesses[0].business_type
    });
  }

  // Test the database connection first
  try {
    console.log('🔍 TRANSACTION: Testing database connection...');

    // Check if we have valid Supabase credentials
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      console.error('❌ TRANSACTION: Missing Supabase credentials');
      throw new Error('Missing Supabase credentials. Check environment variables.');
    }

    // Use a simple query that doesn't require specific table access
    const { data: testData, error: testError } = await supabaseAdmin
      .from('businesses')
      .select('id')
      .limit(1);

    if (testError) {
      console.error('❌ TRANSACTION: Database connection test failed:', testError);
      throw new Error(`Database connection test failed: ${testError.message}`);
    }

    if (!testData || testData.length === 0) {
      console.warn('⚠️ TRANSACTION: Database connection successful but no businesses found');
    } else {
      console.log('✅ TRANSACTION: Database connection test successful:', testData);
    }
  } catch (testError) {
    console.error('❌ TRANSACTION: Database connection test exception:', testError);
    console.error('Error details:', testError);

    if (testError instanceof Error) {
      console.error('Error stack:', testError.stack);
    }

    throw new Error(`Database connection test exception: ${testError.message || 'Unknown error'}`);
  }

  // We'll skip using explicit transactions since they're not well supported in Supabase REST API
  // Instead, we'll handle each business order separately
  try {
    console.log('✅ TRANSACTION: Processing orders for each business separately');

    // Create base order data object with common fields
    let baseOrderData: any = {
      // Timestamps are the only fields we'll set automatically
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Add common customer information
    if (orderData.customer_name) baseOrderData.customer_name = orderData.customer_name;
    if (orderData.customer_email) baseOrderData.customer_email = orderData.customer_email;
    if (orderData.customer_phone) baseOrderData.customer_phone = orderData.customer_phone;
    if (orderData.customer_address) baseOrderData.customer_address = orderData.customer_address;
    if (orderData.customer_coordinates) baseOrderData.customer_coordinates = orderData.customer_coordinates;

    // Delivery address - use the provided delivery_address or customer_address
    if (orderData.delivery_address) {
      baseOrderData.delivery_address = orderData.delivery_address;
    } else if (orderData.customer_address) {
      baseOrderData.delivery_address = orderData.customer_address;
    }

    // Payment information
    if (orderData.payment_method) baseOrderData.payment_method = orderData.payment_method;
    if (orderData.payment_status) baseOrderData.payment_status = orderData.payment_status;
    if (orderData.notes !== undefined) baseOrderData.notes = orderData.notes;
    if (orderData.delivery_instructions) baseOrderData.delivery_instructions = orderData.delivery_instructions;

    // Process user_id if present
    if (orderData.user_id) {
      console.log(`Processing user_id: ${orderData.user_id} (type: ${typeof orderData.user_id})`);

      // Check if we need to look up the numeric user ID from the auth_id
      if (typeof orderData.user_id === 'string' && orderData.user_id.includes('-')) {
        // This looks like a UUID, so we need to look up the numeric user ID
        console.log(`Looking up numeric user ID for auth_id: ${orderData.user_id}`);
        try {
          const { data: userData, error: userError } = await supabaseAdmin
            .from('users')
            .select('id, auth_id, email')
            .eq('auth_id', orderData.user_id)
            .single();

          if (userError) {
            console.error('Error looking up user ID:', userError);
            // Do not set a user_id for non-existent users
            delete baseOrderData.user_id;
          } else if (userData) {
            console.log(`Found numeric user ID: ${userData.id} for auth_id: ${orderData.user_id}`);
            baseOrderData.user_id = userData.id;
          } else {
            console.log(`No user found for auth_id: ${orderData.user_id}`);
            // Do not set a user_id for non-existent users
            delete baseOrderData.user_id;
          }
        } catch (err) {
          console.error('Exception looking up user ID:', err);
          // Do not set a user_id when there's an error
          delete baseOrderData.user_id;
        }
      } else {
        // Try to convert to a number if it's not already
        const numericUserId = Number(orderData.user_id);
        if (!isNaN(numericUserId)) {
          console.log(`Using numeric user ID: ${numericUserId}`);
          baseOrderData.user_id = numericUserId;
        } else {
          console.error(`Invalid user_id: ${orderData.user_id}, must be a number or UUID`);
          // Do not set a user_id for invalid user IDs
          delete baseOrderData.user_id;
        }
      }
    }

    // Keep only the columns that exist in the orders table (based on actual database schema)
    const validColumns = [
      'id',
      'user_id',
      'business_id',
      'business_name',
      'business_type',
      'business_slug',
      'delivery_address',
      'notes',
      'created_at',
      'updated_at',
      'driver_id',
      'customer_name',
      'customer_email',
      'customer_phone',
      'customer_address',
      'customer_coordinates',
      'delivery_instructions',
      'payment_method',
      'payment_status',
      'total',
      'subtotal',
      'delivery_fee',
      'service_fee',
      'status',
      'preparation_time',
      'estimated_delivery_time',
      'order_number',
      'delivery_type',
      'asap',
      'scheduled_time'
    ];

    // Create a new object with only the valid columns
    const cleanedBaseOrderData: any = {};
    for (const key in baseOrderData) {
      if (validColumns.includes(key)) {
        cleanedBaseOrderData[key] = baseOrderData[key];
      } else {
        console.log(`Removing invalid column from base order data: ${key}`);
      }
    }

    // Replace the base order data with the cleaned data
    baseOrderData = cleanedBaseOrderData;

    // Array to store created order IDs
    const createdOrderIds: number[] = [];

    // Process each business and create a separate order for each
    for (const business of orderBusinesses) {
      // Skip invalid businesses
      if (!business || !business.business_id) {
        console.log('Skipping invalid business:', business);
        continue;
      }

      // Get business ID as a number
      const businessId = Number(business.business_id);
      if (isNaN(businessId) || businessId <= 0) {
        console.error(`Invalid business_id: ${business.business_id}`);
        continue;
      }

      // Generate a unique order number for this business
      // Format: B{businessId}-{YYMMDD}-{random}
      const now = new Date();
      const dateStr = now.toISOString().slice(2, 10).replace(/-/g, '');
      const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
      const orderNumber = `B${businessId}-${dateStr}-${random}`;
      console.log(`Generated order number for business ${businessId}: ${orderNumber}`);

      // Get business-specific delivery settings from checkout data
      const businessDeliveryMethod = business.delivery_method || business.deliveryMethod || 'delivery';
      const businessDeliveryType = business.delivery_type || business.deliveryType || 'asap';
      const businessScheduledTime = business.scheduled_time || business.scheduledTime || null;

      // Determine if this is ASAP or scheduled
      const isAsap = businessDeliveryType === 'asap';
      const isPickup = businessDeliveryMethod === 'pickup';

      // Calculate fees based on delivery method and original order
      const originalDeliveryFee = baseOrderData.delivery_fee || 0;
      const originalServiceFee = baseOrderData.service_fee || 0;

      // For pickup orders, no delivery fee. For delivery orders, use the business-specific fee
      const businessDeliveryFee = isPickup ? 0 : (business.delivery_fee || business.deliveryFee || 0);
      const businessServiceFee = business.service_fee || business.serviceFee || (originalServiceFee / orderBusinesses.length);
      const businessSubtotal = business.subtotal || 0;
      const businessTotal = businessSubtotal + businessDeliveryFee + businessServiceFee;

      // Get actual preparation and delivery times from checkout data (not hardcoded)
      const businessPreparationTime = business.preparation_time || business.preparationTime || 15;
      const businessEstimatedDeliveryTime = business.estimated_delivery_time || business.estimatedDeliveryTime || (isPickup ? 0 : 30);

      // Create a copy of the base order data for this business
      const businessOrderData = {
        ...baseOrderData,
        // Ensure these critical fields are always set
        order_number: orderNumber,
        business_id: businessId,
        business_name: business.business_name || business.businessName || `Business ${businessId}`,
        business_type: business.business_type || business.businessType || 'restaurant',
        business_slug: business.business_slug || business.businessSlug || `business-${businessId}`,
        status: 'pending',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),

        // Customer information
        customer_name: baseOrderData.customer_name || 'Guest Customer',
        customer_phone: baseOrderData.customer_phone || '***********',
        customer_email: baseOrderData.customer_email || '<EMAIL>',
        customer_address: baseOrderData.customer_address || 'No address provided',
        customer_coordinates: baseOrderData.customer_coordinates || '',

        // Delivery information - CRITICAL for fulfillment
        delivery_address: baseOrderData.delivery_address || baseOrderData.customer_address || 'No address provided',
        delivery_type: businessDeliveryMethod, // 'pickup' or 'delivery'

        // ASAP vs Scheduled - CRITICAL for timing
        asap: isAsap,
        scheduled_time: isAsap ? null : (businessScheduledTime ? new Date(businessScheduledTime).toISOString() : null),

        // Consolidate notes and delivery_instructions into one field
        delivery_instructions: baseOrderData.delivery_instructions || baseOrderData.notes || '',
        notes: '', // Keep notes empty to avoid duplication

        // Payment information
        payment_method: baseOrderData.payment_method || 'cash',
        payment_status: baseOrderData.payment_status || 'paid',

        // Financial data - CRITICAL for business operations
        subtotal: businessSubtotal,
        delivery_fee: businessDeliveryFee,
        service_fee: businessServiceFee,
        total: businessTotal,

        // Timing information - CRITICAL for fulfillment (from actual data, not hardcoded)
        preparation_time: businessPreparationTime,
        estimated_delivery_time: businessEstimatedDeliveryTime
      };

      // Log the business order data
      console.log(`Business order data for ${businessId}:`, {
        order_number: businessOrderData.order_number,
        business_id: businessOrderData.business_id,
        business_name: businessOrderData.business_name,
        business_type: businessOrderData.business_type
      });

      // Business data is already set in the businessOrderData object above

      // Add business slug if available
      if (business.business_slug) {
        businessOrderData.business_slug = business.business_slug;
      }

      // Add financial data
      if (business.total !== undefined) businessOrderData.total = business.total;
      if (business.subtotal !== undefined) businessOrderData.subtotal = business.subtotal;
      if (business.delivery_fee !== undefined) businessOrderData.delivery_fee = business.delivery_fee;
      if (business.service_fee !== undefined) businessOrderData.service_fee = business.service_fee;

      // Add time-related fields
      if (business.preparation_time !== undefined) businessOrderData.preparation_time = business.preparation_time;
      if (business.estimated_delivery_time !== undefined) businessOrderData.estimated_delivery_time = business.estimated_delivery_time;

      // Add business status
      businessOrderData.status = business.status || 'pending';

      console.log(`Creating order for business ${businessId} (${business.business_name || 'Unknown'}):`);
      console.log(JSON.stringify(businessOrderData, null, 2));

      // Insert the order for this business
      try {
        console.log(`Inserting order for business ${businessId} with order number ${orderNumber}`);
        console.log(`Business order data:`, JSON.stringify(businessOrderData, null, 2));

        // Check if we have all required fields (based on actual database schema)
        const requiredFields = [
          'customer_name', 'customer_phone', 'customer_address', 'delivery_address',
          'payment_method', 'total', 'business_id', 'order_number', 'delivery_type',
          'subtotal', 'delivery_fee', 'service_fee', 'preparation_time', 'asap'
        ];
        const missingFields = requiredFields.filter(field => {
          const value = businessOrderData[field];
          return value === undefined || value === null || (typeof value === 'string' && value.trim() === '');
        });

        // Additional validation for common constraint issues
        console.log('🔍 CONSTRAINT VALIDATION:');

        // Check business_id is valid
        if (businessOrderData.business_id && !Number.isInteger(businessOrderData.business_id)) {
          console.error(`❌ business_id must be an integer: ${businessOrderData.business_id} (type: ${typeof businessOrderData.business_id})`);
        }

        // Check user_id is valid if present
        if (businessOrderData.user_id !== undefined) {
          if (businessOrderData.user_id !== null && !Number.isInteger(businessOrderData.user_id)) {
            console.error(`❌ user_id must be an integer or null: ${businessOrderData.user_id} (type: ${typeof businessOrderData.user_id})`);
          }
        }

        // Check numeric fields are actually numbers
        const numericFields = ['total', 'subtotal', 'delivery_fee', 'service_fee', 'preparation_time', 'estimated_delivery_time'];
        numericFields.forEach(field => {
          if (businessOrderData[field] !== undefined && typeof businessOrderData[field] !== 'number') {
            console.error(`❌ ${field} must be a number: ${businessOrderData[field]} (type: ${typeof businessOrderData[field]})`);
          }
        });

        // Check boolean fields
        if (businessOrderData.asap !== undefined && typeof businessOrderData.asap !== 'boolean') {
          console.error(`❌ asap must be a boolean: ${businessOrderData.asap} (type: ${typeof businessOrderData.asap})`);
        }

        console.log('✅ Constraint validation completed');

        if (missingFields.length > 0) {
          console.error(`Missing required fields for business ${businessId}: ${missingFields.join(', ')}`);
          console.error('Adding default values to prevent database errors');

          // Add missing fields with default values to prevent database errors
          missingFields.forEach(field => {
            if (field === 'customer_name') businessOrderData.customer_name = 'Guest Customer';
            if (field === 'customer_phone') businessOrderData.customer_phone = '***********';
            if (field === 'customer_address') businessOrderData.customer_address = 'No address provided';
            if (field === 'delivery_address') businessOrderData.delivery_address = businessOrderData.customer_address || 'No address provided';
            if (field === 'payment_method') businessOrderData.payment_method = 'cash';
            if (field === 'delivery_type') businessOrderData.delivery_type = 'delivery';
            if (field === 'subtotal') businessOrderData.subtotal = 0;
            if (field === 'delivery_fee') businessOrderData.delivery_fee = 0;
            if (field === 'service_fee') businessOrderData.service_fee = 0;
            if (field === 'preparation_time') businessOrderData.preparation_time = 15;
            if (field === 'asap') businessOrderData.asap = true;
            if (field === 'total') businessOrderData.total = (businessOrderData.subtotal || 0) + (businessOrderData.delivery_fee || 0) + (businessOrderData.service_fee || 0);
            if (field === 'business_id') businessOrderData.business_id = businessId;
            if (field === 'order_number') businessOrderData.order_number = orderNumber;
          });

          console.log('Added default values for missing fields:',
            missingFields.map(field => `${field}: ${businessOrderData[field]}`));
        }

        // Log all the data that will be sent to the database
        console.log(`Final business order data for ${businessId}:`, JSON.stringify(businessOrderData, null, 2));

        // Log the SQL that would be executed
        console.log(`SQL that would be executed: INSERT INTO orders (${Object.keys(businessOrderData).join(', ')}) VALUES (${Object.values(businessOrderData).map(v => typeof v === 'string' ? `'${v}'` : v).join(', ')})`);

        // Insert the order
        try {
          console.log(`Inserting order for business ${businessId} with data:`, JSON.stringify(businessOrderData, null, 2));

          // Log the exact data types for debugging
          console.log('Data type validation:');
          Object.keys(businessOrderData).forEach(key => {
            const value = businessOrderData[key];
            console.log(`  ${key}: ${typeof value} = ${value}`);
          });

          const { data: order, error: orderError } = await supabaseAdmin
            .from('orders')
            .insert(businessOrderData)
            .select('id, order_number, business_id, business_name, created_at')
            .single();

          if (orderError) {
            console.error(`❌ Error creating order for business ${businessId}:`, orderError);
            console.error('❌ Error details:', orderError.details || 'No details available');
            console.error('❌ Error hint:', orderError.hint || 'No hint available');
            console.error('❌ Error code:', orderError.code || 'No code available');
            console.error('❌ Error message:', orderError.message || 'No message available');
            console.error('❌ Full error object:', JSON.stringify(orderError, null, 2));
            console.error('❌ Data that caused the error:', JSON.stringify(businessOrderData, null, 2));

            // Log specific field validation
            console.error('❌ Field validation:');
            console.error(`  - customer_name: "${businessOrderData.customer_name}" (required: YES)`);
            console.error(`  - customer_phone: "${businessOrderData.customer_phone}" (required: YES)`);
            console.error(`  - customer_address: "${businessOrderData.customer_address}" (required: YES)`);
            console.error(`  - delivery_address: "${businessOrderData.delivery_address}" (required: YES)`);
            console.error(`  - payment_method: "${businessOrderData.payment_method}" (required: YES)`);
            console.error(`  - total: ${businessOrderData.total} (required: YES)`);
            console.error(`  - business_id: ${businessOrderData.business_id} (required: NO)`);
            console.error(`  - order_number: "${businessOrderData.order_number}" (required: NO)`);

            // Store the error for later reporting
            console.error(`❌ CRITICAL: Order creation failed for business ${businessId} (${business.business_name}). This will cause the entire multi-business order to fail.`);

            continue;
          }

          if (!order || !order.id) {
            console.error(`Failed to get order ID for business ${businessId}`);
            console.error('Response data:', order);
            continue;
          }

          const orderId = order.id;
          createdOrderIds.push(orderId);
          console.log(`Created order ${orderId} for business ${businessId}`);
          console.log(`Order details:`, JSON.stringify(order, null, 2));
        } catch (insertError) {
          console.error(`Exception inserting order for business ${businessId}:`, insertError);
          if (insertError instanceof Error) {
            console.error('Error stack:', insertError.stack);
          }
          continue;
        }

        // Find items for this business
        const businessItems = orderItems.filter(item => Number(item.business_id) === businessId);
        console.log(`Found ${businessItems.length} items for business ${businessId}`);

        // Log each item for debugging
        businessItems.forEach((item, index) => {
          console.log(`Item ${index + 1} for business ${businessId}:`, {
            product_id: item.product_id,
            product_name: item.product_name || 'Unknown Product',
            quantity: item.quantity || 1,
            price: item.price || 0,
            business_id: item.business_id
          });
        });

        if (businessItems.length > 0) {
          // Add order_id to each item
          const itemsWithOrderId = businessItems.map(item => ({
            ...item,
            order_id: order.id
          }));

          // Format items for insertion
          const formattedItems = itemsWithOrderId.map(item => {
            const formattedItem: any = {
              order_id: item.order_id,
              product_id: item.product_id || 0,
              product_name: item.product_name || 'Unknown Product',
              business_id: Number(item.business_id) || businessId,
              quantity: item.quantity || 1,
              price: item.price || 0,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            };

            if (item.notes) formattedItem.notes = item.notes;
            if (item.options) formattedItem.options = item.options;

            // Log the formatted item
            console.log(`Formatted item for order ${orderId}:`, formattedItem);

            return formattedItem;
          });

          // Insert items for this business
          console.log(`Inserting ${formattedItems.length} items for business ${businessId} in order ${orderId}`);

          try {
            console.log(`Inserting ${formattedItems.length} items for business ${businessId} in order ${orderId}`);
            console.log(`First item:`, JSON.stringify(formattedItems[0], null, 2));

            const { data: insertedItems, error: itemsError } = await supabaseAdmin
              .from('order_items')
              .insert(formattedItems)
              .select('id, product_name, quantity');

            if (itemsError) {
              console.error(`Error inserting items for business ${businessId}:`, itemsError);
              console.error('Error details:', itemsError.details || 'No details available');
              console.error('Error hint:', itemsError.hint || 'No hint available');
              console.error('Error code:', itemsError.code || 'No code available');
            } else {
              console.log(`Inserted ${formattedItems.length} items for business ${businessId}`);
              console.log(`Inserted items:`, JSON.stringify(insertedItems, null, 2));
            }
          } catch (insertError) {
            console.error(`Exception inserting items for business ${businessId}:`, insertError);
            if (insertError instanceof Error) {
              console.error('Error stack:', insertError.stack);
            }
          }
        }

        // Create initial status history entry
        try {
          const historyData = {
            order_id: orderId,
            status: 'pending',
            notes: 'Order created',
            created_at: new Date().toISOString()
          };

          const { error: historyError } = await supabaseAdmin
            .from('order_status_history')
            .insert(historyData);

          if (historyError) {
            console.error(`Error creating status history for business ${businessId}:`, historyError);
          } else {
            console.log(`Created status history for business ${businessId}`);
          }
        } catch (historyError) {
          console.error(`Exception creating status history for business ${businessId}:`, historyError);
        }
      } catch (orderError) {
        console.error(`Exception creating order for business ${businessId}:`, orderError);
      }
    }

    // Return the created order IDs
    console.log(`Successfully created ${createdOrderIds.length} orders:`, createdOrderIds);

    // If no orders were created, throw an error with details
    if (createdOrderIds.length === 0) {
      const errorMessage = `Failed to create any orders for ${orderBusinesses.length} businesses. Check the logs above for specific errors.`;
      console.error('❌ TRANSACTION FAILED:', errorMessage);
      throw new Error(errorMessage);
    }

    return createdOrderIds;
  } catch (error) {
    console.error('Error creating orders:', error);

    // Re-throw the original error
    throw error;
  }
}

/**
 * Generates a unique order number for a specific business
 * Format: B{businessId}-{YY}{MM}{DD}-{random} (e.g., B4-230701-1234)
 */
function generateBusinessSpecificOrderNumber(businessId: number): string {
  // Get current date components
  const now = new Date();
  const year = now.getFullYear().toString().slice(-2);
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');

  // Generate a random 4-digit number
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');

  // Format: B{businessId}-{YY}{MM}{DD}-{random}
  return `B${businessId}-${year}${month}${day}-${random}`;
}
