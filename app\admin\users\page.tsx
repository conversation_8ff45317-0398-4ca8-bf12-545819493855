"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/context/unified-auth-context"
import { getAuthHeaders } from "@/utils/auth-utils"
import { supabase, supabaseAdmin } from "@/lib/supabase"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import {
  AlertCircle,
  CheckCircle,
  Search,
  Shield,
  User,
  Store,
  UserCog
} from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"

interface UserData {
  id: number
  email: string
  name: string
  role: string
  created_at: string
  business_name?: string | null
}

export default function AdminUsersPage() {
  const router = useRouter()
  const { user, userProfile, isAdmin, refreshUserProfile } = useAuth()
  const [users, setUsers] = useState<UserData[]>([])
  const [businesses, setBusinesses] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState("")

  // Form state for assigning roles
  const [selectedUser, setSelectedUser] = useState<UserData | null>(null)
  const [selectedRole, setSelectedRole] = useState<string>("")
  const [selectedBusiness, setSelectedBusiness] = useState<number | null>(null)

  useEffect(() => {
    // Check if user is admin or super_admin
    if (!user) {
      router.push("/login")
      return
    }

    if (userProfile && userProfile.role !== "admin" && userProfile.role !== "super_admin") {
      router.push("/")
      return
    }

    const fetchUsers = async () => {
      try {
        // Use server API endpoint to fetch users
        console.log("Fetching users from API")

        // Get authentication headers
        const headers = await getAuthHeaders({
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        });

        // Add a timestamp to prevent caching
        const response = await fetch(`/api/admin/users-direct?t=${Date.now()}`, {
          credentials: 'include',
          headers
        })

        console.log("API response status:", response.status, response.statusText)

        let result;

        if (!response.ok) {
          console.error("API error response:", await response.json())

          // If we get a 401 or 403, redirect to login
          if (response.status === 401 || response.status === 403) {
            console.log("Authentication error, redirecting to login")
            router.push("/login?redirectTo=/admin/users")
            return
          }

          // For other errors, try the fallback endpoint
          console.log("Trying fallback endpoint due to API error")
          try {
            const fallbackResponse = await fetch(`/api/admin/users-fallback?t=${Date.now()}`)
            if (fallbackResponse.ok) {
              result = await fallbackResponse.json()
              console.log("Fallback users fetched successfully:", result)
            } else {
              console.log("Fallback endpoint also failed, using default values")
              result = { users: [] }
            }
          } catch (fallbackErr) {
            console.error("Error fetching from fallback endpoint:", fallbackErr)
            result = { users: [] }
          }
        } else {
          // If the main endpoint succeeded, use its data
          result = await response.json()
          console.log("Users fetched successfully:", result)
        }

        setUsers(result.users || [])

        // Also fetch businesses for the business manager assignment
        try {
          // Get authentication headers
          const businessHeaders = await getAuthHeaders({
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          });

          // Fetch businesses using the API
          const businessResponse = await fetch('/api/admin/businesses-direct', {
            credentials: 'include',
            headers: businessHeaders
          });

          let businessData = [];
          let businessError = null;

          if (businessResponse.ok) {
            const businessResult = await businessResponse.json();
            businessData = businessResult.businesses || [];
          } else {
            businessError = { message: 'Failed to fetch businesses' };
          }

          if (businessError) {
            console.error("Failed to load businesses:", businessError)
          } else {
            // Try to get business types separately if needed
            try {
              const enhancedBusinessData = await Promise.all(
                (businessData || []).map(async (business) => {
                  if (business.business_type_id) {
                    const { data: typeData } = await supabase
                      .from("business_types")
                      .select("name")
                      .eq("id", business.business_type_id)
                      .single()

                    return {
                      ...business,
                      business_types: typeData
                    }
                  }
                  return business
                })
              )
              setBusinesses(enhancedBusinessData || [])
            } catch (typeError) {
              console.error("Error fetching business types:", typeError)
              setBusinesses(businessData || [])
            }
          }
        } catch (businessQueryError) {
          console.error("Error in business query:", businessQueryError)
          // Continue with empty businesses array
          setBusinesses([])
        }
      } catch (err) {
        console.error("Error fetching users:", err)
        setError("An unexpected error occurred")
      } finally {
        setIsLoading(false)
      }
    }

    fetchUsers()
  }, [user, userProfile, router])

  const handleAssignRole = async () => {
    if (!selectedUser || !selectedRole) {
      setError("Please select a user and role")
      return
    }

    setError(null)
    setSuccess(null)

    try {
      let result

      // Simple direct update as fallback method
      const directUpdate = async () => {
        // Get authentication headers
        const headers = await getAuthHeaders({
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        });

        const response = await fetch('/api/admin/users-direct/update-role', {
          method: 'POST',
          credentials: 'include',
          headers,
          body: JSON.stringify({
            userId: selectedUser.id,
            role: selectedRole
          })
        })

        const result = await response.json()

        if (!response.ok) {
          return { error: { message: result.error || 'Failed to update role' } }
        }

        return { data: result.user, error: null }
      }

      try {
        if (selectedRole === "super_admin") {
          // First do a direct update to ensure the role is set
          const directResult = await directUpdate()
          if (directResult.error) {
            console.error("Direct update failed for super_admin role:", directResult.error)
            throw new Error(`Failed to update role: ${directResult.error.message}`)
          }

          // Then try the RPC as a bonus
          try {
            const rpcResult = await supabase.rpc("assign_super_admin", {
              admin_email: selectedUser.email
            })

            if (rpcResult.error) {
              console.log("RPC failed for super_admin, but direct update succeeded:", rpcResult.error)
            } else {
              console.log("RPC succeeded for super_admin role")
            }
          } catch (rpcError) {
            console.error("Exception in super_admin RPC call:", rpcError)
          }

          // Use the direct update result
          result = directResult
        } else if (selectedRole === "admin") {
          // First do a direct update to ensure the role is set
          const directResult = await directUpdate()
          if (directResult.error) {
            console.error("Direct update failed for admin role:", directResult.error)
            throw new Error(`Failed to update role: ${directResult.error.message}`)
          }

          // Then try the RPC as a bonus
          try {
            const rpcResult = await supabase.rpc("assign_admin", {
              admin_email: selectedUser.email
            })

            if (rpcResult.error) {
              console.log("RPC failed for admin, but direct update succeeded:", rpcResult.error)
            } else {
              console.log("RPC succeeded for admin role")
            }
          } catch (rpcError) {
            console.error("Exception in admin RPC call:", rpcError)
          }

          // Use the direct update result
          result = directResult
        } else if (selectedRole === "business_manager") {
          if (!selectedBusiness) {
            setError("Please select a business for the business manager")
            return
          }

          try {
            // Get authentication headers
            const headers = await getAuthHeaders({
              'Content-Type': 'application/json',
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache'
            });

            // Use the dedicated API endpoint for assigning business managers
            const response = await fetch('/api/admin/assign-business-manager', {
              method: 'POST',
              credentials: 'include',
              headers,
              body: JSON.stringify({
                userId: selectedUser.id,
                businessId: selectedBusiness
              })
            })

            const assignResult = await response.json()

            if (!response.ok) {
              console.error("Error assigning business manager:", assignResult.error)
              throw new Error(assignResult.error || 'Failed to assign business manager')
            }

            console.log("Business manager assignment successful:", assignResult)
            result = { error: null }
          } catch (err) {
            console.error("Error in business manager assignment:", err)
            throw err
          }
        } else if (selectedRole === "business_staff") {
          if (!selectedBusiness) {
            setError("Please select a business for the staff member")
            return
          }

          try {
            // First, update the user role directly using the API
            const response = await fetch('/api/admin/users/update-role', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                userId: selectedUser.id,
                role: selectedRole
              })
            })

            const roleResult = await response.json()

            if (!response.ok) {
              throw new Error(roleResult.error || 'Failed to update role')
            }

            // Check if business_staff table exists
            try {
              const { data: tables, error: tableError } = await supabase
                .from("information_schema.tables")
                .select("table_name")
                .eq("table_name", "business_staff")

              // If table doesn't exist or there's an error, try to create it
              if (tableError || !tables || tables.length === 0) {
                console.log("business_staff table doesn't exist, attempting to create it")
                try {
                  await supabase.rpc("create_business_staff_table")
                  console.log("Created business_staff table")
                } catch (createError) {
                  console.error("Failed to create business_staff table:", createError)
                  // Continue anyway, as the table might actually exist
                }
              }
            } catch (tableCheckError) {
              console.error("Error checking for business_staff table:", tableCheckError)
              // Continue anyway, as the table might actually exist
            }

            // Check if business_staff entry exists
            try {
              // First check if the table exists
              const { data: tables, error: tableError } = await supabase
                .from("information_schema.tables")
                .select("table_name")
                .eq("table_name", "business_staff")
                .maybeSingle()

              // If table doesn't exist, we'll skip the check and just note that we need to create it later
              if (tableError || !tables) {
                console.log("business_staff table doesn't exist or can't be queried, will create it later if needed")
              } else {
                // Table exists, so check for existing entry
                try {
                  const { data: existingEntry, error: entryError } = await supabase
                    .from("business_staff")
                    .select("*")
                    .eq("user_id", selectedUser.id)
                    .eq("business_id", selectedBusiness)
                    .maybeSingle()

                  // If no entry exists, create one
                  if (!existingEntry && !entryError) {
                    console.log("No existing business staff entry, creating one")
                    try {
                      const linkResult = await supabase
                        .from("business_staff")
                        .insert([
                          {
                            user_id: selectedUser.id,
                            business_id: selectedBusiness
                          }
                        ])

                      if (linkResult.error) {
                        console.error("Failed to insert business staff entry:", linkResult.error)
                      } else {
                        console.log("Successfully created business staff entry")
                      }
                    } catch (insertError) {
                      console.error("Exception inserting business staff entry:", insertError)
                      // Continue anyway, as the role update is more important
                    }
                  } else if (entryError) {
                    console.log("Error checking for existing business staff entry, but continuing:", entryError)
                    // Don't throw an error, just log it and continue
                  } else {
                    console.log("Business staff entry already exists")
                  }
                } catch (checkError) {
                  // Log but don't throw, as we want the role update to succeed regardless
                  console.log("Exception checking for business staff entry, but continuing:", checkError)
                }
              }
            } catch (outerError) {
              // Log but don't throw, as we want the role update to succeed regardless
              console.log("Exception in business staff table check, but continuing:", outerError)
            }

            // Try to call the assign_business_staff function as a backup
            try {
              const rpcResult = await supabase.rpc("assign_business_staff", {
                staff_email: selectedUser.email,
                business_id: selectedBusiness
              })

              if (rpcResult.error) {
                console.log("RPC call failed, but direct updates were already attempted:", rpcResult.error)
              } else {
                console.log("RPC call succeeded")
              }
            } catch (rpcError) {
              console.error("Exception in RPC call:", rpcError)
              // We already did the direct updates, so this is just a bonus attempt
            }

            result = { error: null }
          } catch (err) {
            console.error("Error in business staff assignment:", err)
            throw err
          }
        } else {
          // For customer role, just update directly
          result = await directUpdate()
        }
      } catch (rpcError) {
        console.error("Error in RPC call:", rpcError)
        // Try direct update as last resort
        result = await directUpdate()
      }

      if (result.error) {
        setError("Failed to assign role: " + result.error.message)
        return
      }

      // Refresh the user list
      try {
        console.log("Refreshing user list")

        // Get authentication headers
        const headers = await getAuthHeaders({
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        });

        // Add a timestamp to prevent caching
        const response = await fetch(`/api/admin/users-direct?t=${Date.now()}`, {
          credentials: 'include',
          headers
        })

        console.log("API response status:", response.status, response.statusText)

        let result;

        if (!response.ok) {
          console.error("API error response:", await response.json())

          // For errors, try the fallback endpoint
          console.log("Trying fallback endpoint due to API error")
          try {
            const fallbackResponse = await fetch(`/api/admin/users-fallback?t=${Date.now()}`)
            if (fallbackResponse.ok) {
              result = await fallbackResponse.json()
              console.log("Fallback users fetched successfully:", result)
            } else {
              console.log("Fallback endpoint also failed, using default values")
              result = { users: [] }
            }
          } catch (fallbackErr) {
            console.error("Error fetching from fallback endpoint:", fallbackErr)
            result = { users: [] }
          }
        } else {
          // If the main endpoint succeeded, use its data
          result = await response.json()
          console.log("Users refreshed successfully:", result)
        }

        setUsers(result.users || [])
      } catch (refreshError) {
        console.error("Error refreshing users:", refreshError)
      }

      setSuccess(`Successfully assigned ${selectedRole} role to ${selectedUser.name}`)

      // Reset form
      setSelectedUser(null)
      setSelectedRole("")
      setSelectedBusiness(null)

      // If the current user was updated, refresh their profile
      if (user && selectedUser.email === user.email) {
        await refreshUserProfile()
      }
    } catch (err) {
      console.error("Error assigning role:", err)
      setError("An unexpected error occurred")
    }
  }

  const getRoleBadge = (role: string) => {
    switch (role) {
      case "super_admin":
        return <Badge className="bg-purple-100 text-purple-800 border-purple-200">Super Admin</Badge>
      case "admin":
        return <Badge className="bg-red-100 text-red-800 border-red-200">Admin</Badge>
      case "business_manager":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Business Manager</Badge>
      case "business_staff":
        return <Badge className="bg-green-100 text-green-800 border-green-200">Business Staff</Badge>
      case "customer":
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Customer</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">{role || "No Role"}</Badge>
    }
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case "super_admin":
        return <Shield className="h-4 w-4 text-purple-600" />
      case "admin":
        return <Shield className="h-4 w-4 text-red-600" />
      case "business_manager":
        return <Store className="h-4 w-4 text-blue-600" />
      case "business_staff":
        return <UserCog className="h-4 w-4 text-green-600" />
      case "customer":
        return <User className="h-4 w-4 text-gray-600" />
      default:
        return <User className="h-4 w-4 text-gray-600" />
    }
  }

  const filteredUsers = users.filter(user => {
    if (!searchQuery) return true

    const query = searchQuery.toLowerCase()
    return (
      user.name?.toLowerCase().includes(query) ||
      user.email?.toLowerCase().includes(query) ||
      user.role?.toLowerCase().includes(query) ||
      (user.business_name && user.business_name.toLowerCase().includes(query))
    )
  })

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading users...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">User Management</h1>
          <p className="text-gray-500">Manage user roles and permissions</p>
        </div>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="mb-6 bg-green-50 border-green-200">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertTitle>Success</AlertTitle>
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Users</CardTitle>
              <CardDescription>Manage user roles and permissions</CardDescription>
              <div className="mt-4 relative">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search users..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-8"
                />
              </div>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Business</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredUsers.length > 0 ? (
                      filteredUsers.map((user) => (
                        <TableRow key={user.id}>
                          <TableCell>{user.name || "N/A"}</TableCell>
                          <TableCell>{user.email}</TableCell>
                          <TableCell className="flex items-center">
                            {getRoleIcon(user.role)}
                            <span className="ml-2">{getRoleBadge(user.role)}</span>
                          </TableCell>
                          <TableCell>
                            {user.business_name ? (
                              <span className="text-blue-600">{user.business_name}</span>
                            ) : (
                              <span className="text-gray-400 italic">None</span>
                            )}
                          </TableCell>
                          <TableCell>{new Date(user.created_at).toLocaleDateString()}</TableCell>
                          <TableCell>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setSelectedUser(user)}
                            >
                              Assign Role
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-4 text-gray-500">
                          {searchQuery ? "No users match your search" : "No users found"}
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Assign Role</CardTitle>
              <CardDescription>
                {selectedUser
                  ? `Assign a role to ${selectedUser.name || selectedUser.email}`
                  : "Select a user to assign a role"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {selectedUser ? (
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="role">Role</Label>
                    <Select value={selectedRole} onValueChange={setSelectedRole}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a role" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="customer">Customer</SelectItem>
                        <SelectItem value="business_staff">Business Staff</SelectItem>
                        <SelectItem value="business_manager">Business Manager</SelectItem>
                        <SelectItem value="admin">Admin</SelectItem>
                        <SelectItem value="super_admin">Super Admin</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {(selectedRole === "business_manager" || selectedRole === "business_staff") && (
                    <div>
                      <Label htmlFor="business">Business</Label>
                      <Select
                        value={selectedBusiness?.toString() || ""}
                        onValueChange={(value) => setSelectedBusiness(parseInt(value))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select a business" />
                        </SelectTrigger>
                        <SelectContent>
                          {businesses.map((business) => (
                            <SelectItem key={business.id} value={business.id.toString()}>
                              {business.name} ({business.business_types?.name || "Business"})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  <Button
                    className="w-full bg-emerald-600 hover:bg-emerald-700 mt-4"
                    onClick={handleAssignRole}
                  >
                    Assign Role
                  </Button>
                </div>
              ) : (
                <div className="text-center py-6 text-gray-500">
                  Select a user from the table to assign a role
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
