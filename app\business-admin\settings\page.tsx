"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useAuthDirect } from "@/context/auth-context-direct"
import { supabase } from "@/lib/supabase"
import { Loader2, Save, Info, MapPin, DollarSign, Clock, Users, Truck } from "lucide-react"
import type { OpeningHours } from "@/types/business"
import { useToast } from "@/components/ui/use-toast"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Select, Select<PERSON>ontent, SelectI<PERSON>, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Checkbox } from "@/components/ui/checkbox"
import FileUpload from "@/components/file-upload"

// Define delivery fee model options
const DELIVERY_FEE_MODELS = [
  { id: "fixed", name: "Fixed Fee" },
  { id: "distance", name: "Distance-Based" },
  { id: "mixed", name: "Combination (Fixed + Distance)" }
]

// Define cuisine types
const CUISINE_TYPES = [
  { id: "british", name: "British" },
  { id: "italian", name: "Italian" },
  { id: "indian", name: "Indian" },
  { id: "chinese", name: "Chinese" },
  { id: "thai", name: "Thai" },
  { id: "japanese", name: "Japanese" },
  { id: "mexican", name: "Mexican" },
  { id: "french", name: "French" },
  { id: "spanish", name: "Spanish" },
  { id: "mediterranean", name: "Mediterranean" },
  { id: "lebanese", name: "Lebanese" },
  { id: "turkish", name: "Turkish" },
  { id: "greek", name: "Greek" },
  { id: "american", name: "American" },
  { id: "bbq", name: "BBQ" },
  { id: "burger", name: "Burger" },
  { id: "pizza", name: "Pizza" },
  { id: "seafood", name: "Seafood" },
  { id: "sushi", name: "Sushi" },
  { id: "vegan", name: "Vegan" },
  { id: "vegetarian", name: "Vegetarian" },
  { id: "dessert", name: "Dessert" },
  { id: "cafe", name: "Café" },
  { id: "bakery", name: "Bakery" },
  { id: "pub_food", name: "Pub Food" },
  { id: "fast_food", name: "Fast Food" },
  { id: "healthy", name: "Healthy Food" },
  { id: "breakfast", name: "Breakfast" },
  { id: "lunch", name: "Lunch" },
  { id: "dinner", name: "Dinner" },
  { id: "other", name: "Other" }
]

// Define business categories
const BUSINESS_CATEGORIES = [
  { id: "restaurant", name: "Restaurant" },
  { id: "cafe", name: "Café" },
  { id: "bakery", name: "Bakery" },
  { id: "pub", name: "Pub" },
  { id: "bar", name: "Bar" },
  { id: "takeaway", name: "Takeaway" },
  { id: "fast_food", name: "Fast Food" },
  { id: "food_truck", name: "Food Truck" },
  { id: "grocery", name: "Grocery Store" },
  { id: "convenience", name: "Convenience Store" },
  { id: "butcher", name: "Butcher" },
  { id: "fishmonger", name: "Fishmonger" },
  { id: "greengrocer", name: "Greengrocer" },
  { id: "deli", name: "Delicatessen" },
  { id: "health_food", name: "Health Food Store" },
  { id: "other", name: "Other" }
]

export default function BusinessSettings() {
  const { toast } = useToast()
  const router = useRouter()
  const { user, userProfile, isAdmin, isSuperAdmin } = useAuthDirect()

  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [geocoding, setGeocoding] = useState(false)
  const [business, setBusiness] = useState<any>(null)
  const [businessId, setBusinessId] = useState<number | null>(null)
  const [managers, setManagers] = useState<any[]>([])
  const [staff, setStaff] = useState<any[]>([])
  const [approvalStatus, setApprovalStatus] = useState<any>(null)

  const [formData, setFormData] = useState<{
    name: string;
    description: string;
    address: string;
    postcode: string;
    location: string;
    phone: string;
    email: string;
    delivery_radius: number;
    preparation_time_minutes: number;
    minimum_order_amount: number;
    delivery_fee: number;
    delivery_fee_model: string;
    delivery_fee_per_km: number;
    coordinates: string;
    logo_url: string;
    banner_url: string;
    hygiene_rating: string;
    allergens_info: string;
    categories: string;
    attributes: string[];
    opening_hours: OpeningHours;
  }>({
    name: "",
    description: "",
    address: "",
    postcode: "",
    location: "",
    phone: "",
    email: "",
    delivery_radius: 5,
    preparation_time_minutes: 15,
    minimum_order_amount: 15.00,
    delivery_fee: 2.50,
    delivery_fee_model: "fixed",
    delivery_fee_per_km: 0.50,
    coordinates: "",
    logo_url: "",
    banner_url: "",
    hygiene_rating: "",
    allergens_info: "",
    categories: "",
    attributes: [] as string[],
    opening_hours: {
      monday: { open: "09:00", close: "17:00" },
      tuesday: { open: "09:00", close: "17:00" },
      wednesday: { open: "09:00", close: "17:00" },
      thursday: { open: "09:00", close: "17:00" },
      friday: { open: "09:00", close: "17:00" },
      saturday: { open: "10:00", close: "16:00" },
      sunday: { open: "11:00", close: "15:00" }
    }
  })

  // Fetch business data using the server API
  useEffect(() => {
    async function fetchBusinessData() {
      if (!user) return

      try {
        setLoading(true)

        console.log("Current user:", user)
        console.log("User profile:", userProfile)
        console.log("Fetching business data from server API")

        // Get the authentication token from localStorage
        const token = localStorage.getItem('loop_jersey_auth_token') || '';

        // Check if we have a business ID in the URL query parameters
        const urlParams = new URLSearchParams(window.location.search);
        const urlBusinessId = urlParams.get('businessId');

        console.log("URL parameters:", { urlBusinessId });

        // Construct the API URL with businessId parameter if available
        let apiUrl = '/api/business-admin/settings-data';
        if (urlBusinessId) {
          apiUrl += `?businessId=${urlBusinessId}`;
        }

        console.log("Fetching from API URL:", apiUrl);

        // Use the server API to fetch business data (bypasses RLS issues)
        const response = await fetch(apiUrl, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': token ? `Bearer ${token}` : '',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        });

        if (!response.ok) {
          // If we get a 401 or 403, redirect to login
          if (response.status === 401 || response.status === 403) {
            console.log("Authentication error, redirecting to login")
            router.push("/login?redirectTo=/business-admin/settings")
            return
          }

          const errorData = await response.json()
          console.error("API error response:", errorData)

          toast({
            variant: "destructive",
            title: "Error",
            description: errorData.error || "Could not load business data"
          })

          return
        }

        const data = await response.json()
        console.log("Business data from API:", data)

        if (data.business) {
          setBusinessId(data.business.id)
          setBusiness(data.business)

          // Format coordinates for display
          let coordinatesStr = ""
          if (data.business.coordinates) {
            try {
              // Coordinates are stored as a PostgreSQL POINT type, which is a string like "(lng,lat)"
              const coordsMatch = data.business.coordinates.match(/\(([^)]+)\)/)
              if (coordsMatch && coordsMatch[1]) {
                const [lng, lat] = coordsMatch[1].split(',').map(parseFloat)
                coordinatesStr = `${lat},${lng}`
              }
            } catch (e) {
              console.error("Error parsing coordinates:", e)
            }
          }

          // Handle allergens_info field which might be named allergen_info in the database
          const allergensInfo = data.business.allergens_info || data.business.allergen_info || "";
          console.log("Allergens info from database:", {
            allergens_info: data.business.allergens_info,
            allergen_info: data.business.allergen_info,
            using: allergensInfo
          });

          // Set form data from business data
          setFormData({
            name: data.business.name || "",
            description: data.business.description || "",
            address: data.business.address || "",
            postcode: data.business.postcode || "",
            location: data.business.location || "",
            phone: data.business.phone || "",
            email: data.business.email || "",
            delivery_radius: data.business.delivery_radius || 5,
            preparation_time_minutes: data.business.preparation_time_minutes || 15,
            minimum_order_amount: data.business.minimum_order_amount || 15.00,
            delivery_fee: data.business.delivery_fee || 2.50,
            delivery_fee_model: data.business.delivery_fee_model || "fixed",
            delivery_fee_per_km: data.business.delivery_fee_per_km || 0.50,
            coordinates: coordinatesStr,
            logo_url: data.business.logo_url || "",
            banner_url: data.business.banner_url || "",
            hygiene_rating: data.business.hygiene_rating || "",
            allergens_info: allergensInfo,
            categories: data.business.categories || "",
            attributes: data.business.attributes || [],
            opening_hours: data.business.opening_hours || {
              monday: { open: "09:00", close: "17:00" },
              tuesday: { open: "09:00", close: "17:00" },
              wednesday: { open: "09:00", close: "17:00" },
              thursday: { open: "09:00", close: "17:00" },
              friday: { open: "09:00", close: "17:00" },
              saturday: { open: "10:00", close: "16:00" },
              sunday: { open: "11:00", close: "15:00" }
            }
          })

          // Set managers and staff from API response
          if (data.managers) {
            setManagers(data.managers)
          }

          if (data.staff) {
            setStaff(data.staff)
          }

          // Set approval status information
          if (data.approvalStatus) {
            setApprovalStatus(data.approvalStatus)
            console.log("Approval status:", data.approvalStatus)
          }
        } else {
          // Display a proper error message
          toast({
            variant: "destructive",
            title: "Business Not Found",
            description: "We couldn't find your business information. Please contact support for assistance."
          })

          // Redirect to home page after a short delay
          setTimeout(() => {
            router.push('/')
          }, 3000)
        }
      } catch (error) {
        console.error("Error in fetchBusinessData:", error)
        toast({
          variant: "destructive",
          title: "Error",
          description: "An unexpected error occurred. Please try again."
        })
      } finally {
        setLoading(false)
      }
    }

    fetchBusinessData()
  }, [user, userProfile, router])

  // Fetch business managers
  async function fetchBusinessManagers(businessId: number) {
    try {
      console.log("Fetching business managers for business ID:", businessId)

      // First try with the join
      const { data, error } = await supabase
        .from('business_managers')
        .select('*, users(id, name, email, role)')
        .eq('business_id', businessId)

      if (error) {
        console.error("Error fetching business managers with join:", error)

        // Try without the join
        const { data: basicData, error: basicError } = await supabase
          .from('business_managers')
          .select('*')
          .eq('business_id', businessId)

        if (basicError) {
          console.error("Error fetching basic business managers:", basicError)
          return
        }

        if (basicData && basicData.length > 0) {
          console.log("Found basic manager data:", basicData)

          // Now fetch user details separately
          const userIds = basicData.map(manager => manager.user_id)

          const { data: userData, error: userError } = await supabase
            .from('users')
            .select('id, name, email, role')
            .in('id', userIds)

          if (userError) {
            console.error("Error fetching user details for managers:", userError)
            setManagers(basicData) // Use basic data without user details
            return
          }

          // Combine the data
          const combinedData = basicData.map(manager => {
            const user = userData.find(u => u.id === manager.user_id)
            return {
              ...manager,
              users: user
            }
          })

          setManagers(combinedData)
          return
        }
      }

      if (data) {
        console.log("Found business managers with join:", data)
        setManagers(data)
      }
    } catch (error) {
      console.error("Error in fetchBusinessManagers:", error)
    }
  }

  // Fetch business staff
  async function fetchBusinessStaff(businessId: number) {
    try {
      console.log("Fetching business staff for business ID:", businessId)

      // First try with the join
      const { data, error } = await supabase
        .from('business_staff')
        .select('*, users(id, name, email, role)')
        .eq('business_id', businessId)

      if (error) {
        console.error("Error fetching business staff with join:", error)

        // Try without the join
        const { data: basicData, error: basicError } = await supabase
          .from('business_staff')
          .select('*')
          .eq('business_id', businessId)

        if (basicError) {
          console.error("Error fetching basic business staff:", basicError)
          return
        }

        if (basicData && basicData.length > 0) {
          console.log("Found basic staff data:", basicData)

          // Now fetch user details separately
          const userIds = basicData.map(staff => staff.user_id)

          const { data: userData, error: userError } = await supabase
            .from('users')
            .select('id, name, email, role')
            .in('id', userIds)

          if (userError) {
            console.error("Error fetching user details for staff:", userError)
            setStaff(basicData) // Use basic data without user details
            return
          }

          // Combine the data
          const combinedData = basicData.map(staff => {
            const user = userData.find(u => u.id === staff.user_id)
            return {
              ...staff,
              users: user
            }
          })

          setStaff(combinedData)
          return
        }
      }

      if (data) {
        console.log("Found business staff with join:", data)
        setStaff(data)
      }
    } catch (error) {
      console.error("Error in fetchBusinessStaff:", error)
    }
  }

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  // Handle number input changes
  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    const numValue = parseFloat(value)
    if (!isNaN(numValue)) {
      setFormData(prev => ({ ...prev, [name]: numValue }))
    }
  }

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    // Special handling for numeric fields that need to be stored as numbers
    if (name === 'hygiene_rating' && value && !isNaN(Number(value))) {
      console.log(`Converting ${name} value from string "${value}" to number ${Number(value)}`)
      // Use type assertion to handle the type mismatch
      setFormData(prev => ({ ...prev, [name]: value }))
    } else {
      setFormData(prev => ({ ...prev, [name]: value }))
    }
  }

  // Geocode address to get coordinates
  const geocodeAddress = async () => {
    if (!formData.address || !formData.postcode) {
      toast({
        variant: "destructive",
        title: "Missing Information",
        description: "Please enter both address and postcode to geocode."
      })
      return
    }

    setGeocoding(true)

    try {
      // Construct the full address
      const fullAddress = `${formData.address}, ${formData.postcode}, Jersey`

      // Use Nominatim OpenStreetMap API for geocoding
      const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(fullAddress)}`)
      const data = await response.json()

      if (data && data.length > 0) {
        const { lat, lon } = data[0]
        setFormData(prev => ({ ...prev, coordinates: `${lat},${lon}` }))

        toast({
          title: "Address Geocoded",
          description: "Coordinates have been updated successfully."
        })
      } else {
        toast({
          variant: "destructive",
          title: "Geocoding Failed",
          description: "Could not find coordinates for this address. Please check and try again."
        })
      }
    } catch (error) {
      console.error("Error geocoding address:", error)
      toast({
        variant: "destructive",
        title: "Geocoding Error",
        description: "An error occurred while geocoding the address."
      })
    } finally {
      setGeocoding(false)
    }
  }

  // Save business settings using the server API
  const handleSave = async () => {
    if (!business || !businessId) {
      toast({
        variant: "destructive",
        title: "Error saving settings",
        description: "Business data not found. Please refresh the page and try again."
      })
      return
    }

    setSaving(true)

    try {
      // Parse coordinates
      let coordinates = null
      if (formData.coordinates) {
        const [lat, lng] = formData.coordinates.split(',').map(parseFloat)
        if (!isNaN(lat) && !isNaN(lng)) {
          // Format as PostgreSQL POINT type
          coordinates = `(${lng},${lat})`
        }
      }

      // Create a copy of formData with the formatted coordinates
      const dataToSave = {
        ...formData,
        coordinates
      }

      console.log("Saving business settings with data:", {
        businessId,
        coordinatesFormatted: coordinates,
        hygiene_rating: formData.hygiene_rating
      })

      // Get the authentication token from localStorage
      const token = localStorage.getItem('loop_jersey_auth_token') || '';

      // Check if we have a business ID in the URL query parameters
      const urlParams = new URLSearchParams(window.location.search);
      const urlBusinessId = urlParams.get('businessId');

      // Construct the API URL with businessId parameter if available
      let apiUrl = '/api/business-admin/settings-data';
      if (urlBusinessId) {
        apiUrl += `?businessId=${urlBusinessId}`;
      }

      console.log("Saving to API URL:", apiUrl);

      // Use the server API to update business settings
      const response = await fetch(apiUrl, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        },
        body: JSON.stringify(dataToSave)
      })

      // Get the response text first to ensure we can parse it
      const responseText = await response.text()

      // Try to parse the response as JSON
      let errorData: { error?: string, details?: any } = {}
      let result: { business?: any, message?: string } = {}

      try {
        if (responseText) {
          result = JSON.parse(responseText)
          if (!response.ok) {
            errorData = result as { error?: string, details?: any }
          }
        }
      } catch (parseError) {
        console.error("Error parsing response:", parseError)
        console.log("Raw response:", responseText)
        errorData = { error: "Invalid response from server" }
      }

      if (!response.ok) {
        console.error("API error response:", errorData)

        toast({
          variant: "destructive",
          title: "Error",
          description: errorData.error || "Failed to save settings. Please try again."
        })

        // Show detailed error in console
        if (errorData.details) {
          console.error("Error details:", errorData.details)
        }

        return
      }

      if (result.business) {
        setBusiness(result.business)
        toast({
          title: "Settings Saved",
          description: "Your business settings have been updated successfully."
        })
      } else {
        console.warn("No business data returned in successful response")
      }
    } catch (error) {
      console.error("Error saving business settings:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred. Please try again."
      })
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-emerald-600" />
      </div>
    )
  }

  return (
    <div>
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">Business Settings</h1>
          <p className="text-gray-500">
            Welcome, {userProfile?.name || user?.email || "User"} | Manage your business profile and settings
          </p>
        </div>
        <div className="mt-4 md:mt-0">
          <Button
            className="bg-emerald-600 hover:bg-emerald-700"
            onClick={handleSave}
            disabled={saving}
          >
            {saving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="general">
        <TabsList className="mb-4">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="location">Location & Delivery</TabsTrigger>
          <TabsTrigger value="brand-assets">Brand Assets</TabsTrigger>
          <TabsTrigger value="team">Team Management</TabsTrigger>
          <TabsTrigger value="business-hours">Business Hours</TabsTrigger>
          <TabsTrigger value="status">Status & Info</TabsTrigger>
        </TabsList>

        <TabsContent value="general">
          <Card>
            <CardHeader>
              <CardTitle>General Information</CardTitle>
              <CardDescription>
                Update your business's basic information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Business Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    placeholder="Business Name"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    placeholder="Phone Number"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  placeholder="Email Address"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Business Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  placeholder="Describe your business"
                  rows={4}
                />
                <p className="text-sm text-gray-500">
                  A brief description of your business that will be displayed to customers
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="hygiene_rating">Food Hygiene Rating</Label>
                <Select
                  value={formData.hygiene_rating?.toString() || ""}
                  onValueChange={(value) => {
                    // Special handling for hygiene rating to ensure proper type conversion
                    console.log("Setting hygiene rating to value:", value);

                    // Just set the string value directly to avoid type issues
                    setFormData(prev => ({ ...prev, hygiene_rating: value }));
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select hygiene rating" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5 - Excellent</SelectItem>
                    <SelectItem value="4">4 - Very Good</SelectItem>
                    <SelectItem value="3">3 - Good</SelectItem>
                    <SelectItem value="2">2 - Improvement Needed</SelectItem>
                    <SelectItem value="1">1 - Major Improvement Needed</SelectItem>
                    <SelectItem value="0">0 - Urgent Improvement Needed</SelectItem>
                    <SelectItem value="exempt">Exempt / Awaiting Inspection</SelectItem>
                    <SelectItem value="awaiting">Awaiting Publication</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-sm text-gray-500">
                  Your official food hygiene rating from <a href="https://www.gov.je/Industry/RetailHospitality/FoodDrink/pages/eatsaferatings.aspx" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">gov.je Eat Safe ratings</a>
                </p>
                {business?.hygiene_rating && (
                  <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded-md">
                    <p className="text-sm text-green-800">
                      Current rating in database: <span className="font-bold">{business.hygiene_rating}</span>
                      <span className="ml-2 text-xs text-gray-600">(Type: {typeof business.hygiene_rating})</span>
                    </p>
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="allergens_info">Allergens Information</Label>
                <Textarea
                  id="allergens_info"
                  name="allergens_info"
                  value={formData.allergens_info || ""}
                  onChange={handleChange}
                  placeholder="Information about allergens in your products"
                  rows={3}
                />
                <p className="text-sm text-gray-500">
                  Important information about allergens that customers should know
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="business_categories">Business Categories</Label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2">
                  {BUSINESS_CATEGORIES.map((category) => (
                    <div key={category.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`category_${category.id}`}
                        checked={formData.categories?.includes(category.id) || false}
                        onCheckedChange={(checked) => {
                          const newCategories = [...(formData.categories?.split(',').filter(Boolean) || [])];
                          if (checked) {
                            if (!newCategories.includes(category.id)) {
                              newCategories.push(category.id);
                            }
                          } else {
                            const index = newCategories.indexOf(category.id);
                            if (index !== -1) {
                              newCategories.splice(index, 1);
                            }
                          }
                          setFormData({...formData, categories: newCategories.join(',')});
                        }}
                      />
                      <Label htmlFor={`category_${category.id}`} className="font-normal">{category.name}</Label>
                    </div>
                  ))}
                </div>
                <p className="text-sm text-gray-500">
                  Categories that describe your business
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="cuisine_types">Cuisine Types</Label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2 max-h-60 overflow-y-auto border rounded-md p-2">
                  {CUISINE_TYPES.map((cuisine) => (
                    <div key={cuisine.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`cuisine_${cuisine.id}`}
                        checked={formData.attributes?.includes(cuisine.id) || false}
                        onCheckedChange={(checked) => {
                          const newAttributes = [...(formData.attributes || [])];
                          if (checked) {
                            if (!newAttributes.includes(cuisine.id)) {
                              newAttributes.push(cuisine.id);
                            }
                          } else {
                            const index = newAttributes.indexOf(cuisine.id);
                            if (index !== -1) {
                              newAttributes.splice(index, 1);
                            }
                          }
                          setFormData({...formData, attributes: newAttributes});
                        }}
                      />
                      <Label htmlFor={`cuisine_${cuisine.id}`} className="font-normal">{cuisine.name}</Label>
                    </div>
                  ))}
                </div>
                <p className="text-sm text-gray-500">
                  Cuisine types offered by your business
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="business_attributes">Business Features</Label>
                <div className="grid grid-cols-2 gap-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="attr_vegetarian"
                      checked={formData.attributes?.includes("vegetarian_options") || false}
                      onCheckedChange={(checked) => {
                        const newAttributes = [...(formData.attributes || [])];
                        if (checked) {
                          if (!newAttributes.includes("vegetarian_options")) {
                            newAttributes.push("vegetarian_options");
                          }
                        } else {
                          const index = newAttributes.indexOf("vegetarian_options");
                          if (index !== -1) {
                            newAttributes.splice(index, 1);
                          }
                        }
                        setFormData({...formData, attributes: newAttributes});
                      }}
                    />
                    <Label htmlFor="attr_vegetarian" className="font-normal">Vegetarian Options</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="attr_vegan"
                      checked={formData.attributes?.includes("vegan_options") || false}
                      onCheckedChange={(checked) => {
                        const newAttributes = [...(formData.attributes || [])];
                        if (checked) {
                          if (!newAttributes.includes("vegan_options")) {
                            newAttributes.push("vegan_options");
                          }
                        } else {
                          const index = newAttributes.indexOf("vegan_options");
                          if (index !== -1) {
                            newAttributes.splice(index, 1);
                          }
                        }
                        setFormData({...formData, attributes: newAttributes});
                      }}
                    />
                    <Label htmlFor="attr_vegan" className="font-normal">Vegan Options</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="attr_gluten_free"
                      checked={formData.attributes?.includes("gluten_free_options") || false}
                      onCheckedChange={(checked) => {
                        const newAttributes = [...(formData.attributes || [])];
                        if (checked) {
                          if (!newAttributes.includes("gluten_free_options")) {
                            newAttributes.push("gluten_free_options");
                          }
                        } else {
                          const index = newAttributes.indexOf("gluten_free_options");
                          if (index !== -1) {
                            newAttributes.splice(index, 1);
                          }
                        }
                        setFormData({...formData, attributes: newAttributes});
                      }}
                    />
                    <Label htmlFor="attr_gluten_free" className="font-normal">Gluten-Free Options</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="attr_halal"
                      checked={formData.attributes?.includes("halal_options") || false}
                      onCheckedChange={(checked) => {
                        const newAttributes = [...(formData.attributes || [])];
                        if (checked) {
                          if (!newAttributes.includes("halal_options")) {
                            newAttributes.push("halal_options");
                          }
                        } else {
                          const index = newAttributes.indexOf("halal_options");
                          if (index !== -1) {
                            newAttributes.splice(index, 1);
                          }
                        }
                        setFormData({...formData, attributes: newAttributes});
                      }}
                    />
                    <Label htmlFor="attr_halal" className="font-normal">Halal Options</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="attr_outdoor_seating"
                      checked={formData.attributes?.includes("outdoor_seating") || false}
                      onCheckedChange={(checked) => {
                        const newAttributes = [...(formData.attributes || [])];
                        if (checked) {
                          if (!newAttributes.includes("outdoor_seating")) {
                            newAttributes.push("outdoor_seating");
                          }
                        } else {
                          const index = newAttributes.indexOf("outdoor_seating");
                          if (index !== -1) {
                            newAttributes.splice(index, 1);
                          }
                        }
                        setFormData({...formData, attributes: newAttributes});
                      }}
                    />
                    <Label htmlFor="attr_outdoor_seating" className="font-normal">Outdoor Seating</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="attr_wifi"
                      checked={formData.attributes?.includes("wifi") || false}
                      onCheckedChange={(checked) => {
                        const newAttributes = [...(formData.attributes || [])];
                        if (checked) {
                          if (!newAttributes.includes("wifi")) {
                            newAttributes.push("wifi");
                          }
                        } else {
                          const index = newAttributes.indexOf("wifi");
                          if (index !== -1) {
                            newAttributes.splice(index, 1);
                          }
                        }
                        setFormData({...formData, attributes: newAttributes});
                      }}
                    />
                    <Label htmlFor="attr_wifi" className="font-normal">Free WiFi</Label>
                  </div>
                </div>
                <p className="text-sm text-gray-500">
                  Special features and attributes of your business
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="location">
          <Card>
            <CardHeader>
              <CardTitle>Location & Delivery Settings</CardTitle>
              <CardDescription>
                Manage your business's address, delivery area, and preparation time
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Address Information</h3>

                <div className="space-y-2">
                  <Label htmlFor="address">Street Address</Label>
                  <Textarea
                    id="address"
                    name="address"
                    value={formData.address}
                    onChange={handleChange}
                    placeholder="Street address (e.g. 1 Castle St, St Helier)"
                    rows={2}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="postcode">Postcode</Label>
                    <Input
                      id="postcode"
                      name="postcode"
                      value={formData.postcode}
                      onChange={handleChange}
                      placeholder="JE2 3NN"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="location">Location/Area</Label>
                    <Input
                      id="location"
                      name="location"
                      value={formData.location}
                      onChange={handleChange}
                      placeholder="e.g. St Helier"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="coordinates">Coordinates (latitude,longitude)</Label>
                  <div className="flex space-x-2">
                    <Input
                      id="coordinates"
                      name="coordinates"
                      value={formData.coordinates}
                      onChange={handleChange}
                      placeholder="49.1858,-2.1039"
                      className="flex-1"
                    />
                    <Button
                      variant="outline"
                      onClick={geocodeAddress}
                      disabled={geocoding}
                    >
                      {geocoding ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Geocoding...
                        </>
                      ) : (
                        <>
                          <MapPin className="mr-2 h-4 w-4" />
                          Geocode
                        </>
                      )}
                    </Button>
                  </div>
                  <p className="text-sm text-gray-500">
                    Click "Geocode" to automatically get coordinates from your address, or enter them manually.
                  </p>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Delivery Settings</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="delivery_radius">Delivery Radius (km)</Label>
                    <Input
                      id="delivery_radius"
                      name="delivery_radius"
                      type="number"
                      min="0"
                      step="0.1"
                      value={formData.delivery_radius}
                      onChange={handleNumberChange}
                      placeholder="5.0"
                    />
                    <p className="text-sm text-gray-500">
                      Maximum distance you'll deliver from your location
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="preparation_time_minutes">Preparation Time (minutes)</Label>
                    <Input
                      id="preparation_time_minutes"
                      name="preparation_time_minutes"
                      type="number"
                      min="0"
                      value={formData.preparation_time_minutes}
                      onChange={handleNumberChange}
                      placeholder="15"
                    />
                    <p className="text-sm text-gray-500">
                      Average time to prepare an order
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="delivery_fee_model">Delivery Fee Model</Label>
                  <Select
                    value={formData.delivery_fee_model}
                    onValueChange={(value) => handleSelectChange('delivery_fee_model', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a delivery fee model" />
                    </SelectTrigger>
                    <SelectContent>
                      {DELIVERY_FEE_MODELS.map((model) => (
                        <SelectItem key={model.id} value={model.id}>
                          {model.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-gray-500">
                    How you calculate delivery fees for customers
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Conditionally show delivery fee field based on model */}
                  {(formData.delivery_fee_model === "fixed" || formData.delivery_fee_model === "mixed") && (
                    <div className="space-y-2">
                      <Label htmlFor="delivery_fee">
                        {formData.delivery_fee_model === "fixed" ? "Delivery Fee (£)" : "Base Delivery Fee (£)"}
                      </Label>
                      <Input
                        id="delivery_fee"
                        name="delivery_fee"
                        type="number"
                        min="0"
                        step="0.01"
                        value={formData.delivery_fee}
                        onChange={handleNumberChange}
                        placeholder="2.50"
                      />
                      <p className="text-sm text-gray-500">
                        {formData.delivery_fee_model === "fixed"
                          ? "Fixed fee charged for all deliveries"
                          : "Fixed component of your combined fee model"}
                      </p>
                    </div>
                  )}

                  {/* Per-km fee for distance or mixed models */}
                  {(formData.delivery_fee_model === "distance" || formData.delivery_fee_model === "mixed") && (
                    <div className="space-y-2">
                      <Label htmlFor="delivery_fee_per_km">Fee Per Kilometer (£)</Label>
                      <Input
                        id="delivery_fee_per_km"
                        name="delivery_fee_per_km"
                        type="number"
                        min="0"
                        step="0.01"
                        value={formData.delivery_fee_per_km || "0.50"}
                        onChange={handleNumberChange}
                        placeholder="0.50"
                      />
                      <p className="text-sm text-gray-500">
                        Amount charged per kilometer of delivery distance
                      </p>
                    </div>
                  )}

                  {/* Always show minimum order amount */}
                  <div className="space-y-2">
                    <Label htmlFor="minimum_order_amount">Minimum Order Amount (£)</Label>
                    <Input
                      id="minimum_order_amount"
                      name="minimum_order_amount"
                      type="number"
                      min="0"
                      step="0.01"
                      value={formData.minimum_order_amount}
                      onChange={handleNumberChange}
                      placeholder="15.00"
                    />
                    <p className="text-sm text-gray-500">
                      Minimum order value required for delivery
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="brand-assets">
          <Card>
            <CardHeader>
              <CardTitle>Brand Assets</CardTitle>
              <CardDescription>
                Upload and manage your business logo and banner images
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Logo</h3>
                <p className="text-sm text-gray-500">
                  Your logo will be displayed throughout the platform.
                  Recommended size is 400×400 pixels with a square aspect ratio.
                </p>

                {business && (
                  <FileUpload
                    type="logo"
                    businessId={business.slug}
                    currentImageUrl={business.logo_url}
                    onUploadComplete={(url) => {
                      setFormData(prev => ({ ...prev, logo_url: url }))
                    }}
                  />
                )}
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Banner Image</h3>
                <p className="text-sm text-gray-500">
                  Your banner image will be displayed at the top of your business page.
                  Recommended size is 1200×400 pixels.
                </p>

                {business && (
                  <FileUpload
                    type="banner"
                    businessId={business.slug}
                    currentImageUrl={business.banner_url}
                    onUploadComplete={(url) => {
                      setFormData(prev => ({ ...prev, banner_url: url }))
                    }}
                  />
                )}
              </div>

              <Alert className="bg-blue-50 border-blue-200 text-blue-800">
                <Info className="h-4 w-4" />
                <AlertDescription>
                  Images will be saved when you click the "Save Changes" button at the top of the page.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="team">
          <Card>
            <CardHeader>
              <CardTitle>Team Management</CardTitle>
              <CardDescription>
                Manage business managers and staff members
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Business Managers</h3>
                <p className="text-sm text-gray-500">
                  Managers have full access to your business dashboard and settings.
                </p>

                {managers.length > 0 ? (
                  <div className="border rounded-md">
                    <div className="grid grid-cols-3 gap-4 p-4 font-medium border-b">
                      <div>Name</div>
                      <div>Email</div>
                      <div>Role</div>
                    </div>
                    {managers.map((manager) => (
                      <div key={manager.id} className="grid grid-cols-3 gap-4 p-4 border-b last:border-0">
                        <div>{manager.users?.name || "Unknown"}</div>
                        <div>{manager.users?.email || "Unknown"}</div>
                        <div>
                          {manager.is_primary ? "Primary Manager" : "Manager"}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center p-4 border rounded-md bg-gray-50">
                    <p>No managers found.</p>
                  </div>
                )}

                <Alert className="bg-yellow-50 border-yellow-200 text-yellow-800">
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    To add or remove managers, please contact support.
                  </AlertDescription>
                </Alert>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Staff Members</h3>
                <p className="text-sm text-gray-500">
                  Staff members have limited access to your business dashboard.
                </p>

                {staff.length > 0 ? (
                  <div className="border rounded-md">
                    <div className="grid grid-cols-3 gap-4 p-4 font-medium border-b">
                      <div>Name</div>
                      <div>Email</div>
                      <div>Status</div>
                    </div>
                    {staff.map((staffMember) => (
                      <div key={staffMember.id} className="grid grid-cols-3 gap-4 p-4 border-b last:border-0">
                        <div>{staffMember.users?.name || "Unknown"}</div>
                        <div>{staffMember.users?.email || "Unknown"}</div>
                        <div>
                          {staffMember.is_active ? "Active" : "Inactive"}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center p-4 border rounded-md bg-gray-50">
                    <p>No staff members found.</p>
                  </div>
                )}

                <Alert className="bg-yellow-50 border-yellow-200 text-yellow-800">
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    Staff management features will be available in a future update.
                  </AlertDescription>
                </Alert>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="business-hours">
          <Card>
            <CardHeader>
              <CardTitle>Business Hours</CardTitle>
              <CardDescription>
                Set your business's opening and closing times
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <p className="text-sm text-gray-500 mb-4">
                  Set your opening and closing times for each day of the week. These times will be displayed to customers and used to determine when orders can be placed.
                </p>

                {(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'] as const).map((day) => (
                  <div key={day} className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center border-b pb-4">
                    <div className="font-medium capitalize">{day}</div>
                    <div className="space-y-2">
                      <Label htmlFor={`${day}-open`}>Opening Time</Label>
                      <Input
                        id={`${day}-open`}
                        type="time"
                        value={formData.opening_hours?.[day]?.open || "09:00"}
                        onChange={(e) => {
                          const newOpeningHours = { ...formData.opening_hours };
                          newOpeningHours[day] = {
                            ...newOpeningHours[day],
                            open: e.target.value
                          };
                          setFormData({ ...formData, opening_hours: newOpeningHours });
                        }}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor={`${day}-close`}>Closing Time</Label>
                      <Input
                        id={`${day}-close`}
                        type="time"
                        value={formData.opening_hours?.[day]?.close || "17:00"}
                        onChange={(e) => {
                          const newOpeningHours = { ...formData.opening_hours };
                          newOpeningHours[day] = {
                            ...newOpeningHours[day],
                            close: e.target.value
                          };
                          setFormData({ ...formData, opening_hours: newOpeningHours });
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>

              <Alert className="bg-blue-50 border-blue-200 text-blue-800">
                <Info className="h-4 w-4" />
                <AlertDescription>
                  Business hours will be saved when you click the "Save Changes" button at the top of the page.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Status & Info Tab */}
        <TabsContent value="status">
          <Card>
            <CardHeader>
              <CardTitle>Business Status & Information</CardTitle>
              <CardDescription>
                View your business approval status and important dates
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Approval Status Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Approval Status</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="border rounded-md p-4 bg-gray-50">
                    <h4 className="font-medium mb-2">Business Status</h4>
                    {approvalStatus?.business_is_approved === true ? (
                      <div className="flex items-center text-green-600">
                        <div className="h-3 w-3 rounded-full bg-green-500 mr-2"></div>
                        <span>Approved</span>
                      </div>
                    ) : approvalStatus?.business_is_approved === false ? (
                      <div className="flex items-center text-red-600">
                        <div className="h-3 w-3 rounded-full bg-red-500 mr-2"></div>
                        <span>Rejected</span>
                      </div>
                    ) : (
                      <div className="flex items-center text-amber-600">
                        <div className="h-3 w-3 rounded-full bg-amber-500 mr-2"></div>
                        <span>Pending Approval</span>
                      </div>
                    )}
                    {approvalStatus?.business_approval_date && (
                      <p className="text-sm text-gray-500 mt-1">
                        Approved on: {approvalStatus.business_approval_date}
                      </p>
                    )}
                  </div>

                  <div className="border rounded-md p-4 bg-gray-50">
                    <h4 className="font-medium mb-2">Manager Status</h4>
                    {approvalStatus?.manager_is_approved === true ? (
                      <div className="flex items-center text-green-600">
                        <div className="h-3 w-3 rounded-full bg-green-500 mr-2"></div>
                        <span>Approved</span>
                      </div>
                    ) : approvalStatus?.manager_is_approved === false ? (
                      <div className="flex items-center text-red-600">
                        <div className="h-3 w-3 rounded-full bg-red-500 mr-2"></div>
                        <span>Rejected</span>
                      </div>
                    ) : (
                      <div className="flex items-center text-amber-600">
                        <div className="h-3 w-3 rounded-full bg-amber-500 mr-2"></div>
                        <span>Pending Approval</span>
                      </div>
                    )}
                    {approvalStatus?.manager_approval_date && (
                      <p className="text-sm text-gray-500 mt-1">
                        Approved on: {approvalStatus.manager_approval_date}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Important Dates Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Important Dates</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="border rounded-md p-4">
                    <h4 className="font-medium mb-2">Business Created</h4>
                    <p>{approvalStatus?.business_created_at || "Not available"}</p>
                  </div>

                  <div className="border rounded-md p-4">
                    <h4 className="font-medium mb-2">Last Updated</h4>
                    <p>{approvalStatus?.business_updated_at || "Not available"}</p>
                  </div>
                </div>
              </div>

              {/* Hygiene Rating Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Hygiene Information</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="border rounded-md p-4">
                    <h4 className="font-medium mb-2">Hygiene Rating</h4>
                    <div className="flex items-center">
                      {business?.hygiene_rating ? (
                        <div className="bg-green-100 text-green-800 font-bold text-lg px-3 py-1 rounded-md">
                          {business.hygiene_rating}
                        </div>
                      ) : (
                        <span className="text-gray-500">Not yet rated</span>
                      )}
                    </div>
                  </div>

                  <div className="border rounded-md p-4">
                    <h4 className="font-medium mb-2">Last Inspection</h4>
                    <p>{business?.last_inspection_date || "Not available"}</p>
                  </div>
                </div>
              </div>

              {/* Technical Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Technical Information</h3>

                <div className="border rounded-md p-4">
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <h4 className="font-medium text-sm">Business ID</h4>
                      <p className="text-sm">{business?.id || "N/A"}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">Business Type ID</h4>
                      <p className="text-sm">{business?.business_type_id || "N/A"}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">Business Type</h4>
                      <p className="text-sm">{business?.business_types?.name || "N/A"}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">Slug</h4>
                      <p className="text-sm">{business?.slug || "N/A"}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Debug Information - Collapsible */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">Debug Information</h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const debugElement = document.getElementById('debug-info');
                      if (debugElement) {
                        debugElement.style.display = debugElement.style.display === 'none' ? 'block' : 'none';
                      }
                    }}
                  >
                    Toggle Debug Info
                  </Button>
                </div>

                <div id="debug-info" className="border rounded-md p-4 bg-gray-50" style={{ display: 'none' }}>
                  <h4 className="font-medium mb-2">Raw Business Data</h4>
                  <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-60">
                    {JSON.stringify(business, null, 2)}
                  </pre>

                  <h4 className="font-medium mb-2 mt-4">Form Data</h4>
                  <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-60">
                    {JSON.stringify(formData, null, 2)}
                  </pre>

                  <h4 className="font-medium mb-2 mt-4">Approval Status</h4>
                  <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-60">
                    {JSON.stringify(approvalStatus, null, 2)}
                  </pre>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
