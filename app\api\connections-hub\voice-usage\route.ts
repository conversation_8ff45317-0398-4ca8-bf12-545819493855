// API endpoint for voice message usage statistics
import { NextRequest, NextResponse } from 'next/server'
import { getVoiceUsageStats, checkVoiceLimits } from '@/utils/voice-limits'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    const stats = await getVoiceUsageStats(userId)
    
    if (!stats) {
      return NextResponse.json(
        { error: 'Failed to retrieve usage statistics' },
        { status: 500 }
      )
    }

    return NextResponse.json(stats)

  } catch (error) {
    console.error('Voice usage API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { userId, threadId, fileSizeBytes, durationSeconds } = body

    if (!userId || !threadId || fileSizeBytes === undefined || durationSeconds === undefined) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      )
    }

    const limitCheck = await checkVoiceLimits(userId, threadId, fileSizeBytes, durationSeconds)
    
    return NextResponse.json(limitCheck)

  } catch (error) {
    console.error('Voice limits check API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
