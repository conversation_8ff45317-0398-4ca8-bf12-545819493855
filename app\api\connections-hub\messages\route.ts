import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { verifyUserAccess } from '@/utils/auth-helpers'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

interface SendMessageRequest {
  recipient_id: string
  content: string
  subject?: string
  channel_type: 'customer_enquiries' | 'active_order_delivery' | 'pre_order_planning' | 'post_order_feedback' | 'business_networking' | 'rider_coordination' | 'general_networking'
  message_type?: 'chat' | 'voice' | 'notification' | 'alert' | 'request' | 'response'
  order_id?: number
  business_id?: string
  connection_id?: string
  thread_id?: string
  parent_message_id?: string
  is_urgent?: boolean
  priority?: number
  audio_data?: string
}

// GET - Get user's messages/conversations
export async function GET(request: NextRequest) {
  try {
    // Verify user access
    const accessCheck = await verifyUserAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const user = accessCheck.user
    const { searchParams } = new URL(request.url)

    const view = searchParams.get('view') || 'conversations' // 'conversations' or 'messages'
    const thread_id = searchParams.get('thread_id')
    const channel_type = searchParams.get('channel_type')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    if (view === 'conversations') {
      // Get conversation list (grouped by thread_id) with profile information
      let query = supabase
        .from('communications')
        .select(`
          thread_id,
          channel_type,
          order_id,
          business_id,
          connection_id,
          subject,
          content,
          sender_id,
          recipient_id,
          is_read,
          is_urgent,
          priority,
          created_at
        `)
        .or(`sender_id.eq.${user.id},recipient_id.eq.${user.id}`)
        .not('thread_id', 'is', null)
        .order('created_at', { ascending: false })

      if (channel_type) {
        query = query.eq('channel_type', channel_type)
      }

      const { data: messages, error } = await query.limit(limit * 3) // Get more to group properly

      if (error) {
        console.error('Error fetching conversations:', error)
        return NextResponse.json(
          { error: 'Failed to fetch conversations' },
          { status: 500 }
        )
      }

      // Group by thread_id and get latest message for each conversation
      const conversationsMap = new Map()

      messages?.forEach(message => {
        const threadId = message.thread_id
        if (!conversationsMap.has(threadId) ||
            new Date(message.created_at) > new Date(conversationsMap.get(threadId).created_at)) {
          conversationsMap.set(threadId, message)
        }
      })

      const conversations = Array.from(conversationsMap.values())
        .slice(offset, offset + limit)

      // Get unread counts and profile information for each conversation
      const conversationsWithCounts = await Promise.all(
        conversations.map(async (conv) => {
          const { count } = await supabase
            .from('communications')
            .select('*', { count: 'exact', head: true })
            .eq('thread_id', conv.thread_id)
            .eq('recipient_id', user.id)
            .eq('is_read', false)

          const other_user_id = conv.sender_id === user.id ? conv.recipient_id : conv.sender_id

          // Get profile information for the other user
          const { data: profile } = await supabase
            .from('connection_profiles')
            .select('display_name, bio, avatar_url')
            .eq('user_id', other_user_id)
            .single()

          return {
            ...conv,
            unread_count: count || 0,
            other_user_id,
            display_name: profile?.display_name,
            bio: profile?.bio,
            avatar_url: profile?.avatar_url
          }
        })
      )

      return NextResponse.json({
        conversations: conversationsWithCounts,
        total: conversationsMap.size
      })

    } else if (view === 'messages' && thread_id) {
      // Get messages in a specific thread
      let query = supabase
        .from('communications')
        .select(`
          id,
          sender_id,
          recipient_id,
          connection_id,
          order_id,
          business_id,
          channel_type,
          message_type,
          subject,
          content,
          thread_id,
          parent_message_id,
          is_read,
          is_urgent,
          is_automated,
          priority,
          created_at,
          read_at
        `)
        .eq('thread_id', thread_id)
        .or(`sender_id.eq.${user.id},recipient_id.eq.${user.id}`)
        .order('created_at', { ascending: true })

      const { data: messages, error } = await query.range(offset, offset + limit - 1)

      if (error) {
        console.error('Error fetching thread messages:', error)
        return NextResponse.json(
          { error: 'Failed to fetch messages' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        messages: messages || [],
        thread_id
      })

    } else {
      // Get all messages for user
      let query = supabase
        .from('communications')
        .select(`
          id,
          sender_id,
          recipient_id,
          connection_id,
          order_id,
          business_id,
          channel_type,
          message_type,
          subject,
          content,
          thread_id,
          parent_message_id,
          is_read,
          is_urgent,
          is_automated,
          priority,
          created_at,
          read_at
        `)
        .or(`sender_id.eq.${user.id},recipient_id.eq.${user.id}`)
        .order('created_at', { ascending: false })

      if (channel_type) {
        query = query.eq('channel_type', channel_type)
      }

      const { data: messages, error } = await query.range(offset, offset + limit - 1)

      if (error) {
        console.error('Error fetching messages:', error)
        return NextResponse.json(
          { error: 'Failed to fetch messages' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        messages: messages || []
      })
    }

  } catch (error: any) {
    console.error('Error in messages GET:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST - Send new message
export async function POST(request: NextRequest) {
  try {
    // Verify user access
    const accessCheck = await verifyUserAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const user = accessCheck.user
    const body: SendMessageRequest = await request.json()

    // Validate required fields
    if (!body.recipient_id || !body.content || !body.channel_type) {
      return NextResponse.json(
        { error: 'recipient_id, content, and channel_type are required' },
        { status: 400 }
      )
    }

    // Prepare message data
    const messageData = {
      sender_id: user.id,
      recipient_id: body.recipient_id,
      content: body.content,
      subject: body.subject,
      channel_type: body.channel_type,
      message_type: body.message_type || 'chat',
      order_id: body.order_id,
      business_id: body.business_id,
      connection_id: body.connection_id,
      thread_id: body.thread_id,
      parent_message_id: body.parent_message_id,
      is_urgent: body.is_urgent || false,
      is_automated: false,
      priority: body.priority || 0,
      audio_data: body.audio_data, // Now using proper column
      created_at: new Date().toISOString()
    }

    // Insert message
    const { data: message, error } = await supabase
      .from('communications')
      .insert(messageData)
      .select()
      .single()

    if (error) {
      console.error('Error sending message:', error)
      return NextResponse.json(
        { error: 'Failed to send message' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'Message sent successfully',
      data: message
    })

  } catch (error: any) {
    console.error('Error in messages POST:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
