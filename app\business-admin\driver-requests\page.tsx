"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useAuthDirect } from "@/context/auth-context-direct"
import { supabase } from "@/lib/supabase"
import { Loader2, CheckCircle, XCircle, User, Calendar, Clock, MapPin } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { format } from "date-fns"

// Define driver request status types
type RequestStatus = "pending" | "approved" | "rejected"

// Define driver request type
interface DriverRequest {
  id: number
  driver_id: string
  business_id: number
  status: RequestStatus
  created_at: string
  driver: {
    id: string
    email: string
    user_metadata: {
      name?: string
      full_name?: string
      phone?: string
      avatar_url?: string
    }
  }
}

export default function DriverRequestsPage() {
  const { toast } = useToast()
  const router = useRouter()
  const { user, userProfile, isAdmin, isSuperAdmin } = useAuthDirect()

  // Helper function to get driver name
  const getDriverName = (driver: DriverRequest['driver']) => {
    return driver.user_metadata?.name ||
           driver.user_metadata?.full_name ||
           driver.email.split('@')[0];
  }

  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState<number | null>(null)
  const [businessId, setBusinessId] = useState<number | null>(null)
  const [driverRequests, setDriverRequests] = useState<DriverRequest[]>([])

  // Fetch driver requests
  useEffect(() => {
    async function fetchDriverRequests() {
      if (!user) return

      try {
        setLoading(true)

        console.log("Fetching business manager data for user:", user?.email)

        // First, get the business ID for this manager directly
        // Try to query business_managers with the user ID
        console.log("Current user:", user)
        console.log("User profile:", userProfile)
        console.log("Trying to find business manager with user ID:", user?.id)
        const { data: managerData, error: managerError } = await supabase
          .from('business_managers')
          .select('business_id, user_id')
          .eq('user_id', user?.id)
          .single()

        if (managerError) {
          console.error("Error fetching business manager data:", managerError)

          // Try an alternative approach - query by user's email directly
          const { data: businessData, error: businessError } = await supabase
            .from('businesses')
            .select('id')
            .eq('email', user?.email)
            .single()

          if (businessError) {
            console.error("Error fetching business by email:", businessError)
            toast({
              variant: "destructive",
              title: "Error",
              description: "Could not find your business. Please contact support."
            })
            return
          }

          if (businessData) {
            setBusinessId(businessData.id)
            // Continue with driver requests
          } else {
            // As a last resort, try to get the first business in the database
            // This is only for development/testing purposes
            console.log("Trying to get any business as fallback")
            const { data: anyBusiness, error: anyBusinessError } = await supabase
              .from('businesses')
              .select('id')
              .limit(1)
              .single()

            if (anyBusinessError || !anyBusiness) {
              console.error("Error fetching any business:", anyBusinessError)
              toast({
                variant: "destructive",
                title: "Error",
                description: "Could not find any business. Please contact support."
              })
              return
            }

            console.log("Found a business as fallback:", anyBusiness)
            setBusinessId(anyBusiness.id)
            return
          }
        }

        // We've already handled the managerError case above

        if (!managerData || !managerData.business_id) {
          console.error("No business ID found for this user")
          toast({
            variant: "destructive",
            title: "Error",
            description: "No business associated with your account."
          })
          return
        }

        setBusinessId(managerData.business_id)

        try {
          // Fetch driver requests for this business
          const { data, error } = await supabase
            .from('driver_business_requests')
            .select(`
              id,
              driver_id,
              business_id,
              status,
              created_at,
              driver:auth_users!driver_id(
                id,
                email,
                user_metadata
              )
            `)
            .eq('business_id', managerData.business_id)
            .order('created_at', { ascending: false })

          console.log("Driver requests query:", error || data)

          if (error) {
            // Check if the error is because the table doesn't exist
            if (error.message?.includes("relation") && error.message?.includes("does not exist")) {
              console.log("Driver business requests table doesn't exist yet")
              // This is expected if the feature is new and the table hasn't been created
              setDriverRequests([])
            } else {
              console.error("Error fetching driver requests:", error)
              toast({
                variant: "destructive",
                title: "Error",
                description: "Could not load driver requests."
              })
            }
            return
          }

          if (data) {
            setDriverRequests(data as DriverRequest[])
          }
        } catch (error) {
          console.error("Error in driver requests query:", error)
          // Set empty array as fallback
          setDriverRequests([])
        }
      } catch (error) {
        console.error("Error in fetchDriverRequests:", error)
        toast({
          variant: "destructive",
            title: "Error",
            description: "An unexpected error occurred. Please try again."
        })
      } finally {
        setLoading(false)
      }
    }

    fetchDriverRequests()
  }, [user, userProfile])

  // Handle approve driver request
  const handleApprove = async (requestId: number) => {
    if (!businessId) return

    setProcessing(requestId)

    try {
      const request = driverRequests.find(r => r.id === requestId)
      if (!request) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Driver request not found."
        })
        return
      }

      const response = await fetch('/api/business/approve-driver', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          application_id: requestId,
          action: 'approve',
          business_id: businessId
        })
      })

      const data = await response.json()

      if (data.success) {
        // Update local state
        setDriverRequests(prev =>
          prev.map(req =>
            req.id === requestId ? { ...req, status: 'approved' } : req
          )
        )

        toast({
          title: "Driver Approved",
          description: data.message
        })
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: data.error || "Failed to approve driver request."
        })
      }
    } catch (error) {
      console.error("Error in handleApprove:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred. Please try again."
      })
    } finally {
      setProcessing(null)
    }
  }

  // Handle reject driver request
  const handleReject = async (requestId: number) => {
    if (!businessId) return

    setProcessing(requestId)

    try {
      const request = driverRequests.find(r => r.id === requestId)
      if (!request) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Driver request not found."
        })
        return
      }

      const response = await fetch('/api/business/approve-driver', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          application_id: requestId,
          action: 'reject',
          reason: 'Not suitable for our business requirements',
          business_id: businessId
        })
      })

      const data = await response.json()

      if (data.success) {
        // Update local state
        setDriverRequests(prev =>
          prev.map(req =>
            req.id === requestId ? { ...req, status: 'rejected' } : req
          )
        )

        toast({
          title: "Driver Rejected",
          description: data.message
        })
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: data.error || "Failed to reject driver request."
        })
      }
    } catch (error) {
      console.error("Error in handleReject:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred. Please try again."
      })
    } finally {
      setProcessing(null)
    }
  }

  // Filter requests by status
  const pendingRequests = driverRequests.filter(req => req.status === 'pending')
  const approvedRequests = driverRequests.filter(req => req.status === 'approved')
  const rejectedRequests = driverRequests.filter(req => req.status === 'rejected')

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-emerald-600" />
      </div>
    )
  }

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Driver Requests</h1>
        <p className="text-gray-500">Manage driver requests to deliver for your business</p>
      </div>

      <Tabs defaultValue="pending">
        <TabsList className="mb-4">
          <TabsTrigger value="pending">
            Pending
            {pendingRequests.length > 0 && (
              <Badge variant="secondary" className="ml-2">
                {pendingRequests.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="approved">Approved</TabsTrigger>
          <TabsTrigger value="rejected">Rejected</TabsTrigger>
        </TabsList>

        <TabsContent value="pending">
          {pendingRequests.length > 0 ? (
            <div className="space-y-4">
              {pendingRequests.map((request) => (
                <Card key={request.id}>
                  <CardContent className="p-6">
                    <div className="flex flex-col md:flex-row gap-4">
                      <div className="flex-shrink-0">
                        <Avatar className="h-16 w-16">
                          <AvatarImage
                            src={request.driver.user_metadata?.avatar_url || ''}
                            alt={getDriverName(request.driver)}
                          />
                          <AvatarFallback>
                            {getDriverName(request.driver).substring(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                      </div>

                      <div className="flex-grow space-y-2">
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                          <h3 className="text-lg font-semibold">{getDriverName(request.driver)}</h3>
                          <div className="flex items-center space-x-2 mt-2 md:mt-0">
                            <Button
                              variant="outline"
                              size="sm"
                              className="border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700"
                              onClick={() => handleReject(request.id)}
                              disabled={processing === request.id}
                            >
                              {processing === request.id ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <XCircle className="h-4 w-4 mr-1" />
                              )}
                              Reject
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="border-green-200 text-green-600 hover:bg-green-50 hover:text-green-700"
                              onClick={() => handleApprove(request.id)}
                              disabled={processing === request.id}
                            >
                              {processing === request.id ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <CheckCircle className="h-4 w-4 mr-1" />
                              )}
                              Approve
                            </Button>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                          <div className="flex items-center text-gray-600">
                            <User className="h-4 w-4 mr-2" />
                            {request.driver.email}
                          </div>
                          {request.driver.user_metadata?.phone && (
                            <div className="flex items-center text-gray-600">
                              <User className="h-4 w-4 mr-2" />
                              {request.driver.user_metadata.phone}
                            </div>
                          )}
                          <div className="flex items-center text-gray-600">
                            <Calendar className="h-4 w-4 mr-2" />
                            Requested {format(new Date(request.created_at), 'PPP')}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-gray-500">No pending driver requests</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="approved">
          {approvedRequests.length > 0 ? (
            <div className="space-y-4">
              {approvedRequests.map((request) => (
                <Card key={request.id}>
                  <CardContent className="p-6">
                    <div className="flex flex-col md:flex-row gap-4">
                      <div className="flex-shrink-0">
                        <Avatar className="h-16 w-16">
                          <AvatarImage
                            src={request.driver.user_metadata?.avatar_url || ''}
                            alt={getDriverName(request.driver)}
                          />
                          <AvatarFallback>
                            {getDriverName(request.driver).substring(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                      </div>

                      <div className="flex-grow space-y-2">
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                          <h3 className="text-lg font-semibold">{getDriverName(request.driver)}</h3>
                          <Badge className="bg-green-100 text-green-800 hover:bg-green-100 w-fit">
                            Approved
                          </Badge>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                          <div className="flex items-center text-gray-600">
                            <User className="h-4 w-4 mr-2" />
                            {request.driver.email}
                          </div>
                          {request.driver.user_metadata?.phone && (
                            <div className="flex items-center text-gray-600">
                              <User className="h-4 w-4 mr-2" />
                              {request.driver.user_metadata.phone}
                            </div>
                          )}
                          <div className="flex items-center text-gray-600">
                            <Calendar className="h-4 w-4 mr-2" />
                            Requested {format(new Date(request.created_at), 'PPP')}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-gray-500">No approved driver requests</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="rejected">
          {rejectedRequests.length > 0 ? (
            <div className="space-y-4">
              {rejectedRequests.map((request) => (
                <Card key={request.id}>
                  <CardContent className="p-6">
                    <div className="flex flex-col md:flex-row gap-4">
                      <div className="flex-shrink-0">
                        <Avatar className="h-16 w-16">
                          <AvatarImage
                            src={request.driver.user_metadata?.avatar_url || ''}
                            alt={getDriverName(request.driver)}
                          />
                          <AvatarFallback>
                            {getDriverName(request.driver).substring(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                      </div>

                      <div className="flex-grow space-y-2">
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                          <h3 className="text-lg font-semibold">{getDriverName(request.driver)}</h3>
                          <Badge variant="outline" className="border-red-200 text-red-800 w-fit">
                            Rejected
                          </Badge>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                          <div className="flex items-center text-gray-600">
                            <User className="h-4 w-4 mr-2" />
                            {request.driver.email}
                          </div>
                          <div className="flex items-center text-gray-600">
                            <Calendar className="h-4 w-4 mr-2" />
                            Requested {format(new Date(request.created_at), 'PPP')}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-gray-500">No rejected driver requests</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
