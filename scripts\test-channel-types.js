const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function testChannelTypes() {
  console.log('🧪 Testing Channel Types\n');

  try {
    // Get users
    const { data: users } = await supabase.auth.admin.listUsers();
    
    if (users.users.length < 2) {
      console.log('❌ Need at least 2 users');
      return;
    }

    const user1 = users.users[0];
    const user2 = users.users[1];
    
    console.log(`Using users: ${user1.email} and ${user2.email}\n`);

    // Test different channel types
    const channelTypesToTest = [
      'order',
      'pre-order', 
      'post-order',
      'general',
      'customer_enquiries',
      'active_order_delivery',
      'pre_order_planning',
      'post_order_feedback',
      'business_networking',
      'rider_coordination',
      'general_networking'
    ];

    const messageTypesToTest = [
      'chat',
      'notification',
      'alert',
      'request',
      'response',
      'status_update',
      'system'
    ];

    console.log('Testing channel types...');
    
    for (const channelType of channelTypesToTest) {
      const testMessage = {
        sender_id: user1.id,
        recipient_id: user2.id,
        channel_type: channelType,
        message_type: 'chat',
        content: `Test message for channel type: ${channelType}`,
        created_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('communications')
        .insert(testMessage)
        .select();

      if (error) {
        console.log(`❌ ${channelType}: ${error.message}`);
      } else {
        console.log(`✅ ${channelType}: SUCCESS (ID: ${data[0]?.id})`);
        
        // Clean up successful inserts
        await supabase
          .from('communications')
          .delete()
          .eq('id', data[0].id);
      }
    }

    console.log('\nTesting message types...');
    
    for (const messageType of messageTypesToTest) {
      const testMessage = {
        sender_id: user1.id,
        recipient_id: user2.id,
        channel_type: 'order', // Use a channel type that worked
        message_type: messageType,
        content: `Test message for message type: ${messageType}`,
        created_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('communications')
        .insert(testMessage)
        .select();

      if (error) {
        console.log(`❌ ${messageType}: ${error.message}`);
      } else {
        console.log(`✅ ${messageType}: SUCCESS (ID: ${data[0]?.id})`);
        
        // Clean up successful inserts
        await supabase
          .from('communications')
          .delete()
          .eq('id', data[0].id);
      }
    }

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

testChannelTypes();
