"use client"

import { useEffect, useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Loader2 } from "lucide-react"
import { useSupabase } from "@/components/providers/supabase-provider"

export default function AuthCallbackPage() {
  const router = useRouter()
  const [error, setError] = useState<string | null>(null)
  const supabase = useSupabase()

  useEffect(() => {
    // Handle the OAuth callback
    const handleCallback = async () => {
      try {
        // Check if this is a refresh request or OAuth callback
        const isRefresh = new URLSearchParams(window.location.search).get('refresh') === 'true'
        const code = new URLSearchParams(window.location.search).get('code')

        // Get the redirect URL from localStorage
        const redirectTo = localStorage.getItem('auth_redirect') || '/profile'
        console.log("Auth callback: Will redirect to:", redirectTo)

        // Clear the redirect URL from localStorage
        localStorage.removeItem('auth_redirect')

        if (code) {
          // This is an OAuth callback
          console.log("Auth callback: Processing OAuth callback")

          // The auth helpers will automatically exchange the code for a session
          const { error: exchangeError } = await supabase.auth.exchangeCodeForSession(code)

          if (exchangeError) {
            console.error("Auth callback: Error exchanging code for session:", exchangeError)
            setError(`Authentication error: ${exchangeError.message}`)
            return
          }
        }

        // Give the session a moment to be established
        console.log("Auth callback: Waiting for session to be established...")

        // Check for the session
        const { data, error: sessionError } = await supabase.auth.getSession()

        if (sessionError) {
          console.error("Auth callback: Session error:", sessionError)
          setError(`Session error: ${sessionError.message}. Please try logging in again.`)
          return
        }

        if (!data.session) {
          console.log("Auth callback: No session found, waiting...")

          // Wait a bit longer and try again
          setTimeout(async () => {
            const { data: retryData, error: retryError } = await supabase.auth.getSession()

            if (retryError) {
              console.error("Auth callback: Error on retry:", retryError)
              setError(`Authentication error: ${retryError.message}. Please try logging in again.`)
              return
            }

            if (!retryData.session) {
              console.error("Auth callback: Still no session after waiting")
              setError("Could not establish a session. Please try logging in again.")
              return
            }

            // We have a session, redirect
            console.log("Auth callback: Session established on retry, redirecting")
            router.push(redirectTo)
          }, 1000)
          return
        }

        // We have a session, redirect
        console.log("Auth callback: Session established, redirecting")
        router.push(redirectTo)
      } catch (err: any) {
        console.error("Auth callback error:", err)
        setError(err.message || "An unexpected error occurred during authentication")
      }
    }

    handleCallback()
  }, [router])

  return (
    <div className="flex min-h-[calc(100vh-4rem)] items-center justify-center bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>
            {error ? "Authentication Error" : "Completing Login"}
          </CardTitle>
          <CardDescription>
            {error ? "There was a problem with your authentication" : "Please wait while we complete your login..."}
          </CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center">
          {error ? (
            <div className="text-red-500">{error}</div>
          ) : (
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          )}
        </CardContent>
      </Card>
    </div>
  )
}
