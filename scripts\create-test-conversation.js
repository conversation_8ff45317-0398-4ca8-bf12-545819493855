#!/usr/bin/env node

/**
 * Create a simple test conversation for debugging
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables')
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function createTestConversation() {
  console.log('🧪 Creating test conversation...\n')

  try {
    // Get first two users from auth.users
    const { data: users, error: usersError } = await supabase.auth.admin.listUsers()

    if (usersError || !users.users || users.users.length < 2) {
      console.error('❌ Need at least 2 users in the system')
      console.error('Error:', usersError)
      return
    }

    const user1 = users.users[0]
    const user2 = users.users[1]

    console.log(`👤 User 1: ${user1.email} (${user1.id})`)
    console.log(`👤 User 2: ${user2.email} (${user2.id})`)

    // Create a test conversation with a proper UUID thread ID
    const crypto = require('crypto')
    const threadId = crypto.randomUUID()
    console.log(`🧵 Creating thread: ${threadId}`)

    const messages = [
      {
        sender_id: user1.id,
        recipient_id: user2.id,
        channel_type: 'customer_enquiries',
        message_type: 'chat',
        subject: 'Test Message',
        content: 'Hello! This is a test message to verify the messaging system is working.',
        thread_id: threadId,
        is_automated: false,
        is_urgent: false,
        priority: 0,
        created_at: new Date(Date.now() - 60000).toISOString() // 1 minute ago
      },
      {
        sender_id: user2.id,
        recipient_id: user1.id,
        channel_type: 'customer_enquiries',
        message_type: 'chat',
        content: 'Hi there! I received your test message. The system is working correctly!',
        thread_id: threadId,
        is_automated: false,
        is_urgent: false,
        priority: 0,
        created_at: new Date().toISOString()
      }
    ]

    // Insert messages
    for (const message of messages) {
      console.log(`📝 Creating message from ${message.sender_id.slice(0, 8)}...`)

      const { data, error } = await supabase
        .from('communications')
        .insert(message)
        .select()
        .single()

      if (error) {
        console.error('❌ Error creating message:', error)
        return
      }

      console.log(`✅ Message created: ${data.id}`)
    }

    console.log('\n🎉 Test conversation created successfully!')
    console.log(`🔗 Thread ID: ${threadId}`)
    console.log('📱 You can now test the messaging interface')

  } catch (error) {
    console.error('❌ Error creating test conversation:', error)
  }
}

createTestConversation()
