import { cookies } from "next/headers";
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs";
import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

// Create a Supabase client with admin privileges
const adminClient = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || "",
  process.env.SUPABASE_SERVICE_ROLE_KEY || ""
);

// Helper function to create a server-side Supabase client
async function createServerSupabase() {
  const cookieStore = cookies();
  return createServerComponentClient({ cookies: () => cookieStore });
}

// POST handler to save an address (create or update)
export async function POST(request: Request) {
  try {
    console.log("POST /api/user/addresses/save: Starting request");

    // Get the user's session using the server client
    const cookieStore = cookies();
    const supabase = createServerComponentClient({ cookies: () => cookieStore });

    console.log("POST /api/user/addresses/save: Getting session");
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError) {
      console.error("POST /api/user/addresses/save: Session error:", sessionError);
      return NextResponse.json(
        { error: `Session error: ${sessionError.message}` },
        { status: 500 }
      );
    }

    if (!session?.user) {
      console.log("POST /api/user/addresses/save: No authenticated session found");
      return NextResponse.json(
        { error: "Not authenticated" },
        { status: 401 }
      );
    }

    const email = session.user.email;
    const authUserId = session.user.id;

    if (!email) {
      console.log("POST /api/user/addresses/save: User email not found in session");
      return NextResponse.json(
        { error: "User email not found" },
        { status: 400 }
      );
    }

    // Parse request body
    const body = await request.json();
    const {
      id,
      address_name,
      address_line1,
      address_line2,
      parish,
      postcode,
      is_default,
      coordinates
    } = body;

    // Validate required fields
    if (!address_line1 || !parish || !postcode) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    console.log("POST /api/user/addresses/save: Authenticated user:", email);

    // Get user ID from the users table using admin client
    try {
      console.log("POST /api/user/addresses/save: Fetching user ID for email:", email);

      const { data: userData, error: userError } = await adminClient
        .from("users")
        .select("id")
        .eq("email", email)
        .single();

      let userId;

      // If user doesn't exist yet, create one
      if (userError && userError.code === 'PGRST116') {
        console.log("POST /api/user/addresses/save: User not found in database, creating new user");

        // Create a new user
        const { data: newUser, error: createError } = await adminClient
          .from("users")
          .insert([{
            email: email,
            name: email.split('@')[0] || 'User',
            password_hash: "auth_managed",
            role: "customer",
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }])
          .select();

        if (createError || !newUser || newUser.length === 0) {
          console.error("POST /api/user/addresses/save: Error creating user:", createError);
          return NextResponse.json(
            { error: "Error creating user: " + (createError?.message || "Unknown error") },
            { status: 500 }
          );
        }

        userId = newUser[0].id;
      } else if (userError) {
        console.error("POST /api/user/addresses/save: Error fetching user ID:", userError);
        return NextResponse.json(
          { error: "Error fetching user data: " + userError.message },
          { status: 500 }
        );
      } else {
        userId = userData.id;
      }

      console.log("POST /api/user/addresses/save: Using user ID:", userId);

      // If setting as default, first unset any existing default addresses
      if (is_default) {
        console.log("POST /api/user/addresses/save: Setting as default, unsetting other defaults");

        // Unset defaults by user_id
        await adminClient
          .from("user_addresses")
          .update({ is_default: false })
          .eq("user_id", userId);

        // Also unset defaults by auth_user_id
        await adminClient
          .from("user_addresses")
          .update({ is_default: false })
          .eq("auth_user_id", authUserId);

        // Also unset defaults by email
        await adminClient
          .from("user_addresses")
          .update({ is_default: false })
          .eq("email", email);
      }

      // Prepare the address data
      const addressData = {
        user_id: userId,
        auth_user_id: authUserId,
        user_email: email,
        email: email,
        address_name: address_name || "Home",
        address_line1,
        address_line2: address_line2 || null,
        parish,
        postcode,
        is_default: is_default || false,
        coordinates: coordinates || null,
        updated_at: new Date().toISOString()
      };

      let savedAddress;

      // If ID is provided, update existing address
      if (id) {
        console.log("POST /api/user/addresses/save: Updating existing address:", id);

        const { data: updatedAddress, error: updateError } = await adminClient
          .from("user_addresses")
          .update(addressData)
          .eq("id", id)
          .select()
          .single();

        if (updateError) {
          console.error("POST /api/user/addresses/save: Error updating address:", updateError);
          return NextResponse.json(
            { error: "Error updating address: " + updateError.message },
            { status: 500 }
          );
        }

        savedAddress = updatedAddress;
      } else {
        // Create new address
        console.log("POST /api/user/addresses/save: Creating new address");

        // Add created_at for new addresses
        addressData.created_at = new Date().toISOString();

        const { data: newAddress, error: createError } = await adminClient
          .from("user_addresses")
          .insert([addressData])
          .select()
          .single();

        if (createError) {
          console.error("POST /api/user/addresses/save: Error creating address:", createError);
          return NextResponse.json(
            { error: "Error creating address: " + createError.message },
            { status: 500 }
          );
        }

        savedAddress = newAddress;
      }

      // If coordinates weren't provided, geocode the address
      if (!coordinates && savedAddress) {
        try {
          console.log("POST /api/user/addresses/save: Geocoding address");
          const { geocodeAndUpdateUserAddressCoordinates } = await import('@/lib/address-utils');
          await geocodeAndUpdateUserAddressCoordinates(
            savedAddress.id,
            address_line1,
            address_line2 || null,
            parish,
            postcode
          );

          // Get the updated address with coordinates
          const { data: updatedAddress } = await adminClient
            .from("user_addresses")
            .select("*")
            .eq("id", savedAddress.id)
            .single();

          if (updatedAddress) {
            savedAddress = updatedAddress;
          }
        } catch (geocodeError) {
          console.error("POST /api/user/addresses/save: Error geocoding address:", geocodeError);
          // Continue anyway, as the address was saved successfully
        }
      }

      console.log("POST /api/user/addresses/save: Address saved successfully");
      return NextResponse.json({
        message: id ? "Address updated successfully" : "Address created successfully",
        address: savedAddress
      });
    } catch (error) {
      console.error("POST /api/user/addresses/save: Unexpected error:", error);
      return NextResponse.json(
        { error: "An unexpected error occurred" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("POST /api/user/addresses/save: Unexpected error:", error);
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
