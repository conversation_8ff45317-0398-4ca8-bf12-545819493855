"use client"

import { Inter } from "next/font/google"
import { AdminLayoutWrapper } from "@/components/admin/admin-layout-wrapper"
import { AuthProviderDirect } from "@/context/auth-context-direct"
import { SupabaseProvider } from "@/components/providers/supabase-provider"

const inter = Inter({ subsets: ["latin"] })

export default function AdminNewLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <div className={inter.className}>
      <SupabaseProvider>
        <AuthProviderDirect>
          <AdminLayoutWrapper>
            {children}
          </AdminLayoutWrapper>
        </AuthProviderDirect>
      </SupabaseProvider>
    </div>
  )
}
