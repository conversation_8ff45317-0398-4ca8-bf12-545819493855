const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function checkExistingTables() {
  try {
    console.log('🔍 Checking existing tables...');
    
    // Get all tables in public schema
    const { data: tables, error: tablesError } = await supabase
      .rpc('exec_sql', {
        sql: `
          SELECT table_name, table_type
          FROM information_schema.tables
          WHERE table_schema = 'public'
          ORDER BY table_name;
        `
      });
    
    if (tablesError) {
      console.error('❌ Error getting tables:', tablesError);
      return;
    }
    
    console.log('📊 Tables in public schema:');
    if (tables && tables.length > 0) {
      tables.forEach(table => {
        console.log(`  - ${table.table_name} (${table.table_type})`);
      });
    } else {
      console.log('  No tables found');
    }
    
    // Check if orders table exists and get its structure
    const ordersTableExists = tables && tables.some(t => t.table_name === 'orders');
    
    if (ordersTableExists) {
      console.log('\n🔍 Checking orders table structure...');
      
      const { data: columns, error: columnsError } = await supabase
        .rpc('exec_sql', {
          sql: `
            SELECT column_name, data_type, udt_name, is_nullable
            FROM information_schema.columns
            WHERE table_schema = 'public'
            AND table_name = 'orders'
            ORDER BY ordinal_position;
          `
        });
      
      if (columnsError) {
        console.error('❌ Error getting orders columns:', columnsError);
      } else if (columns && columns.length > 0) {
        console.log('📋 Orders table columns:');
        columns.forEach(column => {
          console.log(`  - ${column.column_name}: ${column.data_type} (${column.udt_name}) ${column.is_nullable === 'YES' ? 'nullable' : 'not null'}`);
        });
      }
    } else {
      console.log('\n⚠️  Orders table does not exist');
    }
    
    // Check communications table if it exists
    const communicationsTableExists = tables && tables.some(t => t.table_name === 'communications');
    
    if (communicationsTableExists) {
      console.log('\n🔍 Communications table exists');
      
      const { data: commColumns, error: commColumnsError } = await supabase
        .rpc('exec_sql', {
          sql: `
            SELECT column_name, data_type, udt_name
            FROM information_schema.columns
            WHERE table_schema = 'public'
            AND table_name = 'communications'
            AND column_name IN ('order_id', 'id')
            ORDER BY ordinal_position;
          `
        });
      
      if (commColumnsError) {
        console.error('❌ Error getting communications columns:', commColumnsError);
      } else if (commColumns && commColumns.length > 0) {
        console.log('📋 Communications table relevant columns:');
        commColumns.forEach(column => {
          console.log(`  - ${column.column_name}: ${column.data_type} (${column.udt_name})`);
        });
      }
    } else {
      console.log('\n⚠️  Communications table does not exist');
    }
    
  } catch (err) {
    console.error('❌ Error:', err);
  }
}

checkExistingTables();
