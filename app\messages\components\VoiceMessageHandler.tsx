// Improved voice message handling with file storage
import { useState, useRef } from 'react'
import { uploadVoiceMessage, VoiceUploadResult } from '@/utils/voice-storage'

interface VoiceMessageHandlerProps {
  userId: string
  threadId: string
  channelType: string
  onVoiceMessageSent: (messageData: any) => void
}

export function useVoiceMessageHandler({
  userId,
  threadId,
  channelType,
  onVoiceMessageSent
}: VoiceMessageHandlerProps) {
  const [isRecording, setIsRecording] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [recordingTime, setRecordingTime] = useState(0)
  const [audioChunks, setAudioChunks] = useState<Blob[]>([])
  
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const recordingIntervalRef = useRef<NodeJS.Timeout | null>(null)

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 44100
        }
      })
      
      const recorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      })
      
      mediaRecorderRef.current = recorder
      setAudioChunks([])
      setIsRecording(true)
      setRecordingTime(0)
      
      // Start recording timer
      recordingIntervalRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1)
      }, 1000)
      
      recorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          setAudioChunks(prev => [...prev, event.data])
        }
      }
      
      recorder.onstop = async () => {
        stream.getTracks().forEach(track => track.stop())
        if (recordingIntervalRef.current) {
          clearInterval(recordingIntervalRef.current)
        }
        
        // Process the recorded audio
        await processRecording()
      }
      
      recorder.start(1000) // Collect data every second
    } catch (error) {
      console.error('Error starting recording:', error)
      alert('Could not access microphone. Please check permissions.')
    }
  }

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop()
      setIsRecording(false)
      setRecordingTime(0)
    }
  }

  const processRecording = async () => {
    if (audioChunks.length === 0) return

    setIsUploading(true)
    
    try {
      // Create audio blob
      const audioBlob = new Blob(audioChunks, { type: 'audio/webm' })
      
      // Upload to storage
      const uploadResult: VoiceUploadResult = await uploadVoiceMessage(
        audioBlob,
        userId,
        threadId
      )
      
      if (!uploadResult.success) {
        throw new Error(uploadResult.error || 'Upload failed')
      }
      
      // Send message with file URL instead of base64
      const messageData = {
        content: '[Voice Message]',
        message_type: 'voice',
        channel_type: channelType,
        thread_id: threadId,
        audio_data: uploadResult.fileUrl, // Store URL instead of base64
        audio_file_name: uploadResult.fileName,
        audio_file_size: audioBlob.size,
        audio_duration: recordingTime
      }
      
      await onVoiceMessageSent(messageData)
      setAudioChunks([])
      
    } catch (error) {
      console.error('Error processing voice message:', error)
      alert('Failed to send voice message. Please try again.')
    } finally {
      setIsUploading(false)
    }
  }

  const cancelRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop()
      setIsRecording(false)
      setRecordingTime(0)
      setAudioChunks([])
    }
  }

  return {
    isRecording,
    isUploading,
    recordingTime,
    startRecording,
    stopRecording,
    cancelRecording
  }
}
