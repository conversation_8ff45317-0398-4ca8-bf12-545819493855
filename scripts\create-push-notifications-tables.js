const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function createPushNotificationsTables() {
  try {
    console.log('🚀 Creating push notifications tables...');
    
    // Read the SQL file
    const sqlPath = path.join(__dirname, '../database/migrations/create_push_notifications_table.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    console.log('📄 Executing SQL migration...');
    
    // Execute the SQL
    const { data, error } = await supabase.rpc('exec_sql', { sql });
    
    if (error) {
      console.error('❌ Migration failed:', error);
      return false;
    }
    
    console.log('✅ Push notifications tables created successfully!');
    console.log('📋 Result:', data);
    
    // Verify tables were created
    console.log('🔍 Verifying table creation...');
    
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .in('table_name', ['push_subscriptions', 'notification_log']);
    
    if (tablesError) {
      console.error('❌ Error verifying tables:', tablesError);
      return false;
    }
    
    console.log('📊 Tables found:', tables.map(t => t.table_name));
    
    if (tables.length === 2) {
      console.log('🎉 All tables created successfully!');
      return true;
    } else {
      console.log('⚠️  Some tables may not have been created properly');
      return false;
    }
    
  } catch (err) {
    console.error('❌ Error running migration:', err);
    return false;
  }
}

// Run the migration
createPushNotificationsTables()
  .then(success => {
    if (success) {
      console.log('✅ Migration completed successfully');
      process.exit(0);
    } else {
      console.log('❌ Migration failed');
      process.exit(1);
    }
  })
  .catch(err => {
    console.error('❌ Unexpected error:', err);
    process.exit(1);
  });
