"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { format } from "date-fns"
import { useAuthDirect } from "@/context/auth-context-direct"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import {
  Download,
  HelpCircle,
  ShoppingBag,
  Users,
  AlertCircle,
  RefreshCcw,
  DollarSign,
  CreditCard
} from "lucide-react"

// Import the components we need for the dashboard
import { DatePickerWithRange } from "@/components/date-range-picker"
// Import both real and mock data providers
import { useBusinessDashboardStats } from "@/hooks/use-business-dashboard-stats"
import { useMockDashboardData } from "@/hooks/use-mock-dashboard-data"

// Import chart components
import { RevenueChart } from "@/components/charts/revenue-chart"
import { CategoryRevenueChart } from "@/components/charts/category-revenue-chart"
import { OrdersByDayChart } from "@/components/charts/orders-by-day-chart"
import { OrdersByTimeChart } from "@/components/charts/orders-by-time-chart"
import { OverviewChart } from "@/components/charts/overview-chart"
import { TopProductsList } from "@/components/dashboard/top-products-list"
import { RecentOrdersList } from "@/components/dashboard/recent-orders-list"
import { FulfillmentMetrics } from "@/components/dashboard/fulfillment-metrics"
import { StatsCard } from "@/components/dashboard/stats-card"
import { RealtimeIndicator } from "@/components/dashboard/realtime-indicator"
import { SalesMetrics } from "@/components/dashboard/sales-metrics"
import { DashboardHeader } from "@/components/dashboard/dashboard-header"

// Define types for our data
interface BusinessOption {
  id: number
  name: string
  business_type?: string
}

export default function BusinessAdminDashboard() {
  const router = useRouter()
  const { user, userProfile, isAdmin, isSuperAdmin } = useAuthDirect()
  const [activeTab, setActiveTab] = useState("overview")
  const [availableBusinesses, setAvailableBusinesses] = useState<BusinessOption[]>([])
  const [selectedBusinessId, setSelectedBusinessId] = useState<number | null>(null)
  const [isAdminUser, setIsAdminUser] = useState(false)
  const [realtimeEnabled, setRealtimeEnabled] = useState(true)
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date }>({
    from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
    to: new Date()
  })

  // Check if user is admin or super admin
  useEffect(() => {
    if (isAdmin || isSuperAdmin) {
      setIsAdminUser(true)
    }
  }, [isAdmin, isSuperAdmin])

  // Use our mock data provider instead of the real API to avoid 500 errors
  const {
    business,
    orderStats,
    todayStats,
    loading: isLoading,
    error: dashboardError,
    isPendingApproval,
    refetch: refetchDashboard
  } = useMockDashboardData({
    businessId: (isAdminUser && selectedBusinessId) ? selectedBusinessId : userProfile?.business_id,
    isAdminUser,
    enabled: !!user,
    realtimeEnabled
  })

  // NOTE: The real API implementation is commented out due to 500 errors
  // Uncomment this and comment out the mock implementation when the API is fixed
  /*
  const {
    business,
    orderStats,
    todayStats,
    loading: isLoading,
    error: dashboardError,
    isPendingApproval,
    refetch: refetchDashboard
  } = useBusinessDashboardStats({
    businessId: (isAdminUser && selectedBusinessId) ? selectedBusinessId : userProfile?.business_id,
    isAdminUser,
    enabled: !!user,
    realtimeEnabled
  })
  */

  // For admin users, fetch available businesses
  const fetchAvailableBusinesses = async () => {
    if (!isAdminUser) return

    try {
      console.log("Admin user detected, fetching available businesses")

      // Get the authentication token from localStorage
      const token = localStorage.getItem('loop_jersey_auth_token') || '';

      const response = await fetch('/api/admin/businesses-direct', {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      })

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`)
      }

      const data = await response.json()
      console.log("Available businesses:", data)

      if (data && Array.isArray(data)) {
        setAvailableBusinesses(data.map((b: any) => ({
          id: b.id,
          name: b.name,
          business_type: b.business_type || b.business_types?.name || "Business"
        })))
      }
    } catch (err) {
      console.error("Error fetching available businesses:", err)
    }
  }

  // Handle business selection change for admin users
  const handleBusinessChange = (businessId: number) => {
    console.log("Selected business changed to:", businessId)
    setSelectedBusinessId(businessId)
  }

  // Initial data loading for admin users
  useEffect(() => {
    if (user && isAdminUser) {
      fetchAvailableBusinesses()
    }
  }, [user, isAdminUser])

  // Format currency helper
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
      minimumFractionDigits: 2
    }).format(amount)
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  // Error state
  if (dashboardError) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-red-500 text-5xl mb-4">⚠️</div>
          <h2 className="text-2xl font-bold mb-2">Error Loading Dashboard</h2>
          <p className="text-gray-600 mb-4">{dashboardError.message}</p>
          <Button onClick={() => refetchDashboard()}>Retry</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
        {/* Real-time indicator */}
        <div className="flex items-center justify-end gap-2">
          <div className="flex items-center gap-1.5">
            <div className={`h-2 w-2 rounded-full ${realtimeEnabled ? 'bg-green-500 animate-pulse' : 'bg-gray-300'}`}></div>
            <span className="text-xs text-muted-foreground">
              {realtimeEnabled ? 'Real-time updates enabled' : 'Real-time updates disabled'}
            </span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="h-7 px-2"
            onClick={() => setRealtimeEnabled(!realtimeEnabled)}
          >
            {realtimeEnabled ? 'Disable' : 'Enable'}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-7 px-2"
            onClick={() => refetchDashboard()}
          >
            <RefreshCcw className="h-3.5 w-3.5" />
          </Button>
        </div>

        {/* Pending approval warning */}
        {isPendingApproval && (
          <Alert variant="warning" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Pending Approval</AlertTitle>
            <AlertDescription>
              Your business is currently pending approval by the Loop Jersey team. Some features may be limited until your business is approved.
            </AlertDescription>
          </Alert>
        )}

        {/* New Dashboard Header */}
        <DashboardHeader
          realtimeEnabled={realtimeEnabled}
          onRealtimeToggle={(enabled) => setRealtimeEnabled(enabled)}
          dateRange={dateRange}
          onDateRangeChange={(range) => setDateRange(range)}
          onExport={() => {
            // Handle export functionality
            alert("Export functionality will be implemented here")
          }}
          activeTab={activeTab}
          onTabChange={(tab) => setActiveTab(tab)}
          onRefresh={() => refetchDashboard()}
        />

        {/* Admin business selector */}
        {isAdminUser && availableBusinesses.length > 0 && (
          <Card className="mt-4">
            <CardContent className="p-4">
              <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                <div className="text-sm font-medium">Admin View:</div>
                <Select
                  value={selectedBusinessId?.toString() || 'all'}
                  onValueChange={(value) => handleBusinessChange(value === 'all' ? 0 : Number(value))}
                >
                  <SelectTrigger className="w-[220px]">
                    <SelectValue placeholder="Select a business" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Businesses</SelectItem>
                    {availableBusinesses.map((b) => (
                      <SelectItem key={b.id} value={b.id.toString()}>
                        {b.name} ({b.business_type})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <div className="text-xs text-muted-foreground">
                  {business ? `Viewing data for: ${business.name}` : 'Select a business to view its data'}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <div className="space-y-4 pt-4">
          {activeTab === "overview" && (
            <div className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <StatsCard
                  title="Today's Revenue"
                  value={formatCurrency(todayStats.revenue)}
                  icon={DollarSign}
                  trend={{ value: "20.1%", positive: true }}
                  isLoading={isLoading}
                />

                <StatsCard
                  title="Today's Orders"
                  value={`+${todayStats.orders}`}
                  icon={ShoppingBag}
                  trend={{ value: "12.4%", positive: true }}
                  isLoading={isLoading}
                />

                <StatsCard
                  title="Average Order Value"
                  value={formatCurrency(todayStats.averageOrderValue)}
                  icon={CreditCard}
                  trend={{ value: "2.3%", positive: true }}
                  isLoading={isLoading}
                />

                <StatsCard
                  title="Active Customers"
                  value={`+${Math.max(1, Math.round(todayStats.orders * 0.8))}`}
                  icon={Users}
                  trend={{ value: "8.2%", positive: true }}
                  isLoading={isLoading}
                />
              </div>


              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
                <Card className="lg:col-span-4">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle>Revenue Overview</CardTitle>
                      <Badge variant="outline" className="text-xs">
                        {format(dateRange.from, "MMM d, yyyy")} - {format(dateRange.to, "MMM d, yyyy")}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="pl-2">
                    <RevenueChart isLoading={isLoading} />
                  </CardContent>
                </Card>
                <Card className="lg:col-span-3">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle>Fulfillment Performance</CardTitle>
                      <Badge variant="outline" className="text-xs">
                        {format(dateRange.from, "MMM d, yyyy")} - {format(dateRange.to, "MMM d, yyyy")}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <FulfillmentMetrics
                      metrics={{
                        averageTime: 24,
                        onTimeDelivery: 94.2,
                        orderAccuracy: 98.7,
                        customerSatisfaction: 4.8
                      }}
                      isLoading={isLoading}
                    />
                  </CardContent>
                </Card>
              </div>

              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
                <Card className="lg:col-span-3">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle>Revenue by Category</CardTitle>
                      <Badge variant="outline" className="text-xs">
                        {format(dateRange.from, "MMM d, yyyy")} - {format(dateRange.to, "MMM d, yyyy")}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <CategoryRevenueChart isLoading={isLoading} />
                  </CardContent>
                </Card>
                <Card className="lg:col-span-4">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle>Orders & Revenue by Day</CardTitle>
                      <Badge variant="outline" className="text-xs">
                        {format(dateRange.from, "MMM d, yyyy")} - {format(dateRange.to, "MMM d, yyyy")}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="pl-2">
                    <OrdersByDayChart isLoading={isLoading} />
                  </CardContent>
                </Card>
              </div>

              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-2">
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle>Top Products by Revenue</CardTitle>
                      <Badge variant="outline" className="text-xs">
                        {format(dateRange.from, "MMM d, yyyy")} - {format(dateRange.to, "MMM d, yyyy")}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <TopProductsList isLoading={isLoading} />
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle>Orders by Time of Day</CardTitle>
                      <Badge variant="outline" className="text-xs">
                        {format(dateRange.from, "MMM d, yyyy")} - {format(dateRange.to, "MMM d, yyyy")}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <OrdersByTimeChart isLoading={isLoading} />
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>Recent Orders</CardTitle>
                    <Badge variant="outline" className="text-xs">
                      Last 5 Orders
                    </Badge>
                  </div>
                  <CardDescription>Last 5 orders placed on your store</CardDescription>
                </CardHeader>
                <CardContent>
                  <RecentOrdersList isLoading={isLoading} />
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Link href="/business-admin/orders-new" className="text-sm text-muted-foreground hover:text-foreground">
                    View All Orders
                  </Link>
                  <Link
                    href="/business-admin/orders-enhanced"
                    className="text-sm font-medium text-emerald-600 hover:text-emerald-700 flex items-center"
                  >
                    Try Enhanced Orders
                    <Badge className="ml-2 bg-emerald-100 text-emerald-800 hover:bg-emerald-200 text-xs">New</Badge>
                  </Link>
                </CardFooter>
              </Card>
            </div>
          )}

          {activeTab === "analytics" && (
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>Performance Analytics</CardTitle>
                    <Badge variant="outline" className="text-xs">
                      {format(dateRange.from, "MMM d, yyyy")} - {format(dateRange.to, "MMM d, yyyy")}
                    </Badge>
                  </div>
                  <CardDescription>Detailed view of your business performance over time</CardDescription>
                </CardHeader>
                <CardContent className="pl-2">
                  <OverviewChart isLoading={isLoading} />
                </CardContent>
              </Card>

              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle>Sales Metrics</CardTitle>
                      <Badge variant="outline" className="text-xs">
                        {format(dateRange.from, "MMM d, yyyy")} - {format(dateRange.to, "MMM d, yyyy")}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <SalesMetrics isLoading={isLoading} />
                  </CardContent>
                  <CardFooter>
                    <Button
                      variant="outline"
                      className="w-full text-emerald-600 hover:bg-emerald-50 hover:text-emerald-700"
                    >
                      View detailed report
                    </Button>
                  </CardFooter>
                </Card>
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle>Customer Retention</CardTitle>
                      <Badge variant="outline" className="text-xs">
                        {format(dateRange.from, "MMM d, yyyy")} - {format(dateRange.to, "MMM d, yyyy")}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-center h-[180px]">
                      <div className="text-center">
                        <div className="text-5xl font-bold text-emerald-500">76%</div>
                        <div className="mt-2 text-sm text-muted-foreground">Returning customers</div>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button
                      variant="outline"
                      className="w-full text-emerald-600 hover:bg-emerald-50 hover:text-emerald-700"
                    >
                      View customer details
                    </Button>
                  </CardFooter>
                </Card>
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle>Delivery Performance</CardTitle>
                      <Badge variant="outline" className="text-xs">
                        {format(dateRange.from, "MMM d, yyyy")} - {format(dateRange.to, "MMM d, yyyy")}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex justify-between">
                        <div className="text-sm">Average delivery time</div>
                        <div className="font-medium">24 minutes</div>
                      </div>
                      <div className="flex justify-between">
                        <div className="text-sm">On-time delivery rate</div>
                        <div className="font-medium">94.2%</div>
                      </div>
                      <div className="flex justify-between">
                        <div className="text-sm">Customer satisfaction</div>
                        <div className="font-medium">4.8/5</div>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button
                      variant="outline"
                      className="w-full text-emerald-600 hover:bg-emerald-50 hover:text-emerald-700"
                    >
                      View delivery analytics
                    </Button>
                  </CardFooter>
                </Card>
              </div>
            </div>
          )}

          {activeTab === "reports" && (
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>Generated Reports</CardTitle>
                    <Badge variant="outline" className="text-xs">
                      Available Reports
                    </Badge>
                  </div>
                  <CardDescription>Access and download your business reports</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between border-b pb-4">
                      <div>
                        <div className="font-medium">Monthly Sales Report</div>
                        <div className="text-sm text-muted-foreground">{format(new Date(), "MMMM yyyy")}</div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-emerald-600 hover:bg-emerald-50 hover:text-emerald-700"
                      >
                        <Download className="mr-2 h-4 w-4" />
                        Download
                      </Button>
                    </div>
                    <div className="flex items-center justify-between border-b pb-4">
                      <div>
                        <div className="font-medium">Quarterly Performance</div>
                        <div className="text-sm text-muted-foreground">Q{Math.ceil((new Date().getMonth() + 1) / 3)} {new Date().getFullYear()}</div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-emerald-600 hover:bg-emerald-50 hover:text-emerald-700"
                      >
                        <Download className="mr-2 h-4 w-4" />
                        Download
                      </Button>
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">Inventory Status</div>
                        <div className="text-sm text-muted-foreground">Current</div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-emerald-600 hover:bg-emerald-50 hover:text-emerald-700"
                      >
                        <Download className="mr-2 h-4 w-4" />
                        Download
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
  )
}
