"use client";

import React from 'react';
import Link from 'next/link';
import { ArrowRight, ChevronLeftIcon, ChevronRightIcon } from 'lucide-react';
import PortraitBusinessCard from "@/components/portrait-business-card";
import { Button } from "@/components/ui/button";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious
} from "@/components/ui/carousel";

interface Business {
  id: string | number;
  slug: string;
  name: string;
  logo_url?: string;
  banner_url?: string;
  rating?: number;
  review_count?: number;
  delivery_time_minutes?: number;
  preparation_time_minutes?: number;
  delivery_fee?: number;
  delivery_radius?: number;
  minimum_order_amount?: number;
  location?: string;
  business_type: string;
  business_type_slug: string;
  offer?: string;
  dealType?: string;
}

interface PortraitDealsSectionProps {
  businesses: Business[];
}

export default function PortraitDealsSection({ businesses }: PortraitDealsSectionProps) {
  if (!businesses || businesses.length === 0) {
    return null;
  }

  return (
    <section className="py-4 container-fluid">
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center">
          <h2 className="text-xl font-bold">Deals</h2>
          <span className="ml-3 px-2 py-1 bg-red-100 text-red-600 text-xs font-semibold rounded-full">Limited Time</span>
        </div>
        <Link
          href="/deals"
          className="hidden md:flex items-center text-emerald-600 hover:text-emerald-700 transition-colors bg-white px-3 py-1.5 rounded-full shadow-sm hover:shadow border border-emerald-100"
        >
          <span className="text-sm font-medium mr-2">View all deals</span>
          <ArrowRight className="h-4 w-4" />
        </Link>
      </div>

      <div className="relative">
        <Carousel
          opts={{
            align: "start",
            loop: true,
            dragFree: true
          }}
          className="w-full"
        >
          {/* Custom navigation arrows positioned over the carousel content */}
          <Button
            onClick={() => document.querySelector('[data-carousel-prev]')?.click()}
            className="absolute left-4 top-1/2 -translate-y-1/2 z-30 hidden md:flex bg-white rounded-full shadow-md p-0 h-10 w-10 items-center justify-center hover:bg-gray-50 text-emerald-600 hover:text-emerald-700 transition-colors"
            size="icon"
            variant="outline"
            aria-label="Previous slide"
          >
            <ChevronLeftIcon className="h-6 w-6" />
          </Button>

          <Button
            onClick={() => document.querySelector('[data-carousel-next]')?.click()}
            className="absolute right-4 top-1/2 -translate-y-1/2 z-30 hidden md:flex bg-white rounded-full shadow-md p-0 h-10 w-10 items-center justify-center hover:bg-gray-50 text-emerald-600 hover:text-emerald-700 transition-colors"
            size="icon"
            variant="outline"
            aria-label="Next slide"
          >
            <ChevronRightIcon className="h-6 w-6" />
          </Button>

          {/* Hidden original carousel controls for functionality */}
          <div className="hidden">
            <CarouselPrevious data-carousel-prev className="h-9 w-9" />
            <CarouselNext data-carousel-next className="h-9 w-9" />
          </div>

          <CarouselContent className="px-0 mx-0 pl-0 pr-0">
            {businesses.map((business) => {
              // Add offer badge for deals with improved styling
              const offers = [
                {
                  text: business.dealType || "Special Offer",
                  color: business.dealType === 'Free Delivery' ? 'bg-emerald-500' :
                         business.dealType === 'Buy One Get One Free' ? 'bg-red-500' :
                         business.dealType?.includes('% Off') ? 'bg-orange-500' :
                         'bg-red-500'
                }
              ];

              return (
                <CarouselItem key={business.id} className="sm:basis-1/2 md:basis-1/3 lg:basis-1/4 xl:basis-1/5 px-2 h-full">
                  <div className="h-full">
                    <PortraitBusinessCard
                      id={business.slug || business.id.toString()}
                      name={business.name}
                      image={business.logo_url || "/placeholder.svg"}
                      businessType={business.business_type_slug || "business"}
                      rating={business.rating}
                      deliveryTime={business.delivery_time_minutes ? `${business.delivery_time_minutes}` : "30"}
                      deliveryTimeRange={business.deliveryTimeRange || "25-35 min"}
                      deliveryFee={business.delivery_fee_formatted || (business.delivery_fee === 0
                        ? "Free delivery"
                        : `£${business.delivery_fee?.toFixed(2) || "0.00"}`)}
                      distance={business.distance || (business.delivery_radius ? `${business.delivery_radius} km` : undefined)}
                      sponsored={false}
                      offers={offers}
                      location={business.location}
                      deliveryRadius={business.delivery_radius ? `${business.delivery_radius} miles` : undefined}
                      preparationTime={business.preparation_time_minutes ? `${business.preparation_time_minutes} min` : undefined}
                      className="h-full"
                    />
                  </div>
                </CarouselItem>
              );
            })}
          </CarouselContent>
        </Carousel>
      </div>
    </section>
  );
}
