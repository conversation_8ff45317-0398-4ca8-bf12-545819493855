import { NextResponse } from "next/server"
import { cookies } from "next/headers"
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs"
import { createClient } from "@supabase/supabase-js"

// Get Supabase credentials from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // First, verify the user is authenticated and has business manager permissions
    const cookieStore = cookies()
    const authClient = createServerComponentClient({ cookies: () => cookieStore })
    const adminClient = createClient(supabaseUrl, supabaseServiceKey)

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession()

    if (sessionError || !session) {
      console.error("No authenticated session found:", sessionError)
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Get the user's profile to check their role
    const { data: userProfile, error: profileError } = await adminClient
      .from("users")
      .select("id, role")
      .eq("email", session.user.email)
      .single()

    if (profileError || !userProfile) {
      console.error("Failed to get user profile:", profileError)
      return NextResponse.json(
        { error: "User profile not found" },
        { status: 403 }
      )
    }

    // Check if user is a business manager, admin, or super_admin
    const isBusinessManager = userProfile.role === "business_manager"
    const isBusinessStaff = userProfile.role === "business_staff"
    const isAdmin = userProfile.role === "admin"
    const isSuperAdmin = userProfile.role === "super_admin"

    if (!isBusinessManager && !isBusinessStaff && !isAdmin && !isSuperAdmin) {
      console.error("User does not have business manager permissions:", userProfile.role)
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    const orderId = parseInt(params.id)

    if (isNaN(orderId)) {
      return NextResponse.json(
        { error: "Invalid order ID" },
        { status: 400 }
      )
    }

    // For admin users, allow viewing any order
    if (isAdmin || isSuperAdmin) {
      // Try to use order_details_view first
      const { data: orderDetails, error: detailsError } = await adminClient
        .from("order_details_view")
        .select("*")
        .eq("id", orderId)
        .single()

      if (detailsError) {
        console.error("Error fetching order details from view:", detailsError)

        // Fall back to manual joins
        const { data: order, error: orderError } = await adminClient
          .from("orders")
          .select(`
            *,
            users (id, name, email, phone),
            businesses (id, name, address, phone),
            order_status_history(*),
            order_payment_allocations(*)
          `)
          .eq("id", orderId)
          .single()

        if (orderError) {
          console.error("Error fetching order:", orderError)
          return NextResponse.json(
            { error: "Order not found" },
            { status: 404 }
          )
        }

        // Get order items
        const { data: items, error: itemsError } = await adminClient
          .from("order_items_view")
          .select("*")
          .eq("order_id", orderId)

        if (itemsError) {
          console.error("Error fetching order items from view:", itemsError)

          // Fall back to manual joins for items
          const { data: fallbackItems, error: fallbackItemsError } = await adminClient
            .from("order_items")
            .select(`
              *,
              products (id, name, description, price, image_url),
              product_variants (id, name, price)
            `)
            .eq("order_id", orderId)

          if (fallbackItemsError) {
            console.error("Error fetching order items:", fallbackItemsError)
            // Continue without items
          }

          return NextResponse.json({
            order,
            items: fallbackItems || []
          })
        }

        return NextResponse.json({
          order,
          items
        })
      }

      // Get order items if using the view
      const { data: items, error: itemsError } = await adminClient
        .from("order_items_view")
        .select("*")
        .eq("order_id", orderId)

      if (itemsError) {
        console.error("Error fetching order items from view:", itemsError)

        // Fall back to manual joins for items
        const { data: fallbackItems, error: fallbackItemsError } = await adminClient
          .from("order_items")
          .select(`
            *,
            products (id, name, description, price, image_url),
            product_variants (id, name, price)
          `)
          .eq("order_id", orderId)

        if (fallbackItemsError) {
          console.error("Error fetching order items:", fallbackItemsError)
          // Continue without items
        }

        return NextResponse.json({
          order: orderDetails,
          items: fallbackItems || []
        })
      }

      return NextResponse.json({
        order: orderDetails,
        items
      })
    }

    // For business users, verify they own the order
    let businessId: number | null = null

    // Check if user is a business manager
    if (isBusinessManager) {
      const { data: managerData, error: managerError } = await adminClient
        .from("business_managers")
        .select("business_id")
        .eq("user_id", userProfile.id)
        .single()

      if (managerError) {
        console.error("Error fetching business manager data:", managerError)
        return NextResponse.json(
          { error: "Business manager data not found" },
          { status: 404 }
        )
      }

      businessId = managerData.business_id
    }

    // Check if user is a business staff
    if (isBusinessStaff) {
      const { data: staffData, error: staffError } = await adminClient
        .from("business_staff")
        .select("business_id")
        .eq("user_id", userProfile.id)
        .single()

      if (staffError) {
        console.error("Error fetching business staff data:", staffError)
        return NextResponse.json(
          { error: "Business staff data not found" },
          { status: 404 }
        )
      }

      businessId = staffData.business_id
    }

    if (!businessId) {
      return NextResponse.json(
        { error: "No business found for this user" },
        { status: 404 }
      )
    }

    // Verify the order belongs to this business
    const { data: order, error: orderError } = await adminClient
      .from("orders")
      .select("id, business_id")
      .eq("id", orderId)
      .single()

    if (orderError) {
      console.error("Error fetching order:", orderError)
      return NextResponse.json(
        { error: "Order not found" },
        { status: 404 }
      )
    }

    if (order.business_id !== businessId) {
      return NextResponse.json(
        { error: "You do not have permission to view this order" },
        { status: 403 }
      )
    }

    // Try to use order_details_view first
    const { data: orderDetails, error: detailsError } = await adminClient
      .from("order_details_view")
      .select("*")
      .eq("id", orderId)
      .single()

    if (detailsError) {
      console.error("Error fetching order details from view:", detailsError)

      // Fall back to manual joins
      const { data: orderData, error: orderDataError } = await adminClient
        .from("orders")
        .select(`
          *,
          users (id, name, email, phone),
          businesses (id, name, address, phone),
          order_status_history(*),
          order_payment_allocations(*)
        `)
        .eq("id", orderId)
        .single()

      if (orderDataError) {
        console.error("Error fetching order:", orderDataError)
        return NextResponse.json(
          { error: "Order not found" },
          { status: 404 }
        )
      }

      // Get order items
      const { data: items, error: itemsError } = await adminClient
        .from("order_items_view")
        .select("*")
        .eq("order_id", orderId)

      if (itemsError) {
        console.error("Error fetching order items from view:", itemsError)

        // Fall back to manual joins for items
        const { data: fallbackItems, error: fallbackItemsError } = await adminClient
          .from("order_items")
          .select(`
            *,
            products (id, name, description, price, image_url),
            product_variants (id, name, price)
          `)
          .eq("order_id", orderId)

        if (fallbackItemsError) {
          console.error("Error fetching order items:", fallbackItemsError)
          // Continue without items
        }

        return NextResponse.json({
          order: orderData,
          items: fallbackItems || []
        })
      }

      return NextResponse.json({
        order: orderData,
        items
      })
    }

    // Get order items if using the view
    const { data: items, error: itemsError } = await adminClient
      .from("order_items_view")
      .select("*")
      .eq("order_id", orderId)

    if (itemsError) {
      console.error("Error fetching order items from view:", itemsError)

      // Fall back to manual joins for items
      const { data: fallbackItems, error: fallbackItemsError } = await adminClient
        .from("order_items")
        .select(`
          *,
          products (id, name, description, price, image_url)
        `)
        .eq("order_id", orderId)

      if (fallbackItemsError) {
        console.error("Error fetching order items:", fallbackItemsError)
        // Continue without items
      }

      return NextResponse.json({
        order: orderDetails,
        items: fallbackItems || []
      })
    }

    return NextResponse.json({
      order: orderDetails,
      items
    })
  } catch (error: any) {
    console.error("Error in GET /api/business-admin/orders/[id]:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
