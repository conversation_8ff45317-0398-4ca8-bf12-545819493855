import { createClient } from '@supabase/supabase-js'

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

interface OrderStatusUpdate {
  orderId: number // INTEGER based on actual schema
  newStatus: string
  notes?: string
  updatedBy: string
  businessId?: string
}

interface NotificationConfig {
  shouldPush: boolean
  priority: number
  isUrgent: boolean
  title: string
  body: string
}

export async function updateOrderStatusWithNotifications({
  orderId,
  newStatus,
  notes,
  updatedBy,
  businessId
}: OrderStatusUpdate) {

  try {
    // Start transaction
    const { data: order, error: orderError } = await supabaseAdmin
      .from('orders')
      .select(`
        *,
        order_businesses!inner(business_id, business_name)
      `)
      .eq('id', orderId)
      .single()

    if (orderError || !order) {
      throw new Error(`Order not found: ${orderError?.message}`)
    }

    // 1. Update order status
    const { error: updateError } = await supabaseAdmin
      .from('orders')
      .update({
        status: newStatus,
        order_status: newStatus,
        updated_at: new Date().toISOString()
      })
      .eq('id', orderId)

    if (updateError) {
      throw new Error(`Failed to update order: ${updateError.message}`)
    }

    // 2. Add status history entry
    const { error: historyError } = await supabaseAdmin
      .from('order_status_history')
      .insert({
        order_id: orderId,
        status: newStatus,
        notes: notes || `Status updated to ${newStatus}`,
        created_by: updatedBy,
        created_at: new Date().toISOString()
      })

    if (historyError) {
      console.warn('Failed to create status history:', historyError.message)
    }

    // 3. Create communication notification
    const notificationConfig = getNotificationConfig(newStatus, order)

    if (notificationConfig) {
      await createOrderStatusCommunication({
        orderId,
        customerId: order.user_id,
        businessId: businessId || order.order_businesses[0]?.business_id,
        businessName: order.order_businesses[0]?.business_name,
        status: newStatus,
        notes,
        updatedBy,
        config: notificationConfig
      })
    }

    return { success: true, order }

  } catch (error: any) {
    console.error('Error in enhanced status update:', error)
    throw error
  }
}

function getNotificationConfig(status: string, order: any): NotificationConfig | null {
  const businessName = order.order_businesses[0]?.business_name || 'Business'

  switch (status) {
    case 'confirmed':
      return {
        shouldPush: true,
        priority: 1,
        isUrgent: false,
        title: 'Order Confirmed',
        body: `Your order from ${businessName} has been confirmed`
      }

    case 'preparing':
      return {
        shouldPush: true,
        priority: 0,
        isUrgent: false,
        title: 'Order Being Prepared',
        body: `${businessName} is preparing your order`
      }

    case 'ready':
      return {
        shouldPush: true,
        priority: 1,
        isUrgent: true,
        title: 'Order Ready',
        body: order.delivery_type === 'pickup'
          ? `Your order is ready for pickup at ${businessName}`
          : `Your order is ready for delivery from ${businessName}`
      }

    case 'out_for_delivery':
      return {
        shouldPush: true,
        priority: 1,
        isUrgent: true,
        title: 'Out for Delivery',
        body: `Your order from ${businessName} is out for delivery`
      }

    case 'delivered':
      return {
        shouldPush: true,
        priority: 1,
        isUrgent: false,
        title: 'Order Delivered',
        body: `Your order from ${businessName} has been delivered`
      }

    case 'cancelled':
      return {
        shouldPush: true,
        priority: 2,
        isUrgent: true,
        title: 'Order Cancelled',
        body: `Your order from ${businessName} has been cancelled`
      }

    default:
      // For 'pending' and other statuses, create in-app notification only
      return {
        shouldPush: false,
        priority: 0,
        isUrgent: false,
        title: 'Order Update',
        body: `Order status updated to ${status}`
      }
  }
}

async function createOrderStatusCommunication({
  orderId,
  customerId,
  businessId,
  businessName,
  status,
  notes,
  updatedBy,
  config
}: {
  orderId: string
  customerId: string
  businessId: string
  businessName: string
  status: string
  notes?: string
  updatedBy: string
  config: NotificationConfig
}) {

  // Create communication entry
  const { error: commError } = await supabaseAdmin
    .from('communications')
    .insert({
      sender_id: updatedBy, // Business user who updated status
      recipient_id: customerId,
      order_id: orderId,
      business_id: businessId,
      channel_type: 'active_order_delivery',
      message_type: 'status_update',
      subject: config.title,
      content: notes || config.body,
      is_automated: true,
      is_urgent: config.isUrgent,
      priority: config.priority,
      created_at: new Date().toISOString()
    })

  if (commError) {
    console.error('Failed to create communication:', commError.message)
  }

  // If should push, trigger push notification
  if (config.shouldPush) {
    await triggerPushNotification({
      userId: customerId,
      title: config.title,
      body: config.body,
      data: {
        type: 'order_update',
        orderId,
        status,
        businessId,
        url: `/orders/${orderId}`
      }
    })
  }
}

async function triggerPushNotification({
  userId,
  title,
  body,
  data
}: {
  userId: string
  title: string
  body: string
  data: any
}) {

  try {
    // Get user's push subscriptions
    const { data: subscriptions } = await supabaseAdmin
      .from('push_subscriptions')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true)

    if (!subscriptions || subscriptions.length === 0) {
      console.log(`No push subscriptions found for user ${userId}`)
      return
    }

    // Send push notification to each subscription
    for (const subscription of subscriptions) {
      await sendPushNotification(subscription.subscription_data, {
        title,
        body,
        icon: '/android-chrome-192x192.png',
        badge: '/favicon-32x32.png',
        data
      })
    }

  } catch (error) {
    console.error('Error sending push notification:', error)
  }
}

async function sendPushNotification(subscription: any, payload: any) {
  // This would use a service like web-push or Firebase
  // For now, just log the notification
  console.log('Push notification sent:', { subscription, payload })

  // TODO: Implement actual push notification sending
  // Example with web-push:
  // const webpush = require('web-push')
  // await webpush.sendNotification(subscription, JSON.stringify(payload))
}
