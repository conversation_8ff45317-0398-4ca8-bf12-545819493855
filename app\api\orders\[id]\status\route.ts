import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { updateOrderStatus } from '@/app/api/orders/update-order';

// Create Supabase client with service role key (only available server-side)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Helper function to verify user access
async function verifyUserAccess(request: NextRequest) {
  // Get the authorization header
  const authorization = request.headers.get('Authorization');

  // Check if we have an authorization header
  if (!authorization) {
    console.log("No authorization header found in orders/[id]/status API")
    // Skip auth check in development for easier testing
    if (process.env.NODE_ENV === 'development') {
      console.log("Development mode: Skipping auth check in orders/[id]/status API")
      return { authorized: true };
    } else {
      return {
        authorized: false,
        error: "Authentication required",
        status: 401
      };
    }
  }

  console.log("Found authorization header in orders/[id]/status API, attempting to verify")

  // Extract the token
  const token = authorization.replace('Bearer ', '');

  try {
    // Verify the token
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      console.error("Invalid token in orders/[id]/status API:", error)
      return {
        authorized: false,
        error: "Invalid authentication token",
        status: 401
      };
    }

    console.log("Token verified for user in orders/[id]/status API:", user.email)
    return { authorized: true, user };
  } catch (authError) {
    console.error("Error verifying token in orders/[id]/status API:", authError)
    // Continue anyway in development mode
    if (process.env.NODE_ENV !== 'development') {
      return {
        authorized: false,
        error: "Authentication error",
        status: 401
      };
    } else {
      console.log("Development mode: Continuing despite auth error in orders/[id]/status API")
      return { authorized: true };
    }
  }
}

// PATCH endpoint to update an order's status
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify user access
    const accessCheck = await verifyUserAccess(request);
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      );
    }

    // Get the user from the access check
    const user = accessCheck.user;

    // Get the order ID from the URL
    const orderId = params.id;

    if (!orderId) {
      return NextResponse.json(
        { error: 'Order ID is required' },
        { status: 400 }
      );
    }

    // Parse the request body
    const body = await request.json();

    if (!body.status) {
      return NextResponse.json(
        { error: 'Status is required' },
        { status: 400 }
      );
    }

    // Update the order status using our helper function
    const result = await updateOrderStatus(
      parseInt(orderId),
      body.status
    );

    if (!result.success) {
      console.error('Error updating order status:', result.error);
      return NextResponse.json(
        { error: 'Failed to update order status', details: result.error },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      order: result.order
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}
