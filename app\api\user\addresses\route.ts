import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import { cookies } from "next/headers";
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs";
import { createServerClient } from "@/lib/supabase-server";

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const adminClient = createClient(supabaseUrl, supabaseServiceKey);

// GET handler to fetch user addresses
export async function GET(request: Request) {
  try {
    console.log("GET /api/user/addresses: Starting request");

    // Get the authorization header
    const authHeader = request.headers.get('Authorization')
    let email = null;
    let user = null;

    // First try to get user from Authorization header if present
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7)
      if (token) {
        console.log("GET /api/user/addresses: Found Authorization header, attempting to use token")
        try {
          // Use admin client to verify the token
          const { data: userData, error } = await adminClient.auth.getUser(token)

          if (!error && userData?.user) {
            console.log("GET /api/user/addresses: Successfully verified token for user:", userData.user.email)
            user = userData.user
            email = userData.user.email
          } else if (error) {
            console.error("GET /api/user/addresses: Error getting user from token:", error)
            // Log more details about the token for debugging
            console.log("Token details:", {
              length: token.length,
              firstChars: token.substring(0, 10) + '...',
              lastChars: '...' + token.substring(token.length - 10)
            })
          }
        } catch (e) {
          console.error("GET /api/user/addresses: Exception using Authorization token:", e)
        }
      }
    }

    // If we couldn't get user from token, try cookies
    if (!user) {
      console.log("GET /api/user/addresses: No valid token found, trying cookie-based session")
      try {
        // Get the user's session using the server client
        const supabase = await createServerClient()
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()

        if (sessionError) {
          console.error("GET /api/user/addresses: Session error:", sessionError)
        } else if (session?.user) {
          console.log("GET /api/user/addresses: Found user in cookie session:", session.user.email)
          user = session.user
          email = session.user.email
        }
      } catch (cookieError) {
        console.error("GET /api/user/addresses: Error getting session from cookies:", cookieError)
      }
    }

    // Skip auth check in development for easier testing
    if (!user && process.env.NODE_ENV === 'development') {
      // Extract email from query params for development testing
      const url = new URL(request.url)
      const devEmail = url.searchParams.get('dev_email')

      if (devEmail) {
        console.log("GET /api/user/addresses: Development mode: Using email from query param:", devEmail)
        email = devEmail
      } else {
        console.log("GET /api/user/addresses: Development mode: No auth found and no dev_email param")
      }
    } else if (!user) {
      console.log("GET /api/user/addresses: No authenticated user found")
      return NextResponse.json(
        { error: "Not authenticated" },
        { status: 401 }
      )
    }

    if (!email) {
      console.log("GET /api/user/addresses: User email not found in session");
      return NextResponse.json(
        { error: "User email not found" },
        { status: 400 }
      );
    }

    console.log("GET /api/user/addresses: Authenticated user:", email);

    // Get user ID from the users table using admin client
    try {
      console.log("GET /api/user/addresses: Fetching user ID for email:", email);

      const { data: userData, error: userError } = await adminClient
        .from("users")
        .select("id")
        .eq("email", email)
        .single();

      if (userError) {
        console.error("GET /api/user/addresses: Error fetching user ID:", userError);
        console.error("Error details:", {
          code: userError.code,
          message: userError.message,
          details: userError.details,
          hint: userError.hint
        });

        // If user doesn't exist yet, return empty addresses array instead of error
        if (userError.code === 'PGRST116') {
          console.log("GET /api/user/addresses: User not found in database, returning empty addresses array");
          return NextResponse.json({ addresses: [] });
        }

        return NextResponse.json(
          { error: "Error fetching user data: " + userError.message },
          { status: 500 }
        );
      }

      if (!userData) {
        console.log("GET /api/user/addresses: User not found in database, returning empty addresses array");
        return NextResponse.json({ addresses: [] });
      }

      console.log("GET /api/user/addresses: Found user ID:", userData.id);

      // Fetch user addresses using admin client
      console.log("GET /api/user/addresses: Fetching addresses for user ID:", userData.id);

      const { data: addresses, error: addressError } = await adminClient
        .from("user_addresses")
        .select("*")
        .eq("user_id", userData.id)
        .order("is_default", { ascending: false })
        .order("created_at", { ascending: false });

      if (addressError) {
        console.error("GET /api/user/addresses: Error fetching addresses:", addressError);
        console.error("Error details:", {
          code: addressError.code,
          message: addressError.message,
          details: addressError.details,
          hint: addressError.hint
        });

        return NextResponse.json(
          { error: "Failed to fetch addresses: " + addressError.message },
          { status: 500 }
        );
      }

      console.log(`GET /api/user/addresses: Successfully fetched ${addresses?.length || 0} addresses`);
      return NextResponse.json({ addresses: addresses || [] });
    } catch (dbError) {
      console.error("GET /api/user/addresses: Database error:", dbError);
      return NextResponse.json(
        { error: "Database error occurred" },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error("Unexpected error in GET /api/user/addresses:", error);

    // Try to extract more detailed error information
    let errorMessage = "An unexpected error occurred";

    if (error.message) {
      errorMessage = error.message;
    }

    if (error.code) {
      errorMessage += ` (Code: ${error.code})`;
    }

    // Return a more detailed error response
    return NextResponse.json(
      {
        error: errorMessage,
        addresses: [] // Return empty addresses array to prevent UI errors
      },
      { status: 500 }
    );
  }
}

// POST handler to create a new address
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { address_name, address_line1, address_line2, parish, postcode, is_default } = body;

    // Validate required fields
    if (!address_line1 || !parish || !postcode) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Get the authorization header
    const authHeader = request.headers.get('Authorization')
    let email = null;
    let user = null;

    // First try to get user from Authorization header if present
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7)
      if (token) {
        console.log("POST /api/user/addresses: Found Authorization header, attempting to use token")
        try {
          // Use admin client to verify the token
          const { data: userData, error } = await adminClient.auth.getUser(token)

          if (!error && userData?.user) {
            console.log("POST /api/user/addresses: Successfully verified token for user:", userData.user.email)
            user = userData.user
            email = userData.user.email
          } else if (error) {
            console.error("POST /api/user/addresses: Error getting user from token:", error)
          }
        } catch (e) {
          console.error("POST /api/user/addresses: Exception using Authorization token:", e)
        }
      }
    }

    // If we couldn't get user from token, try cookies
    if (!user) {
      console.log("POST /api/user/addresses: No valid token found, trying cookie-based session")
      try {
        // Get the user's session using the server client
        const supabase = await createServerClient()
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()

        if (sessionError) {
          console.error("POST /api/user/addresses: Session error:", sessionError)
        } else if (session?.user) {
          console.log("POST /api/user/addresses: Found user in cookie session:", session.user.email)
          user = session.user
          email = session.user.email
        }
      } catch (cookieError) {
        console.error("POST /api/user/addresses: Error getting session from cookies:", cookieError)
      }
    }

    // Skip auth check in development for easier testing
    if (!user && process.env.NODE_ENV === 'development') {
      // Extract email from query params for development testing
      const url = new URL(request.url)
      const devEmail = url.searchParams.get('dev_email')

      if (devEmail) {
        console.log("POST /api/user/addresses: Development mode: Using email from query param:", devEmail)
        email = devEmail
      } else {
        console.log("POST /api/user/addresses: Development mode: No auth found and no dev_email param")
      }
    } else if (!user) {
      console.log("POST /api/user/addresses: No authenticated user found")
      return NextResponse.json(
        { error: "Not authenticated" },
        { status: 401 }
      )
    }

    if (!email) {
      console.log("POST /api/user/addresses: User email not found in session");
      return NextResponse.json(
        { error: "User email not found" },
        { status: 400 }
      );
    }

    console.log("POST /api/user/addresses: Authenticated user:", email);

    // Get user ID from the users table using admin client
    console.log("POST /api/user/addresses: Fetching user ID for email:", email);

    const { data: existingUser, error: userError } = await adminClient
      .from("users")
      .select("id")
      .eq("email", email)
      .single();

    let userId;

    if (userError) {
      console.error("POST /api/user/addresses: Error fetching user ID:", userError);
      console.error("Error details:", {
        code: userError.code,
        message: userError.message,
        details: userError.details,
        hint: userError.hint
      });

      // If user doesn't exist, create a new user record
      if (userError.code === 'PGRST116') {
        console.log("POST /api/user/addresses: User not found, creating new user record");
        const defaultName = email.split('@')[0] || 'User';

        const { data: newUser, error: createUserError } = await adminClient
          .from("users")
          .insert({
            email: email,
            name: defaultName,
            first_name: null,
            last_name: null,
            role: email.includes('dominos') ? "business_manager" : "customer",
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single();

        if (createUserError) {
          console.error("POST /api/user/addresses: Error creating user:", createUserError);
          console.error("Error details:", {
            code: createUserError.code,
            message: createUserError.message,
            details: createUserError.details,
            hint: createUserError.hint
          });
          return NextResponse.json(
            { error: "Failed to create user profile: " + createUserError.message },
            { status: 500 }
          );
        }

        // Use the newly created user
        if (newUser) {
          userId = newUser.id;
        } else {
          return NextResponse.json(
            { error: "Failed to create user profile" },
            { status: 500 }
          );
        }
      } else {
        return NextResponse.json(
          { error: "Error fetching user data: " + userError.message },
          { status: 500 }
        );
      }
    } else if (existingUser) {
      userId = existingUser.id;
    } else {
      console.error("User not found and could not be created");
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Check if an address with the same name already exists for this user
    const { data: existingAddresses, error: checkAddressError } = await adminClient
      .from("user_addresses")
      .select("id, address_name")
      .eq("user_id", userId)
      .eq("address_name", address_name);

    if (checkAddressError) {
      console.error("Error checking existing addresses:", checkAddressError);
    } else if (existingAddresses && existingAddresses.length > 0) {
      return NextResponse.json(
        { error: `You already have an address named "${address_name}". Please use a different name.` },
        { status: 400 }
      );
    }

    // If setting as default, first unset any existing default
    if (is_default) {
      await adminClient
        .from("user_addresses")
        .update({ is_default: false })
        .eq("user_id", userId)
        .eq("is_default", true);
    }

    // Create the new address
    const { data: newAddress, error: createError } = await adminClient
      .from("user_addresses")
      .insert({
        user_id: userId,
        address_name: address_name || "Home",
        address_line1,
        address_line2: address_line2 || null,
        parish,
        postcode,
        is_default: is_default || false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (createError) {
      console.error("Error creating address:", createError);
      return NextResponse.json(
        { error: "Failed to create address" },
        { status: 500 }
      );
    }

    // Geocode the address and update coordinates
    try {
      const { geocodeAndUpdateUserAddressCoordinates } = await import('@/lib/address-utils');
      await geocodeAndUpdateUserAddressCoordinates(
        newAddress.id,
        address_line1,
        address_line2 || null,
        parish,
        postcode
      );

      // Get the updated address with coordinates
      const { data: updatedAddress } = await adminClient
        .from("user_addresses")
        .select("*")
        .eq("id", newAddress.id)
        .single();

      if (updatedAddress) {
        return NextResponse.json({
          message: "Address created successfully with coordinates",
          address: updatedAddress
        });
      }
    } catch (geocodeError) {
      console.error("Error geocoding address:", geocodeError);
      // Continue anyway, as the address was created successfully
    }

    return NextResponse.json({
      message: "Address created successfully",
      address: newAddress
    });
  } catch (error: any) {
    console.error("Unexpected error in POST /api/user/addresses:", error);

    // Try to extract more detailed error information
    let errorMessage = "An unexpected error occurred";

    if (error.message) {
      errorMessage = error.message;
    }

    if (error.code) {
      errorMessage += ` (Code: ${error.code})`;
    }

    // Return a more detailed error response
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
