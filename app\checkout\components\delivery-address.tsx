"use client";

import React from "react";
import { MapPin, CheckCircle, Plus, AlertTriangle } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { SimpleCheckbox } from "@/components/ui/simple-checkbox";
import { JerseyAddressInput, SavedAddressesSelector, DeliveryMapAdapter } from "@/components/delivery";
import { useCheckout } from "../checkout-context";

export const DeliveryAddress: React.FC = () => {
  const {
    address,
    setAddress,
    parish,
    setParish,
    postcode,
    setPostcode,
    instructions,
    setInstructions,
    saveAddress,
    setSaveAddress,
    customerCoords,
    setCustomerCoords,
    isAddressFormExpanded,
    setIsAddressFormExpanded,
    hasSelectedSavedAddress,
    setHasSelectedSavedAddress,
    stepsCompleted,
    goToNextStep,
    goToPrevStep,
    user,
    businessDetails
  } = useCheckout();

  // Extract business coordinates from business details
  const businessCoordinates = React.useMemo(() => {
    const coordinates: Record<string, [number, number]> = {};

    if (businessDetails) {
      Object.entries(businessDetails).forEach(([businessId, details]) => {
        if (details.coordinates) {
          coordinates[businessId] = details.coordinates;
        }
      });
    }

    return coordinates;
  }, [businessDetails]);

  return (
    <>
      {user && (
        <div className="mb-8">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-medium text-gray-800">Your Saved Addresses</h3>
          </div>
          <SavedAddressesSelector
            onSelectAddress={(savedAddress) => {
              setAddress(savedAddress.address);
              setPostcode(savedAddress.postcode);

              // Extract parish from address if possible
              const addressParts = savedAddress.address.split(',').map(part => part.trim());
              const jerseyParishes = [
                "St Helier", "St Brelade", "St Clement", "Grouville",
                "St John", "St Lawrence", "St Martin", "St Mary",
                "St Ouen", "St Peter", "St Saviour", "Trinity"
              ];

              // Look for a parish in the address
              let foundParish = '';
              for (const part of addressParts) {
                for (const parishName of jerseyParishes) {
                  if (part.toLowerCase().includes(parishName.toLowerCase())) {
                    foundParish = parishName;
                    break;
                  }
                }
                if (foundParish) break;
              }

              if (foundParish) {
                setParish(foundParish);
              }

              if (savedAddress.coordinates) {
                setCustomerCoords(savedAddress.coordinates);
              }

              // Collapse the address form when a saved address is selected
              setIsAddressFormExpanded(false);
              setHasSelectedSavedAddress(true);
            }}
          />
        </div>
      )}

      <div className="border-t border-dashed border-gray-200 pt-6 mt-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <div className="bg-emerald-100 p-2 rounded-full mr-3">
              <MapPin className="h-5 w-5 text-emerald-600" />
            </div>
            <h3 className="font-medium text-lg text-gray-800">
              {user ? "Add New Delivery Address" : "Delivery Address"}
            </h3>
          </div>

          {user && hasSelectedSavedAddress && !isAddressFormExpanded && (
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsAddressFormExpanded(true)}
              className="flex items-center text-emerald-600 border-emerald-200"
            >
              <Plus className="h-4 w-4 mr-1" />
              Add New Address
            </Button>
          )}
        </div>

        {(!hasSelectedSavedAddress || isAddressFormExpanded) && (
          <div className="bg-white border border-gray-200 rounded-lg p-5 mb-6 shadow-sm">
            <div className="mb-4">
              <Label htmlFor="address" className="text-gray-700 font-medium">Street Address <span className="text-red-500">*</span></Label>
              <JerseyAddressInput
                value={address}
                onChange={(value, coords, selectedParish) => {
                  // Update state in a single batch to minimize re-renders
                  const hasChanges = value !== address ||
                      (coords && (!customerCoords ||
                                coords[0] !== customerCoords[0] ||
                                coords[1] !== customerCoords[1])) ||
                      (selectedParish && selectedParish !== parish);

                  if (hasChanges) {
                    // Update all values at once
                    setAddress(value);

                    if (coords) {
                      setCustomerCoords(coords);
                    }

                    if (selectedParish && selectedParish !== parish) {
                      setParish(selectedParish);
                    }
                  }
                }}
                onParishChange={(selectedParish) => {
                  // Only update if there's a real change
                  if (selectedParish !== parish) {
                    setParish(selectedParish);
                  }
                }}
                required
                placeholder="Enter your delivery address"
                className="mt-1"
              />
            </div>

            <div className="mb-4">
              <Label htmlFor="postcode" className="text-gray-700 font-medium">Postcode <span className="text-red-500">*</span></Label>
              <Input
                id="postcode"
                value={postcode}
                onChange={(e) => {
                  const newPostcode = e.target.value;
                  // Only update if there's a real change
                  if (newPostcode !== postcode) {
                    setPostcode(newPostcode);
                  }
                }}
                required
                placeholder="JE2 3NN"
                className="mt-1"
              />
            </div>

            <div className="mb-4">
              <Label htmlFor="instructions" className="text-gray-700 font-medium">Delivery Instructions (Optional)</Label>
              <Textarea
                id="instructions"
                value={instructions}
                onChange={(e) => setInstructions(e.target.value)}
                placeholder="Any special instructions for delivery (e.g., gate code, landmark, etc.)"
                className="h-24 mt-1"
              />
            </div>

            {user && (
              <div className="flex items-center p-3 bg-emerald-50 rounded-lg border border-emerald-100">
                <SimpleCheckbox
                  id="saveAddress"
                  checked={saveAddress}
                  onCheckedChange={(checked) => {
                    setSaveAddress(checked);
                  }}
                  className="text-emerald-600"
                />
                <div className="ml-2">
                  <Label htmlFor="saveAddress" className="cursor-pointer text-gray-800 font-medium">
                    Save this address for future orders
                  </Label>
                  <p className="text-xs text-gray-600 mt-0.5">
                    This will be added to your saved addresses for easy selection next time
                  </p>
                </div>
              </div>
            )}
          </div>
        )}

        {user && hasSelectedSavedAddress && !isAddressFormExpanded && (
          <div className="bg-emerald-50 p-4 rounded-lg border border-emerald-100 mb-6">
            <div className="flex items-center">
              <CheckCircle className="h-5 w-5 text-emerald-600 mr-2" />
              <p className="text-emerald-800 font-medium">Using saved address for delivery</p>
            </div>
            <p className="text-sm text-emerald-700 mt-1 ml-7">{address}</p>
          </div>
        )}
      </div>

      {customerCoords && (
        <div className="mt-6 border rounded-lg overflow-hidden">
          <div className="bg-gray-50 p-2 text-sm font-medium text-gray-700 border-b">
            Delivery Map
          </div>
          <div className="p-0">
            {/* Wrap the map in an error boundary */}
            <div className="relative">
              <DeliveryMapAdapter
                customerCoordinates={customerCoords}
                businessCoordinates={businessCoordinates}
              />
              {/* Fallback message that appears if the map fails to load */}
              <div className="hidden absolute inset-0 bg-gray-100 items-center justify-center text-gray-500 map-error-fallback">
                Unable to load the delivery map. Please check your address.
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-between mt-6">
        <Button
          type="button"
          variant="outline"
          onClick={goToPrevStep}
        >
          Back
        </Button>
        <Button
          type="button"
          onClick={goToNextStep}
          className={`${
            stepsCompleted.deliveryAddress
              ? 'bg-emerald-600 hover:bg-emerald-700'
              : 'bg-blue-600 hover:bg-blue-700'
          }`}
          disabled={!stepsCompleted.deliveryAddress}
        >
          {stepsCompleted.deliveryAddress ? (
            "Continue to Timing"
          ) : (
            <div className="flex items-center">
              <AlertTriangle className="h-4 w-4 mr-2" />
              Complete required fields
            </div>
          )}
        </Button>
      </div>
    </>
  );
};
