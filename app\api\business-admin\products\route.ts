import { NextResponse } from "next/server"
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs"
import { createClient } from "@supabase/supabase-js"
import { cookies } from "next/headers"

// Create a Supabase client with the service role key for admin operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET(request: Request) {
  try {
    console.log("Products API called")

    // First, verify the user is authenticated and has business manager permissions
    const cookieStore = cookies()
    const authClient = createServerComponentClient({ cookies: () => cookieStore })
    const adminClient = createClient(supabaseUrl, supabaseServiceKey)

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession()

    if (sessionError || !session) {
      console.error("No authenticated session found:", sessionError)
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    console.log("User authenticated:", session.user.email)

    // Get the user's profile to check their role
    const { data: userProfile, error: userError } = await authClient
      .from("users")
      .select("id, role, business_id")
      .eq("id", session.user.id)
      .single()

    if (userError) {
      console.error("Error fetching user profile:", userError)

      // Return empty products array for development
      return NextResponse.json({ products: [] })
    }

    if (!userProfile) {
      console.error("User profile not found")
      return NextResponse.json(
        { error: "User profile not found" },
        { status: 404 }
      )
    }

    console.log("User profile:", userProfile)

    // Check if the user is a business manager, admin, or super admin
    const isBusinessManager = userProfile.role === "business_manager"
    const isAdmin = userProfile.role === "admin"
    const isSuperAdmin = userProfile.role === "super_admin"

    if (!isBusinessManager && !isAdmin && !isSuperAdmin) {
      console.error("User does not have business manager permissions")
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    // Get the business ID from the user profile
    const businessId = userProfile.business_id

    if (!businessId && !isAdmin && !isSuperAdmin) {
      console.error("Business manager without a business ID")
      return NextResponse.json(
        { error: "No business associated with this account" },
        { status: 400 }
      )
    }

    // Check if products table exists
    const { data: productsTableExists } = await adminClient
      .from("pg_tables")
      .select("tablename")
      .eq("tablename", "products")
      .eq("schemaname", "public")
      .single()

    if (!productsTableExists) {
      console.log("Products table doesn't exist, returning empty products array")
      return NextResponse.json({ products: [] })
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const categoryId = searchParams.get("categoryId")
    const featured = searchParams.get("featured")
    const available = searchParams.get("available")

    // For business managers, fetch products for their business
    // For admins and super admins, fetch all products if no business ID is specified
    let query = adminClient
      .from("products")
      .select(`
        id,
        name,
        description,
        price,
        image_url,
        category_id,
        is_available,
        is_featured,
        is_popular,
        slug,
        sku,
        unit,
        stock_quantity,
        business_ref,
        created_at,
        updated_at,
        variants:product_variants(
          id,
          product_id,
          name,
          price,
          is_default,
          is_available,
          is_popular,
          sku,
          unit,
          stock_quantity,
          business_ref,
          created_at,
          updated_at
        )
      `)
      .order("name")

    if (isBusinessManager) {
      // Business managers can only see products for their business
      query = query.eq("business_id", businessId)
    } else if ((isAdmin || isSuperAdmin) && businessId) {
      // Admins and super admins can see products for a specific business if specified
      query = query.eq("business_id", businessId)
    }

    // Apply filters
    if (categoryId) {
      query = query.eq("category_id", categoryId)
    }

    if (featured === "true") {
      query = query.eq("is_featured", true)
    }

    if (available === "true") {
      query = query.eq("is_available", true)
    } else if (available === "false") {
      query = query.eq("is_available", false)
    }

    const { data: products, error: productsError } = await query

    if (productsError) {
      console.error("Error fetching products:", productsError)
      return NextResponse.json({ products: [] })
    }

    return NextResponse.json({ products: products || [] })
  } catch (error: any) {
    console.error("Error in GET /api/business-admin/products:", error)
    return NextResponse.json({ products: [] })
  }
}

export async function POST(request: Request) {
  try {
    // First, verify the user is authenticated and has business manager permissions
    const cookieStore = cookies()
    const authClient = createServerComponentClient({ cookies: () => cookieStore })
    const adminClient = createClient(supabaseUrl, supabaseServiceKey)

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession()

    if (sessionError || !session) {
      console.error("No authenticated session found:", sessionError)
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Get the user's profile to check their role
    const { data: userProfile, error: userError } = await authClient
      .from("users")
      .select("id, role, business_id")
      .eq("id", session.user.id)
      .single()

    if (userError || !userProfile) {
      console.error("Error fetching user profile:", userError)
      return NextResponse.json(
        { error: "User profile not found" },
        { status: 404 }
      )
    }

    // Check if the user is a business manager, admin, or super admin
    const isBusinessManager = userProfile.role === "business_manager"
    const isAdmin = userProfile.role === "admin"
    const isSuperAdmin = userProfile.role === "super_admin"

    if (!isBusinessManager && !isAdmin && !isSuperAdmin) {
      console.error("User does not have business manager permissions")
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    // Get the business ID from the user profile
    const businessId = userProfile.business_id

    if (!businessId && !isAdmin && !isSuperAdmin) {
      console.error("Business manager without a business ID")
      return NextResponse.json(
        { error: "No business associated with this account" },
        { status: 400 }
      )
    }

    // Parse the request body
    const requestData = await request.json()
    const {
      name,
      description,
      price,
      image_url,
      category_id,
      is_available,
      is_featured,
      is_popular,
      slug,
      sku,
      unit,
      stock_quantity,
      business_ref,
      variants
    } = requestData

    // Validate required fields
    if (!name) {
      return NextResponse.json(
        { error: "Product name is required" },
        { status: 400 }
      )
    }

    if (price === undefined || price === null) {
      return NextResponse.json(
        { error: "Product price is required" },
        { status: 400 }
      )
    }

    // Check if products table exists
    const { data: productsTableExists } = await adminClient
      .from("pg_tables")
      .select("tablename")
      .eq("tablename", "products")
      .eq("schemaname", "public")
      .single()

    if (!productsTableExists) {
      console.log("Products table doesn't exist, returning mock product")
      return NextResponse.json({
        product: {
          id: 1,
          name,
          description,
          price,
          image_url,
          category_id,
          is_available: is_available !== undefined ? is_available : true,
          is_featured: is_featured !== undefined ? is_featured : false,
          business_id: businessId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          variants: variants || []
        }
      })
    }

    // Create the product
    const { data: product, error: productError } = await adminClient
      .from("products")
      .insert({
        name,
        description,
        price,
        image_url,
        category_id,
        is_available: is_available !== undefined ? is_available : true,
        is_featured: is_featured !== undefined ? is_featured : false,
        is_popular: is_popular !== undefined ? is_popular : false,
        slug: slug || name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, ''),
        sku,
        unit,
        stock_quantity,
        business_ref,
        business_id: businessId
      })
      .select()
      .single()

    if (productError) {
      console.error("Error creating product:", productError)

      // Return a mock product for development
      return NextResponse.json({
        product: {
          id: Math.floor(Math.random() * 1000) + 1,
          name,
          description,
          price,
          image_url,
          category_id,
          is_available: is_available !== undefined ? is_available : true,
          is_featured: is_featured !== undefined ? is_featured : false,
          is_popular: is_popular !== undefined ? is_popular : false,
          slug: slug || name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, ''),
          sku,
          unit,
          stock_quantity,
          business_ref,
          business_id: businessId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          variants: variants || []
        }
      })
    }

    // If variants are provided, create them
    if (variants && variants.length > 0) {
      const variantsToInsert = variants.map((variant: any) => ({
        product_id: product.id,
        name: variant.name,
        price: variant.price || 0,
        is_default: variant.is_default || false,
        is_available: variant.is_available !== undefined ? variant.is_available : true,
        is_popular: variant.is_popular || false,
        sku: variant.sku || null,
        unit: variant.unit || null,
        stock_quantity: variant.stock_quantity || null,
        business_ref: variant.business_ref || null
      }))

      const { error: variantsError } = await adminClient
        .from("product_variants")
        .insert(variantsToInsert)

      if (variantsError) {
        console.error("Error creating product variants:", variantsError)
        // Don't fail the whole request, just log the error
      }
    }

    // Fetch the product with variants
    const { data: productWithVariants, error: fetchError } = await adminClient
      .from("products")
      .select(`
        id,
        name,
        description,
        price,
        image_url,
        category_id,
        is_available,
        is_featured,
        is_popular,
        slug,
        sku,
        unit,
        stock_quantity,
        business_ref,
        created_at,
        updated_at,
        variants:product_variants(
          id,
          product_id,
          name,
          price,
          is_default,
          is_available,
          is_popular,
          sku,
          unit,
          stock_quantity,
          business_ref,
          created_at,
          updated_at
        )
      `)
      .eq("id", product.id)
      .single()

    if (fetchError) {
      console.error("Error fetching product with variants:", fetchError)
      // Return the product without variants
      return NextResponse.json({ product })
    }

    return NextResponse.json({ product: productWithVariants })
  } catch (error: any) {
    console.error("Error in POST /api/business-admin/products:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}

export async function PATCH(request: Request) {
  try {
    // First, verify the user is authenticated and has business manager permissions
    const cookieStore = cookies()
    const authClient = createServerComponentClient({ cookies: () => cookieStore })
    const adminClient = createClient(supabaseUrl, supabaseServiceKey)

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession()

    if (sessionError || !session) {
      console.error("No authenticated session found:", sessionError)
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Get the user's profile to check their role
    const { data: userProfile, error: userError } = await authClient
      .from("users")
      .select("id, role, business_id")
      .eq("id", session.user.id)
      .single()

    if (userError || !userProfile) {
      console.error("Error fetching user profile:", userError)
      return NextResponse.json(
        { error: "User profile not found" },
        { status: 404 }
      )
    }

    // Check if the user is a business manager, admin, or super admin
    const isBusinessManager = userProfile.role === "business_manager"
    const isAdmin = userProfile.role === "admin"
    const isSuperAdmin = userProfile.role === "super_admin"

    if (!isBusinessManager && !isAdmin && !isSuperAdmin) {
      console.error("User does not have business manager permissions")
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    // Get the business ID from the user profile
    const businessId = userProfile.business_id

    // Parse the request body
    const requestData = await request.json()
    const {
      id,
      is_available,
      is_featured,
      is_popular,
      sku,
      unit,
      stock_quantity,
      business_ref
    } = requestData

    if (!id) {
      return NextResponse.json(
        { error: "Product ID is required" },
        { status: 400 }
      )
    }

    // Check if the product exists and belongs to the user's business (for business managers)
    if (isBusinessManager) {
      const { data: existingProduct, error: existingProductError } = await adminClient
        .from("products")
        .select("id, business_id")
        .eq("id", id)
        .single()

      if (existingProductError || !existingProduct) {
        console.error("Product not found:", existingProductError)
        return NextResponse.json(
          { error: "Product not found" },
          { status: 404 }
        )
      }

      if (existingProduct.business_id !== businessId) {
        console.error("Product does not belong to the user's business")
        return NextResponse.json(
          { error: "Product not found" },
          { status: 404 }
        )
      }
    }

    // Update the product
    const updateData: any = {
      updated_at: new Date().toISOString()
    }

    if (is_available !== undefined) {
      updateData.is_available = is_available
    }

    if (is_featured !== undefined) {
      updateData.is_featured = is_featured
    }

    if (is_popular !== undefined) {
      updateData.is_popular = is_popular
    }

    if (sku !== undefined) {
      updateData.sku = sku
    }

    if (unit !== undefined) {
      updateData.unit = unit
    }

    if (stock_quantity !== undefined) {
      updateData.stock_quantity = stock_quantity
    }

    if (business_ref !== undefined) {
      updateData.business_ref = business_ref
    }

    const { data: updatedProduct, error: updateError } = await adminClient
      .from("products")
      .update(updateData)
      .eq("id", id)
      .select()
      .single()

    if (updateError) {
      console.error("Error updating product:", updateError)
      return NextResponse.json(
        { error: "Failed to update product" },
        { status: 500 }
      )
    }

    return NextResponse.json({ product: updatedProduct })
  } catch (error: any) {
    console.error("Error in PATCH /api/business-admin/products:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
