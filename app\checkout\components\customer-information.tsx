"use client";

import React from "react";
import { Check<PERSON>ircle, AlertTriangle } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { useCheckout } from "../checkout-context";
import GuestCheckoutBlock from "@/components/auth/guest-checkout-block";
import { validatePhoneNumber } from "../utils";

export const CustomerInformation: React.FC = () => {
  const {
    firstName,
    setFirstName,
    lastName,
    setLastName,
    phone,
    setPhone,
    stepsCompleted,
    goToNextStep,
    user
  } = useCheckout();

  return (
    <>
      {/* Only show guest checkout block if user is not logged in */}
      {!user && (
        <div className="mb-6">
          <GuestCheckoutBlock />
        </div>
      )}

      {/* Welcome message for logged in users */}
      {user && (
        <div className="mb-6 p-4 bg-emerald-50 rounded-lg border border-emerald-100">
          <h3 className="font-medium text-emerald-800 mb-2 flex items-center">
            <CheckCircle className="h-4 w-4 mr-2" />
            Welcome back, {user.email}
          </h3>
          <p className="text-sm text-emerald-700">
            You're logged in and ready to complete your order.
          </p>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div>
          <Label htmlFor="firstName" className="text-gray-700">First Name <span className="text-red-500">*</span></Label>
          <Input
            id="firstName"
            value={firstName}
            onChange={(e) => setFirstName(e.target.value)}
            required
            placeholder="First Name"
            className="mt-1"
          />
        </div>
        <div>
          <Label htmlFor="lastName" className="text-gray-700">Last Name <span className="text-red-500">*</span></Label>
          <Input
            id="lastName"
            value={lastName}
            onChange={(e) => setLastName(e.target.value)}
            required
            placeholder="Last Name"
            className="mt-1"
          />
        </div>
      </div>

      <div className="mb-4">
        <Label htmlFor="phone" className="text-gray-700">Phone Number <span className="text-red-500">*</span></Label>
        <Input
          id="phone"
          value={phone}
          onChange={(e) => setPhone(e.target.value)}
          required
          placeholder="07797123456"
          className="mt-1"
        />
        {phone && !validatePhoneNumber(phone) && (
          <p className="text-red-500 text-sm mt-1 flex items-center">
            <AlertTriangle className="h-3 w-3 mr-1" />
            Please enter a valid Jersey phone number
          </p>
        )}
      </div>

      {user && (
        <div className="mb-4">
          <Label htmlFor="email" className="text-gray-700">Email</Label>
          <Input
            id="email"
            value={user.email || ''}
            disabled
            className="bg-gray-50 mt-1"
          />
        </div>
      )}

      <div className="flex justify-end mt-6">
        <Button
          type="button"
          onClick={goToNextStep}
          className={`${
            stepsCompleted.customerInfo
              ? 'bg-emerald-600 hover:bg-emerald-700'
              : 'bg-blue-600 hover:bg-blue-700'
          }`}
          disabled={!stepsCompleted.customerInfo}
        >
          {stepsCompleted.customerInfo ? (
            "Continue to Delivery"
          ) : (
            <div className="flex items-center">
              <AlertTriangle className="h-4 w-4 mr-2" />
              Complete required fields
            </div>
          )}
        </Button>
      </div>
    </>
  );
};
