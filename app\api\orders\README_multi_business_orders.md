# Multi-Business Order Status Management

This document explains the implementation of the multi-business order status management system.

## Overview

In a multi-business order, different businesses might be at different stages in the fulfillment process. This system allows each business to have its own independent status while maintaining a meaningful aggregate status for the overall order.

## Database Implementation

The system uses PostgreSQL triggers to manage the status synchronization:

1. **Initial Status Sync**: When an order is first created, all `order_businesses` records inherit the initial status from the main order.

2. **Independent Status Updates**: After creation, each business's status can be updated independently.

3. **Aggregate Status Calculation**: When a business's status changes, a trigger automatically calculates the appropriate aggregate status for the main order based on the statuses of all businesses involved.

## Status Calculation Logic

The aggregate order status is calculated based on the following rules:

1. If all businesses are "cancelled", the order is "cancelled".
2. If all businesses are "delivered", the order is "delivered".
3. If any business is "pending", the order is "pending".
4. If any business is "confirmed" (and none are "pending"), the order is "confirmed".
5. If any business is "preparing" (and none are earlier in the flow), the order is "preparing".
6. If any business is "ready" (and none are earlier in the flow), the order is "ready".
7. If any business is "out_for_delivery" (and none are earlier in the flow), the order is "out_for_delivery".
8. If some businesses are "delivered" and others are in different states, the order is "partially_delivered".

## API Endpoints

### 1. Create Order
- **Endpoint**: `POST /api/orders`
- **Description**: Creates a new order with all related records in a single transaction.
- **Implementation**: Uses `createOrderWithTransaction` function.

### 2. Update Order Status (Overall)
- **Endpoint**: `PATCH /api/orders/[id]`
- **Description**: Updates the status of the entire order and all businesses within it.
- **Implementation**: Uses `updateOrderStatusWithTransaction` function.
- **Note**: This is deprecated for multi-business orders.

### 3. Update Business-Specific Status
- **Endpoint**: `PATCH /api/orders/[id]/business/[businessId]/status`
- **Description**: Updates the status of a specific business within an order.
- **Implementation**: Uses `updateBusinessOrderStatus` function.
- **Note**: This is the preferred method for multi-business orders.

## Transaction Helper Functions

The transaction helper module (`app/api/orders/transaction-helper.ts`) provides three main functions:

1. `createOrderWithTransaction`: Creates an order with all related records in a single transaction.

2. `updateOrderStatusWithTransaction`: Updates an order's status (deprecated for multi-business orders).

3. `updateBusinessOrderStatus`: Updates a specific business's status within an order, which is the preferred method for multi-business orders.

## Usage Examples

### Updating a Specific Business's Status

```javascript
// Example: Update a business status to "preparing"
const response = await fetch(`/api/orders/123/business/456/status`, {
  method: 'PATCH',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    status: 'preparing',
    notes: 'Kitchen has started preparing the order'
  })
});

const result = await response.json();
```

## Benefits

1. **Independent Business Tracking**: Each business can progress through the fulfillment process at its own pace.

2. **Accurate Order Status**: The main order status accurately reflects the overall state of the order.

3. **Data Consistency**: Database triggers ensure that the status is always calculated consistently.

4. **Transaction Support**: All database operations are wrapped in transactions for data integrity.

## Future Improvements

1. Add a UI component to display the status of each business in a multi-business order.

2. Implement notifications when a business's status changes.

3. Add more detailed status tracking for each business, such as preparation start time, estimated completion time, etc.
