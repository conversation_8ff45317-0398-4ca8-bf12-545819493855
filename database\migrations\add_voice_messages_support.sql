-- Add voice message support to communications table
-- This migration adds audio_data column and updates message_type constraints

-- Add audio_data column for storing base64 encoded audio
ALTER TABLE public.communications 
ADD COLUMN IF NOT EXISTS audio_data TEXT;

-- Update message_type constraint to include 'voice'
ALTER TABLE public.communications 
DROP CONSTRAINT IF EXISTS communications_message_type_check;

ALTER TABLE public.communications 
ADD CONSTRAINT communications_message_type_check 
CHECK (message_type IN (
    'chat',           -- Regular conversation
    'voice',          -- Voice messages
    'notification',   -- System or user notifications
    'alert',          -- Urgent notifications
    'request',        -- Formal requests
    'response',       -- Responses to requests
    'status_update',  -- Order/delivery status updates
    'system'          -- Automated system messages
));

-- Add index for voice messages for better performance
CREATE INDEX IF NOT EXISTS idx_communications_voice_messages 
ON public.communications(message_type) 
WHERE message_type = 'voice';

-- Add comment for documentation
COMMENT ON COLUMN public.communications.audio_data IS 'Base64 encoded audio data for voice messages';

-- Migration complete
SELECT 'Voice message support added successfully' as status;
