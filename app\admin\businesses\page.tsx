"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useAuthDirect } from "@/context/auth-context-direct"

interface Business {
  id: number
  name: string
  description: string
  address: string
  postcode: string
  phone: string
  business_type_id: number
  business_type: string
  created_at: string
  is_approved: boolean
}

export default function AdminBusinessesPage() {
  const router = useRouter()
  const { user, userProfile, isAdmin, isLoading } = useAuthDirect()
  const [businesses, setBusinesses] = useState<Business[]>([])
  const [filteredBusinesses, setFilteredBusinesses] = useState<Business[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [statusFilter, setStatusFilter] = useState<string>("pending")
  const [searchQuery, setSearchQuery] = useState<string>("")
  const [authChecked, setAuthChecked] = useState(false)

  // First, check authentication before fetching any data
  useEffect(() => {
    // Skip if still loading auth state
    if (isLoading) return

    // If we have auth info and user is not admin, redirect immediately
    if (!isLoading && (!user || !isAdmin)) {
      console.log("AdminBusinessesPage: User is not authorized, redirecting to login")
      router.push("/login?redirectTo=/admin/businesses")
      return
    }

    // If user is admin, mark auth as checked and allow data fetching
    if (!isLoading && user && isAdmin) {
      console.log("AdminBusinessesPage: User is authorized, proceeding to fetch data")
      setAuthChecked(true)
    }
  }, [user, isAdmin, isLoading, router])

  // Only fetch data after authentication is confirmed
  useEffect(() => {
    if (authChecked) {
      fetchBusinesses()
    }
  }, [authChecked])

  useEffect(() => {
    // Check for URL parameters
    const urlParams = new URLSearchParams(window.location.search)
    const filterParam = urlParams.get('filter')

    if (filterParam) {
      setStatusFilter(filterParam)
    }
  }, [])

  useEffect(() => {
    filterBusinesses()
  }, [businesses, statusFilter, searchQuery])

  const fetchBusinesses = async () => {
    setLoading(true)
    setError(null)

    try {
      console.log("AdminBusinessesPage: Fetching businesses")

      // Get the authentication token from localStorage
      const token = localStorage.getItem('loop_jersey_auth_token') || '';

      console.log("AdminBusinessesPage: Fetching businesses with token:", token ? "Token available" : "No token");

      // Add a timestamp to prevent caching and include the authorization header
      const response = await fetch(`/api/admin/businesses-direct?t=${Date.now()}`, {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      })

      console.log("API response status:", response.status, response.statusText)

      if (!response.ok) {
        console.error("API error response:", await response.json())

        // If we get a 401 or 403, redirect to login
        if (response.status === 401 || response.status === 403) {
          console.log("Authentication error, redirecting to login")
          router.push("/login?redirectTo=/admin/businesses")
          return
        }

        throw new Error(`Failed to fetch businesses: ${response.status} ${response.statusText}`)
      }

      const result = await response.json()
      console.log("Businesses fetched successfully:", result.businesses?.length || 0)

      setBusinesses(result.businesses || [])
    } catch (err: any) {
      console.error("Error fetching businesses:", err)
      setError(err.message || "Failed to load businesses")
    } finally {
      setLoading(false)
    }
  }

  const filterBusinesses = () => {
    let filtered = [...businesses]

    // Filter by status
    if (statusFilter === "pending") {
      filtered = filtered.filter(business => business.is_approved === null)
    } else if (statusFilter === "approved") {
      filtered = filtered.filter(business => business.is_approved === true)
    } else if (statusFilter === "rejected") {
      filtered = filtered.filter(business => business.is_approved === false)
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(business =>
        business.name.toLowerCase().includes(query) ||
        business.business_type.toLowerCase().includes(query) ||
        business.address.toLowerCase().includes(query) ||
        business.postcode.toLowerCase().includes(query)
      )
    }

    setFilteredBusinesses(filtered)
  }

  const viewBusinessDetails = (id: number) => {
    router.push(`/admin/businesses/${id}`)
  }



  return (
    <div className="container mx-auto p-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h1 className="text-3xl font-bold">Manage Businesses</h1>

        <div className="flex flex-col sm:flex-row gap-4 mt-4 md:mt-0 w-full md:w-auto">
          <div className="relative w-full sm:w-64">
            <input
              type="text"
              placeholder="Search businesses..."
              className="w-full px-4 py-2 border rounded-md"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <select
            className="px-4 py-2 border rounded-md bg-white"
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="all">All Businesses</option>
            <option value="pending">Pending Approval</option>
            <option value="approved">Approved</option>
            <option value="rejected">Rejected</option>
          </select>
        </div>
      </div>

      {isLoading || !authChecked || loading ? (
        <div className="text-center p-8">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-emerald-500 mx-auto"></div>
          <p className="mt-4 text-gray-500">Loading businesses...</p>
        </div>
      ) : error ? (
        <div className="text-center p-8 bg-red-50 text-red-500 rounded-lg">
          Error: {error}
        </div>
      ) : filteredBusinesses.length === 0 ? (
        <div className="text-center p-8 bg-gray-100 rounded-lg">
          {searchQuery ? (
            <p>No businesses found matching "{searchQuery}"</p>
          ) : statusFilter === "pending" ? (
            <p>No businesses pending approval</p>
          ) : statusFilter === "approved" ? (
            <p>No approved businesses</p>
          ) : statusFilter === "rejected" ? (
            <p>No rejected businesses</p>
          ) : (
            <p>No businesses found</p>
          )}
        </div>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredBusinesses.map((business) => (
            <Card key={business.id} className="overflow-hidden">
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <CardTitle className="text-xl">{business.name}</CardTitle>
                  <Badge variant={business.is_approved ? "success" : business.is_approved === false ? "destructive" : "outline"}>
                    {business.is_approved ? "Approved" : business.is_approved === false ? "Rejected" : "Pending"}
                  </Badge>
                </div>
                <CardDescription>{business.business_type}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-2 text-sm">
                <p><strong>Address:</strong> {business.address}, {business.postcode}</p>
                <p><strong>Phone:</strong> {business.phone}</p>
                <p><strong>Created:</strong> {new Date(business.created_at).toLocaleDateString()}</p>
                {business.description && (
                  <p className="line-clamp-2"><strong>Description:</strong> {business.description}</p>
                )}
              </CardContent>
              <CardFooter className="flex justify-center pt-3 border-t">
                <Button
                  variant="default"
                  onClick={() => viewBusinessDetails(business.id)}
                >
                  View Details
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
