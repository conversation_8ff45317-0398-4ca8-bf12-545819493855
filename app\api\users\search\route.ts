import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { getAuthUser } from '@/utils/auth-token'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const user = await getAuthUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')

    if (!query || query.trim().length < 2) {
      return NextResponse.json({
        users: [],
        message: 'Query must be at least 2 characters'
      })
    }

    // Search connection profiles
    const { data: profiles, error } = await supabase
      .from('connection_profiles')
      .select('*')
      .neq('user_id', user.id) // Exclude current user
      .eq('is_public', true) // Only public profiles
      .or(`display_name.ilike.%${query}%,bio.ilike.%${query}%`)
      .limit(20)

    if (error) {
      console.error('Error searching users:', error)
      return NextResponse.json(
        { error: 'Failed to search users' },
        { status: 500 }
      )
    }

    // Transform profiles to match UserProfile interface
    const users = (profiles || []).map(profile => ({
      id: profile.id,
      user_id: profile.user_id,
      display_name: profile.display_name,
      bio: profile.bio,
      avatar_url: profile.avatar_url,
      role_capabilities: {
        can_be_customer: true,
        can_be_rider: false,
        owns_business: false
      },
      specialties: {},
      average_rating: 4.5,
      total_ratings: 0,
      is_public: profile.is_public,
      allow_direct_messages: profile.allow_direct_messages
    }))

    return NextResponse.json({
      users,
      total: users.length
    })

  } catch (error) {
    console.error('Error in users search API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
