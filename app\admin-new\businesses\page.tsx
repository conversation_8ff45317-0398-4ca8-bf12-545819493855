"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import Link from "next/link"
import { format } from "date-fns"
import {
  ArrowDown,
  ArrowUp,
  Clock,
  DollarSign,
  Download,
  MapPin,
  Package,
  Package2,
  Search,
  ShoppingBag,
  Star,
  Store,
  TrendingUp,
  Truck,
} from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useAuthDirect } from "@/context/auth-context-direct"
import { useSupabaseRealtime } from "@/hooks/use-supabase-realtime"

interface Business {
  id: number
  name: string
  business_type_id: number
  is_approved: boolean
  created_at: string
  // Make all other fields optional
  business_type?: string
  logo_url?: string
  updated_at?: string
  location?: string
  address?: string
  postcode?: string
  phone?: string
  email?: string
  website?: string
  description?: string
  delivery_radius?: number
  minimum_order?: number
  delivery_fee?: number
  rating?: number
  total_ratings?: number
  total_orders?: number
  total_revenue?: number
}

export default function AdminBusinessesPage() {
  const router = useRouter()
  const { user, isAdmin, isLoading: authLoading } = useAuthDirect()
  const [authChecked, setAuthChecked] = useState(false)
  const [selectedBusiness, setSelectedBusiness] = useState<string | null>(null)
  const [dateRange, setDateRange] = useState("7d")

  // Use our real-time hook to fetch businesses
  const {
    data: businesses,
    loading: businessesLoading,
    error: businessesError,
    refetch: refetchBusinesses
  } = useSupabaseRealtime<Business>(
    'businesses',
    'id, name, business_type_id, is_approved, created_at',
    [
      { table: 'businesses', event: '*' }
    ],
    {
      orderBy: 'created_at',
      orderDirection: 'desc'
    }
  )

  // First, check authentication before showing any data
  useEffect(() => {
    // Skip if still loading auth state
    if (authLoading) return

    // If we have auth info and user is not admin, redirect immediately
    if (!authLoading && (!user || !isAdmin)) {
      console.log("AdminBusinessesPage: User is not authorized, redirecting to login")
      router.push("/login?redirectTo=/admin-new/businesses")
      return
    }

    // If user is admin, mark auth as checked
    if (!authLoading && user && isAdmin) {
      console.log("AdminBusinessesPage: User is authorized, proceeding to fetch data")
      setAuthChecked(true)
    }
  }, [user, isAdmin, authLoading, router])

  // Show loading state if auth is not checked or data is loading
  if (!authChecked || (authChecked && businessesLoading)) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading businesses data...</p>
        </div>
      </div>
    )
  }

  // Show error state if there was an error fetching data
  if (businessesError) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-5xl mb-4">⚠️</div>
          <h2 className="text-2xl font-bold mb-2">Error Loading Businesses</h2>
          <p className="text-gray-600 mb-4">{businessesError.message}</p>
          <Button onClick={() => refetchBusinesses()}>Retry</Button>
        </div>
      </div>
    )
  }

  // Format date helper
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'dd MMM yyyy')
  }

  // Get business type name
  const getBusinessTypeName = (typeId: number) => {
    const businessTypes: Record<number, string> = {
      1: "Restaurant",
      2: "Cafe",
      3: "Shop",
      4: "Pharmacy",
      5: "Grocery",
      6: "Bakery",
      7: "Errands"
    }
    return businessTypes[typeId] || "Business"
  }

  // Get business stats
  const businessStats = {
    total: businesses.length,
    approved: businesses.filter(b => b.is_approved).length,
    pending: businesses.filter(b => !b.is_approved).length,
    // Safely handle missing total_orders field
    withOrders: 0 // We're not fetching total_orders anymore
  }

  // Get selected business
  const business = selectedBusiness
    ? businesses.find(b => b.id.toString() === selectedBusiness)
    : businesses[0]

  return (
    <>
      {/* Header */}
      <header className="sticky top-0 z-10 flex h-16 items-center gap-4 border-b bg-background px-4 sm:px-6">
        <div className="flex flex-1 items-center gap-4">
          <h1 className="text-xl font-semibold">Business Management</h1>
        </div>
        <div className="flex items-center gap-4">
          <form className="relative hidden md:block">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input type="search" placeholder="Search businesses..." className="w-64 pl-8" />
          </form>
          <Button variant="outline" size="sm" className="hidden md:flex">
            <Download className="mr-2 h-4 w-4" />
            Export Data
          </Button>
        </div>
      </header>

      {/* Main Content */}
      <div className="p-4 sm:p-6 lg:p-8">
        {/* Business Stats */}
        <div className="mb-6 grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Total Businesses</CardTitle>
              <Store className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{businessStats.total}</div>
              <p className="text-xs text-muted-foreground">
                {businessStats.approved} approved, {businessStats.pending} pending
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Active Businesses</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{businessStats.withOrders}</div>
              <p className="text-xs text-muted-foreground">
                {businessStats.total > 0
                  ? `${Math.round((businessStats.withOrders / businessStats.total) * 100)}% of total businesses`
                  : "No businesses yet"}
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Pending Approvals</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{businessStats.pending}</div>
              <p className="text-xs text-muted-foreground">
                {businessStats.pending > 0 ? "Awaiting review" : "No pending approvals"}
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
              <Star className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {businesses.length > 0
                  ? (businesses.reduce((sum, b) => sum + (b.rating || 0), 0) / businesses.filter(b => b.rating).length).toFixed(1)
                  : 'N/A'}
              </div>
              {businesses.some(b => b.rating) ? (
                <div className="flex items-center mt-1">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star
                      key={star}
                      className={`h-3 w-3 ${
                        star <= (businesses.reduce((sum, b) => sum + (b.rating || 0), 0) / businesses.filter(b => b.rating).length)
                          ? 'text-yellow-400 fill-yellow-400'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
              ) : (
                <div className="mt-1 text-xs text-amber-600">
                  <Link href="/admin-new/database-tools" className="underline">
                    Calculate ratings
                  </Link>
                </div>
              )}
              <p className="text-xs text-muted-foreground mt-1">
                {businesses.some(b => b.rating)
                  ? `Based on ${businesses.filter(b => b.rating).length} businesses`
                  : "No ratings available"}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Business List */}
        <Card className="mb-6">
          <CardHeader>
            <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
              <div>
                <CardTitle>Businesses</CardTitle>
                <CardDescription>Manage businesses on your platform</CardDescription>
              </div>
              <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
                <Button>
                  <Store className="mr-2 h-4 w-4" />
                  Add Business
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="all" className="space-y-4">
              <TabsList>
                <TabsTrigger value="all">All Businesses</TabsTrigger>
                <TabsTrigger value="approved">Approved</TabsTrigger>
                <TabsTrigger value="pending">Pending</TabsTrigger>
              </TabsList>

              <TabsContent value="all">
                <div className="space-y-4">
                  {businesses.length > 0 ? (
                    businesses.map((business) => (
                      <div key={business.id} className="flex items-center justify-between rounded-lg border p-4">
                        <div className="flex items-center gap-4">
                          <Avatar className="h-10 w-10">
                            <AvatarFallback className="bg-emerald-100 text-emerald-700">
                              {business.name.charAt(0)}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <h3 className="font-medium">{business.name}</h3>
                            <p className="text-sm text-muted-foreground">
                              {getBusinessTypeName(business.business_type_id)} • {formatDate(business.created_at)}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-4">
                          <Badge
                            variant="outline"
                            className={business.is_approved
                              ? "bg-green-50 text-green-700"
                              : "bg-amber-50 text-amber-700"}
                          >
                            {business.is_approved ? "Approved" : "Pending"}
                          </Badge>
                          <Link href={`/admin/businesses/${business.id}`}>
                            <Button variant="outline" size="sm">
                              View Details
                            </Button>
                          </Link>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      No businesses found
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="approved">
                <div className="space-y-4">
                  {businesses.filter(b => b.is_approved).length > 0 ? (
                    businesses.filter(b => b.is_approved).map((business) => (
                      <div key={business.id} className="flex items-center justify-between rounded-lg border p-4">
                        <div className="flex items-center gap-4">
                          <Avatar className="h-10 w-10">
                            <AvatarFallback className="bg-emerald-100 text-emerald-700">
                              {business.name.charAt(0)}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <h3 className="font-medium">{business.name}</h3>
                            <p className="text-sm text-muted-foreground">
                              {getBusinessTypeName(business.business_type_id)} • {formatDate(business.created_at)}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-4">
                          <Badge
                            variant="outline"
                            className="bg-green-50 text-green-700"
                          >
                            Approved
                          </Badge>
                          <Link href={`/admin/businesses/${business.id}`}>
                            <Button variant="outline" size="sm">
                              View Details
                            </Button>
                          </Link>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      No approved businesses found
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="pending">
                <div className="space-y-4">
                  {businesses.filter(b => !b.is_approved).length > 0 ? (
                    businesses.filter(b => !b.is_approved).map((business) => (
                      <div key={business.id} className="flex items-center justify-between rounded-lg border p-4">
                        <div className="flex items-center gap-4">
                          <Avatar className="h-10 w-10">
                            <AvatarFallback className="bg-emerald-100 text-emerald-700">
                              {business.name.charAt(0)}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <h3 className="font-medium">{business.name}</h3>
                            <p className="text-sm text-muted-foreground">
                              {getBusinessTypeName(business.business_type_id)} • {formatDate(business.created_at)}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-4">
                          <Badge
                            variant="outline"
                            className="bg-amber-50 text-amber-700"
                          >
                            Pending
                          </Badge>
                          <Link href={`/admin/businesses/${business.id}`}>
                            <Button variant="outline" size="sm">
                              Review
                            </Button>
                          </Link>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      No pending businesses found
                    </div>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        <div className="text-center mt-8">
          <p className="text-sm text-muted-foreground">
            This is a preview of the new admin dashboard with real-time data updates.
            <br />
            Changes to businesses will appear instantly without refreshing the page.
          </p>
          <div className="mt-4">
            <Link href="/admin-new">
              <Button variant="outline">
                Back to Dashboard
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </>
  )
}
