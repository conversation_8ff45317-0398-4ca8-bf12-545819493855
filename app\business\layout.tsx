"use client"

import { ReactNode } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"

interface BusinessLayoutProps {
  children: ReactNode
}

export default function BusinessLayout({ children }: BusinessLayoutProps) {
  const pathname = usePathname()

  // Only show the header on certain pages
  const showHeader = !pathname?.includes('/registration-success')

  return (
    <div className="min-h-screen bg-white">
      {showHeader && (
        <header className="bg-white border-b">
          <div className="container-fluid py-4 flex items-center justify-between">
            <Link href="/search" className="flex items-center">
              <Button variant="ghost" size="sm" className="mr-2">
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to Search
              </Button>
            </Link>
          </div>
        </header>
      )}

      <main className="container-fluid py-6">
        {children}
      </main>
    </div>
  )
}
