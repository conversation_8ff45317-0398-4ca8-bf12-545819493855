{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "node scripts/kill-port.js && npx next dev -p 3000", "dev:stable": "node scripts/server-monitor.js", "build": "npx next build", "start": "npx next start -p 3000", "lint": "npx next lint", "migrate-restaurants": "node scripts/migrate-restaurants.js", "migrate-businesses": "node scripts/migrate-businesses.js", "seed-data": "node scripts/seed-data.js", "kill-port": "node scripts/kill-port.js", "clear-cache": "node scripts/clear-cache.js", "dev:fresh": "node scripts/clear-cache.js && npm run dev", "deploy:order-status": "node scripts/deploy-order-businesses-changes.js", "test:order-status": "cd tests && npm test", "drop-default-order-trigger": "node scripts/drop-ensure-order-businesses-trigger.js", "add-business-columns": "node scripts/add-business-columns-to-orders.js"}, "dependencies": {"@google/genai": "^0.12.0", "@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.49.4", "@types/leaflet": "^1.9.17", "@types/leaflet-routing-machine": "^3.2.8", "@types/mapbox-gl": "^3.4.1", "@types/pg": "^8.11.13", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "dotenv": "^16.5.0", "embla-carousel-react": "8.5.1", "immer": "latest", "input-otp": "1.4.1", "leaflet": "^1.9.4", "leaflet-routing-machine": "^3.2.12", "lucide-react": "^0.454.0", "mapbox-gl": "^3.11.0", "next": "15.2.4", "next-themes": "^0.4.6", "node-fetch": "2", "pg": "^8.15.1", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "use-sync-external-store": "latest", "uuid": "latest", "vaul": "^0.9.6", "zod": "^3.24.1", "zustand": "latest"}, "devDependencies": {"@types/node": "22.0.0", "@types/react": "^19.0.0", "@types/react-dom": "^19", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5.0.2"}}