import { cookies } from "next/headers";
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs";
import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

// Create a Supabase client with admin privileges
const adminClient = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || "",
  process.env.SUPABASE_SERVICE_ROLE_KEY || ""
);

// Helper function to create a server-side Supabase client
async function createServerSupabase() {
  const cookieStore = cookies();
  return createServerComponentClient({ cookies: () => cookieStore });
}

// GET handler to fetch all user addresses
export async function GET(request: Request) {
  try {
    console.log("GET /api/user/addresses/all: Starting request");

    // Check for Authorization header first
    const authHeader = request.headers.get('Authorization');
    let userId = null;
    let email = null;
    let authUserId = null;

    // Get the user's session using the server client
    const cookieStore = cookies();
    const supabase = createServerComponentClient({ cookies: () => cookieStore });

    console.log("GET /api/user/addresses/all: Getting session");
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError) {
      console.error("GET /api/user/addresses/all: Session error:", sessionError);
      // Continue with token-based auth instead of returning an error
    }

    // If we have a session, use it
    if (session?.user) {
      console.log("GET /api/user/addresses/all: Found session user:", session.user.email);
      email = session.user.email;
      authUserId = session.user.id;
    }
    // If no session but we have an auth header, try to use it
    else if (authHeader) {
      console.log("GET /api/user/addresses/all: Using Authorization header");
      const token = authHeader.replace('Bearer ', '');

      try {
        // Verify the token with Supabase
        const { data: userData, error: userError } = await adminClient.auth.getUser(token);

        if (userError) {
          console.error("GET /api/user/addresses/all: Invalid token:", userError);
          return NextResponse.json(
            { error: "Invalid authentication token" },
            { status: 401 }
          );
        }

        if (userData?.user) {
          email = userData.user.email;
          authUserId = userData.user.id;
          console.log("GET /api/user/addresses/all: Found user from token:", email);
        }
      } catch (tokenError) {
        console.error("GET /api/user/addresses/all: Error verifying token:", tokenError);
      }
    }

    // If we still don't have a user, return unauthorized
    if (!email && !authUserId) {
      console.log("GET /api/user/addresses/all: No authenticated user found");
      return NextResponse.json(
        { error: "Not authenticated" },
        { status: 401 }
      );
    }

    console.log("GET /api/user/addresses/all: Authenticated user:", email || authUserId);

    // Get user ID from the users table using admin client
    try {
      console.log("GET /api/user/addresses/all: Fetching user ID for email:", email);

      const { data: userData, error: userError } = await adminClient
        .from("users")
        .select("id")
        .eq("email", email)
        .single();

      let addresses = [];

      // If user doesn't exist yet, try to find addresses by auth user ID or email
      if (userError && userError.code === 'PGRST116') {
        console.log("GET /api/user/addresses/all: User not found in database, trying to find addresses by auth ID or email");

        // Try to find addresses by auth user ID
        const { data: addressesByAuthId, error: authIdError } = await adminClient
          .from("user_addresses")
          .select("*")
          .eq("auth_user_id", authUserId)
          .order("is_default", { ascending: false })
          .order("updated_at", { ascending: false });

        if (!authIdError && addressesByAuthId && addressesByAuthId.length > 0) {
          console.log("GET /api/user/addresses/all: Found addresses by auth ID:", addressesByAuthId.length);
          addresses = addressesByAuthId;
        } else {
          // Try to find addresses by email
          const { data: addressesByEmail, error: emailError } = await adminClient
            .from("user_addresses")
            .select("*")
            .eq("email", email)
            .order("is_default", { ascending: false })
            .order("updated_at", { ascending: false });

          if (!emailError && addressesByEmail && addressesByEmail.length > 0) {
            console.log("GET /api/user/addresses/all: Found addresses by email:", addressesByEmail.length);
            addresses = addressesByEmail;
          }
        }

        return NextResponse.json({ addresses });
      }

      if (userError) {
        console.error("GET /api/user/addresses/all: Error fetching user ID:", userError);
        return NextResponse.json(
          { error: "Error fetching user data: " + userError.message },
          { status: 500 }
        );
      }

      if (!userData) {
        console.log("GET /api/user/addresses/all: User not found in database");
        return NextResponse.json({ addresses: [] });
      }

      console.log("GET /api/user/addresses/all: Found user ID:", userData.id);

      // Fetch all user addresses using admin client
      console.log("GET /api/user/addresses/all: Fetching addresses for user ID:", userData.id);

      const { data: addressesByUserId, error: addressError } = await adminClient
        .from("user_addresses")
        .select("*")
        .eq("user_id", userData.id)
        .order("is_default", { ascending: false })
        .order("updated_at", { ascending: false });

      if (addressError) {
        console.error("GET /api/user/addresses/all: Error fetching addresses:", addressError);
        return NextResponse.json(
          { error: "Error fetching addresses: " + addressError.message },
          { status: 500 }
        );
      }

      // Also try to find addresses by auth user ID or email to catch any that might have been created differently
      const { data: addressesByAuthId } = await adminClient
        .from("user_addresses")
        .select("*")
        .eq("auth_user_id", authUserId)
        .order("is_default", { ascending: false })
        .order("updated_at", { ascending: false });

      const { data: addressesByEmail } = await adminClient
        .from("user_addresses")
        .select("*")
        .eq("email", email)
        .order("is_default", { ascending: false })
        .order("updated_at", { ascending: false });

      // Combine all addresses and remove duplicates
      const allAddresses = [
        ...(addressesByUserId || []),
        ...(addressesByAuthId || []),
        ...(addressesByEmail || [])
      ];

      // Remove duplicates by ID
      const uniqueAddresses = Array.from(
        new Map(allAddresses.map(addr => [addr.id, addr])).values()
      );

      console.log("GET /api/user/addresses/all: Found addresses:", uniqueAddresses.length);
      return NextResponse.json({ addresses: uniqueAddresses });
    } catch (error) {
      console.error("GET /api/user/addresses/all: Unexpected error:", error);
      return NextResponse.json(
        { error: "An unexpected error occurred" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("GET /api/user/addresses/all: Unexpected error:", error);
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
