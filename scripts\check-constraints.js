const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function checkConstraints() {
  console.log('🔍 Checking Database Constraints\n');

  try {
    // Check channel_type constraint
    console.log('1️⃣ Checking channel_type constraint...');
    const { data: channelConstraint, error: channelError } = await supabase
      .rpc('exec_sql', {
        sql: `
          SELECT 
            conname as constraint_name,
            pg_get_constraintdef(oid) as constraint_definition
          FROM pg_constraint 
          WHERE conname LIKE '%channel_type%' 
          AND conrelid = 'public.communications'::regclass;
        `
      });

    if (channelError) {
      console.log('❌ Error checking channel constraint:', channelError.message);
    } else {
      console.log('✅ Channel type constraint:');
      console.log(channelConstraint);
    }

    // Check message_type constraint  
    console.log('\n2️⃣ Checking message_type constraint...');
    const { data: messageConstraint, error: messageError } = await supabase
      .rpc('exec_sql', {
        sql: `
          SELECT 
            conname as constraint_name,
            pg_get_constraintdef(oid) as constraint_definition
          FROM pg_constraint 
          WHERE conname LIKE '%message_type%'
          AND conrelid = 'public.communications'::regclass;
        `
      });

    if (messageError) {
      console.log('❌ Error checking message constraint:', messageError.message);
    } else {
      console.log('✅ Message type constraint:');
      console.log(messageConstraint);
    }

    // Check table structure
    console.log('\n3️⃣ Checking communications table structure...');
    const { data: tableStructure, error: structureError } = await supabase
      .rpc('exec_sql', {
        sql: `
          SELECT 
            column_name,
            data_type,
            is_nullable,
            column_default
          FROM information_schema.columns
          WHERE table_schema = 'public'
          AND table_name = 'communications'
          ORDER BY ordinal_position;
        `
      });

    if (structureError) {
      console.log('❌ Error checking table structure:', structureError.message);
    } else {
      console.log('✅ Communications table structure:');
      tableStructure.forEach(col => {
        console.log(`   ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'}`);
      });
    }

    // Try a simple insert to see what happens
    console.log('\n4️⃣ Testing simple insert...');
    const { data: users } = await supabase.auth.admin.listUsers();
    
    if (users.users.length >= 2) {
      const testMessage = {
        sender_id: users.users[0].id,
        recipient_id: users.users[1].id,
        channel_type: 'general',
        message_type: 'chat',
        content: 'Test message',
        created_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('communications')
        .insert(testMessage)
        .select();

      if (error) {
        console.log('❌ Test insert failed:', error.message);
        console.log('   Error details:', error);
      } else {
        console.log('✅ Test insert successful!');
        console.log('   Message ID:', data[0]?.id);
        console.log('   Thread ID:', data[0]?.thread_id);
        
        // Clean up test message
        await supabase
          .from('communications')
          .delete()
          .eq('id', data[0].id);
        console.log('   Test message cleaned up');
      }
    }

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

checkConstraints();
