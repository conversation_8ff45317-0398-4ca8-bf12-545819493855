import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { verifyUserAccess } from '@/utils/auth-helpers'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

interface PushSubscriptionData {
  endpoint: string
  expirationTime?: number | null
  keys: {
    p256dh: string
    auth: string
  }
}

interface SubscribeRequest {
  subscription: PushSubscriptionData
  deviceType?: string
  browserName?: string
  userAgent?: string
  preferences?: {
    order_updates?: boolean
    delivery_updates?: boolean
    messages?: boolean
    marketing?: boolean
    quiet_hours?: {
      enabled?: boolean
      start?: string
      end?: string
    }
  }
}

// POST - Subscribe to push notifications
export async function POST(request: NextRequest) {
  try {
    // Verify user access
    const accessCheck = await verifyUserAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const user = accessCheck.user
    const body: SubscribeRequest = await request.json()

    if (!body.subscription || !body.subscription.endpoint) {
      return NextResponse.json(
        { error: 'Valid push subscription is required' },
        { status: 400 }
      )
    }

    // Default preferences
    const defaultPreferences = {
      order_updates: true,
      delivery_updates: true,
      messages: true,
      marketing: false,
      quiet_hours: {
        enabled: false,
        start: '22:00',
        end: '08:00'
      }
    }

    const preferences = { ...defaultPreferences, ...body.preferences }

    // Check if subscription already exists
    const { data: existingSubscription } = await supabase
      .from('push_subscriptions')
      .select('id')
      .eq('user_id', user.id)
      .eq('subscription_data->endpoint', body.subscription.endpoint)
      .single()

    if (existingSubscription) {
      // Update existing subscription
      const { data, error } = await supabase
        .from('push_subscriptions')
        .update({
          subscription_data: body.subscription,
          device_type: body.deviceType,
          browser_name: body.browserName,
          user_agent: body.userAgent,
          preferences,
          is_active: true,
          last_used_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', existingSubscription.id)
        .select()
        .single()

      if (error) {
        console.error('Error updating push subscription:', error)
        return NextResponse.json(
          { error: 'Failed to update push subscription' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        message: 'Push subscription updated successfully',
        subscription: data
      })
    } else {
      // Create new subscription
      const { data, error } = await supabase
        .from('push_subscriptions')
        .insert({
          user_id: user.id,
          subscription_data: body.subscription,
          device_type: body.deviceType,
          browser_name: body.browserName,
          user_agent: body.userAgent,
          preferences,
          is_active: true,
          last_used_at: new Date().toISOString()
        })
        .select()
        .single()

      if (error) {
        console.error('Error creating push subscription:', error)
        return NextResponse.json(
          { error: 'Failed to create push subscription' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        message: 'Push subscription created successfully',
        subscription: data
      })
    }

  } catch (error: any) {
    console.error('Error in push subscription:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET - Get user's push subscriptions
export async function GET(request: NextRequest) {
  try {
    // Verify user access
    const accessCheck = await verifyUserAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const user = accessCheck.user

    // Get user's active subscriptions
    const { data: subscriptions, error } = await supabase
      .from('push_subscriptions')
      .select('id, device_type, browser_name, preferences, is_active, created_at, last_used_at')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching push subscriptions:', error)
      return NextResponse.json(
        { error: 'Failed to fetch push subscriptions' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      subscriptions: subscriptions || []
    })

  } catch (error: any) {
    console.error('Error fetching push subscriptions:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE - Unsubscribe from push notifications
export async function DELETE(request: NextRequest) {
  try {
    // Verify user access
    const accessCheck = await verifyUserAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const user = accessCheck.user
    const { searchParams } = new URL(request.url)
    const subscriptionId = searchParams.get('id')
    const endpoint = searchParams.get('endpoint')

    if (!subscriptionId && !endpoint) {
      return NextResponse.json(
        { error: 'Subscription ID or endpoint is required' },
        { status: 400 }
      )
    }

    let query = supabase
      .from('push_subscriptions')
      .update({ is_active: false, updated_at: new Date().toISOString() })
      .eq('user_id', user.id)

    if (subscriptionId) {
      query = query.eq('id', subscriptionId)
    } else if (endpoint) {
      query = query.eq('subscription_data->endpoint', endpoint)
    }

    const { error } = await query

    if (error) {
      console.error('Error unsubscribing from push notifications:', error)
      return NextResponse.json(
        { error: 'Failed to unsubscribe from push notifications' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'Successfully unsubscribed from push notifications'
    })

  } catch (error: any) {
    console.error('Error unsubscribing from push notifications:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
