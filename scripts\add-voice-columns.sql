-- Add voice message file storage columns to communications table
-- Run this in your Supabase SQL editor or database client

-- Add the new columns if they don't exist
ALTER TABLE public.communications 
ADD COLUMN IF NOT EXISTS audio_file_name VA<PERSON>HAR(255),
ADD COLUMN IF NOT EXISTS audio_file_size INTEGER,
ADD COLUMN IF NOT EXISTS audio_duration DECIMAL(5,2);

-- Add comments for documentation
COMMENT ON COLUMN public.communications.audio_data IS 'URL to voice message file in storage (was base64 data)';
COMMENT ON COLUMN public.communications.audio_file_name IS 'Original filename of voice message in storage';
COMMENT ON COLUMN public.communications.audio_file_size IS 'File size in bytes';
COMMENT ON COLUMN public.communications.audio_duration IS 'Duration in seconds';

-- Create index for better performance on voice message queries
CREATE INDEX IF NOT EXISTS idx_communications_voice_messages 
ON public.communications (message_type, audio_file_name) 
WHERE message_type = 'voice' AND audio_file_name IS NOT NULL;

-- Create index for usage queries (by user and date)
CREATE INDEX IF NOT EXISTS idx_communications_voice_usage 
ON public.communications (sender_id, created_at, audio_file_size) 
WHERE message_type = 'voice' AND audio_file_size IS NOT NULL;

-- Create index for thread voice messages
CREATE INDEX IF NOT EXISTS idx_communications_thread_voice 
ON public.communications (thread_id, created_at, audio_file_size) 
WHERE message_type = 'voice' AND audio_file_size IS NOT NULL;
