import { NextResponse } from 'next/server';

// In-memory log storage
let logs: { timestamp: string; message: string; type: 'info' | 'error'; data?: any }[] = [];

export async function POST(request: Request) {
  try {
    // Get the request body as text first
    const text = await request.text();

    // Check if the body is empty
    if (!text || text.trim() === '') {
      console.error('Empty request body received in debug log API');
      return NextResponse.json({ error: 'Empty request body' }, { status: 400 });
    }

    // Try to parse as JSON
    let body;
    try {
      body = JSON.parse(text);
    } catch (parseError) {
      console.error('Invalid JSON in debug log API:', text.substring(0, 200));
      return NextResponse.json({ error: 'Invalid JSON in request body' }, { status: 400 });
    }

    const { message, type = 'info', data } = body;

    // Validate required fields
    if (!message) {
      console.error('Missing message in debug log API');
      return NextResponse.json({ error: 'Message is required' }, { status: 400 });
    }

    // Process data to ensure it's serializable
    let processedData = data;
    if (data !== undefined && data !== null) {
      if (typeof data === 'object') {
        try {
          // Test if it can be stringified
          JSON.stringify(data);
        } catch (e) {
          // If not, convert to string
          processedData = 'Unserializable object: ' + String(data);
        }
      }
    }

    // Add log to in-memory storage
    logs.push({
      timestamp: new Date().toISOString(),
      message,
      type,
      data: processedData
    });

    // Keep only the last 100 logs
    if (logs.length > 100) {
      logs = logs.slice(-100);
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error logging to terminal:', error);
    return NextResponse.json({
      error: 'Failed to log message',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({ logs });
}

// Clear logs
export async function DELETE() {
  logs = [];
  return NextResponse.json({ success: true });
}
