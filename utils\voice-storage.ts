// Voice message storage utilities using Supabase Storage
import { createClient } from '@supabase/supabase-js'
import { checkVoiceLimits, VoiceLimitCheck } from './voice-limits'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

// Initialize storage bucket on first use
let bucketInitialized = false

export interface VoiceUploadResult {
  success: boolean
  fileUrl?: string
  fileName?: string
  error?: string
  limitCheck?: VoiceLimitCheck
}

/**
 * Ensure voice messages bucket exists
 */
async function ensureBucketExists(): Promise<boolean> {
  if (bucketInitialized) return true

  try {
    // Check if bucket exists
    const { data: buckets } = await supabase.storage.listBuckets()
    const bucketExists = buckets?.some(bucket => bucket.name === 'voice-messages')

    if (!bucketExists) {
      // Create bucket
      const { error } = await supabase.storage.createBucket('voice-messages', {
        public: false,
        fileSizeLimit: 10 * 1024 * 1024, // 10MB
        allowedMimeTypes: ['audio/webm', 'audio/mp4', 'audio/wav', 'audio/ogg']
      })

      if (error && !error.message.includes('already exists')) {
        console.error('Failed to create voice messages bucket:', error)
        return false
      }
    }

    bucketInitialized = true
    return true
  } catch (error) {
    console.error('Error ensuring bucket exists:', error)
    return false
  }
}

/**
 * Upload voice message to Supabase Storage with limit checks
 */
export async function uploadVoiceMessage(
  audioBlob: Blob,
  userId: string,
  threadId: string,
  durationSeconds: number = 0
): Promise<VoiceUploadResult> {
  try {
    // Check limits before upload
    const limitCheck = await checkVoiceLimits(userId, threadId, audioBlob.size, durationSeconds)
    if (!limitCheck.allowed) {
      return {
        success: false,
        error: limitCheck.reason,
        limitCheck
      }
    }

    // Ensure bucket exists
    const bucketReady = await ensureBucketExists()
    if (!bucketReady) {
      return { success: false, error: 'Storage not available' }
    }

    // Generate unique filename with user folder structure
    const timestamp = Date.now()
    const randomId = Math.random().toString(36).substr(2, 9)
    const fileName = `${userId}/voice-${threadId}-${timestamp}-${randomId}.webm`

    // Upload to Supabase Storage
    const { data, error } = await supabase.storage
      .from('voice-messages')
      .upload(fileName, audioBlob, {
        contentType: 'audio/webm',
        cacheControl: '3600', // Cache for 1 hour
        upsert: false
      })

    if (error) {
      console.error('Voice upload error:', error)
      return { success: false, error: error.message }
    }

    // Get signed URL for secure access (24 hour expiry)
    const { data: signedUrlData, error: urlError } = await supabase.storage
      .from('voice-messages')
      .createSignedUrl(fileName, 24 * 60 * 60) // 24 hours

    if (urlError) {
      console.error('Voice URL error:', urlError)
      return { success: false, error: urlError.message }
    }

    return {
      success: true,
      fileUrl: signedUrlData.signedUrl,
      fileName: fileName,
      limitCheck
    }
  } catch (error) {
    console.error('Voice upload exception:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Upload failed'
    }
  }
}

/**
 * Delete voice message from Supabase Storage
 */
export async function deleteVoiceMessage(fileName: string): Promise<boolean> {
  try {
    const { error } = await supabase.storage
      .from('voice-messages')
      .remove([fileName])

    if (error) {
      console.error('Voice delete error:', error)
      return false
    }

    return true
  } catch (error) {
    console.error('Voice delete exception:', error)
    return false
  }
}

/**
 * Get signed URL for private voice message access
 */
export async function getVoiceMessageUrl(fileName: string): Promise<string | null> {
  try {
    const { data, error } = await supabase.storage
      .from('voice-messages')
      .createSignedUrl(fileName, 3600) // 1 hour expiry

    if (error) {
      console.error('Voice URL error:', error)
      return null
    }

    return data.signedUrl
  } catch (error) {
    console.error('Voice URL exception:', error)
    return null
  }
}

/**
 * Setup Supabase Storage bucket for voice messages
 */
export async function setupVoiceStorage() {
  try {
    // Create bucket if it doesn't exist
    const { data, error } = await supabase.storage.createBucket('voice-messages', {
      public: false, // Private bucket for security
      fileSizeLimit: 10 * 1024 * 1024, // 10MB limit
      allowedMimeTypes: ['audio/webm', 'audio/mp4', 'audio/wav']
    })

    if (error && error.message !== 'Bucket already exists') {
      console.error('Bucket creation error:', error)
      return false
    }

    console.log('Voice storage bucket ready')
    return true
  } catch (error) {
    console.error('Voice storage setup error:', error)
    return false
  }
}
