"use client"

import { useState, useEffect } from "react"
import {
  Plus,
  Trash2,
  DollarSign,
  Star,
  GripVertical,
  Upload,
  X
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/components/ui/use-toast"

interface Category {
  id: number
  name: string
}

interface ProductVariant {
  id?: number
  product_id?: number
  name: string
  price: number
  is_default: boolean
  created_at?: string
  updated_at?: string
}

interface Product {
  id?: number
  name: string
  description?: string
  price: number
  image_url?: string
  category_id?: number
  is_available: boolean
  is_featured: boolean
  variants?: ProductVariant[]
}

interface ProductFormProps {
  product?: Product
  categoryId?: number
  categories: Category[]
  onSubmit: (product: Product) => void
  onCancel: () => void
}

export default function ProductForm({
  product,
  categoryId,
  categories,
  onSubmit,
  onCancel
}: ProductFormProps) {
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState<Product>({
    name: "",
    description: "",
    price: 0,
    image_url: "",
    category_id: categoryId || undefined,
    is_available: true,
    is_featured: false,
    variants: []
  })
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [isUploading, setIsUploading] = useState(false)

  // Initialize form data with product data if editing
  useEffect(() => {
    if (product) {
      setFormData({
        id: product.id,
        name: product.name,
        description: product.description || "",
        price: product.price,
        image_url: product.image_url || "",
        category_id: product.category_id,
        is_available: product.is_available,
        is_featured: product.is_featured,
        variants: product.variants || []
      })

      if (product.image_url) {
        setImagePreview(product.image_url)
      }
    } else if (categoryId) {
      setFormData(prev => ({
        ...prev,
        category_id: categoryId
      }))
    }
  }, [product, categoryId])

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  // Handle number input changes
  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: parseFloat(value) || 0
    }))
  }

  // Handle switch changes
  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }))
  }

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value === "" ? undefined : parseInt(value)
    }))
  }

  // Handle image upload
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        variant: "destructive",
        title: "File too large",
        description: "Image must be less than 5MB"
      })
      return
    }

    // Check file type
    if (!file.type.startsWith("image/")) {
      toast({
        variant: "destructive",
        title: "Invalid file type",
        description: "Please upload an image file"
      })
      return
    }

    setImageFile(file)
    const previewUrl = URL.createObjectURL(file)
    setImagePreview(previewUrl)
  }

  // Upload image to storage
  const uploadImage = async (): Promise<string | null> => {
    if (!imageFile) return formData.image_url || null

    setIsUploading(true)
    try {
      // Create a FormData object to send the file
      const formData = new FormData()
      formData.append("file", imageFile)

      // Upload the file to your storage endpoint
      const response = await fetch("/api/upload", {
        method: "POST",
        body: formData
      })

      if (!response.ok) {
        throw new Error("Failed to upload image")
      }

      const data = await response.json()
      return data.url
    } catch (error) {
      console.error("Error uploading image:", error)
      toast({
        variant: "destructive",
        title: "Upload Failed",
        description: "Failed to upload image. Please try again."
      })
      return null
    } finally {
      setIsUploading(false)
    }
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Validate form data
      if (!formData.name.trim()) {
        throw new Error("Product name is required")
      }

      if (formData.price <= 0) {
        throw new Error("Price must be greater than 0")
      }

      // Upload image if selected
      let imageUrl = formData.image_url
      if (imageFile) {
        imageUrl = await uploadImage()
      }

      // Prepare product data
      const productData: Product = {
        ...formData,
        image_url: imageUrl || undefined
      }

      // Submit the form
      onSubmit(productData)
    } catch (error: any) {
      console.error("Error submitting product form:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to save product"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Add a new variant
  const addVariant = () => {
    setFormData(prev => ({
      ...prev,
      variants: [
        ...(prev.variants || []),
        {
          name: "",
          price: 0,
          is_default: false
        }
      ]
    }))
  }

  // Remove a variant
  const removeVariant = (index: number) => {
    setFormData(prev => ({
      ...prev,
      variants: prev.variants?.filter((_, i) => i !== index)
    }))
  }

  // Update a variant
  const updateVariant = (index: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      variants: prev.variants?.map((variant, i) => {
        if (i === index) {
          return {
            ...variant,
            [field]: value
          }
        }
        return variant
      })
    }))
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Product Name *</Label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="Enter product name"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder="Enter product description"
              rows={3}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="price">Price *</Label>
              <div className="relative">
                <DollarSign className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                <Input
                  id="price"
                  name="price"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.price}
                  onChange={handleNumberChange}
                  className="pl-8"
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="category_id">Category</Label>
              <Select
                value={formData.category_id?.toString() || ""}
                onValueChange={(value) => handleSelectChange("category_id", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">None</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Switch
                id="is_available"
                checked={formData.is_available}
                onCheckedChange={(checked) => handleSwitchChange("is_available", checked)}
              />
              <Label htmlFor="is_available">Available</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="is_featured"
                checked={formData.is_featured}
                onCheckedChange={(checked) => handleSwitchChange("is_featured", checked)}
              />
              <Label htmlFor="is_featured" className="flex items-center">
                <Star className="mr-1 h-4 w-4 text-yellow-500" />
                Featured
              </Label>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label>Product Image</Label>
            <div className="border-2 border-dashed rounded-md p-4 text-center">
              {imagePreview ? (
                <div className="relative">
                  <img
                    src={imagePreview}
                    alt="Product preview"
                    className="mx-auto max-h-40 object-contain"
                  />
                  <Button
                    type="button"
                    variant="destructive"
                    size="icon"
                    className="absolute top-0 right-0 h-6 w-6"
                    onClick={() => {
                      setImagePreview(null)
                      setImageFile(null)
                      setFormData(prev => ({
                        ...prev,
                        image_url: ""
                      }))
                    }}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ) : (
                <div className="py-4">
                  <Upload className="mx-auto h-12 w-12 text-gray-300" />
                  <p className="mt-2 text-sm text-gray-500">
                    Click to upload or drag and drop
                  </p>
                  <p className="text-xs text-gray-400">
                    PNG, JPG, GIF up to 5MB
                  </p>
                </div>
              )}
              <Input
                id="image"
                type="file"
                accept="image/*"
                onChange={handleImageChange}
                className={imagePreview ? "hidden" : "opacity-0 absolute inset-0 cursor-pointer"}
              />
            </div>
          </div>

          <Separator className="my-4" />

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label>Variants</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addVariant}
                className="flex items-center gap-1"
              >
                <Plus className="h-4 w-4" />
                Add Variant
              </Button>
            </div>

            {formData.variants && formData.variants.length > 0 ? (
              <div className="space-y-2">
                {formData.variants.map((variant, index) => (
                  <Card key={index} className="overflow-hidden">
                    <CardContent className="p-3">
                      <div className="flex items-start gap-2">
                        <div className="text-gray-400 mt-2">
                          <GripVertical className="h-5 w-5" />
                        </div>
                        <div className="flex-1 space-y-2">
                          <div className="grid grid-cols-2 gap-2">
                            <Input
                              placeholder="Variant name"
                              value={variant.name}
                              onChange={(e) => updateVariant(index, "name", e.target.value)}
                            />
                            <div className="relative">
                              <DollarSign className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                              <Input
                                type="number"
                                step="0.01"
                                placeholder="Variant price"
                                value={variant.price}
                                onChange={(e) => updateVariant(index, "price", parseFloat(e.target.value) || 0)}
                                className="pl-8"
                              />
                            </div>
                          </div>
                          <div className="flex items-center justify-end">
                            <div className="flex items-center space-x-2">
                              <Switch
                                id={`variant-default-${index}`}
                                checked={variant.is_default}
                                onCheckedChange={(checked) => updateVariant(index, "is_default", checked)}
                              />
                              <Label htmlFor={`variant-default-${index}`} className="text-sm">
                                Default
                              </Label>
                            </div>
                          </div>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          onClick={() => removeVariant(index)}
                          className="text-red-500"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500 italic">
                No variants added. Add variants for different sizes, options, etc.
              </p>
            )}
          </div>
        </div>
      </div>

      <div className="flex justify-end space-x-2">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting || isUploading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting || isUploading}
          className="bg-emerald-600 hover:bg-emerald-700"
        >
          {isSubmitting || isUploading ? (
            <>
              <div className="animate-spin mr-2 h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
              {isUploading ? "Uploading..." : "Saving..."}
            </>
          ) : (
            <>Save Product</>
          )}
        </Button>
      </div>
    </form>
  )
}
