"use client"

import type { ReactNode } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { BarChart3, Home, Settings, ShoppingBag, Users, ChevronDown, Bell, MenuIcon, LogOut } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface RestaurantAdminLayoutProps {
  children: ReactNode
}

export default function RestaurantAdminLayout({ children }: RestaurantAdminLayoutProps) {
  const pathname = usePathname()

  return (
    <div className="flex min-h-screen bg-gray-100">
      {/* Sidebar - Desktop */}
      <aside className="hidden md:flex flex-col w-64 bg-white border-r">
        <div className="p-4 border-b">
          <Link href="/restaurant-admin" className="flex items-center">
            <span className="text-xl font-bold text-emerald-600">Loop Jersey</span>
            <span className="ml-2 text-xs bg-emerald-100 text-emerald-800 px-2 py-0.5 rounded">Restaurant</span>
          </Link>
        </div>

        <nav className="flex-1 p-4 space-y-1">
          <Link
            href="/restaurant-admin"
            className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
              pathname === "/restaurant-admin" ? "text-emerald-600 bg-emerald-50 font-medium" : ""
            }`}
          >
            <Home className="w-5 h-5 mr-3 text-gray-500" />
            Dashboard
          </Link>
          <Link
            href="/restaurant-admin/orders"
            className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
              pathname === "/restaurant-admin/orders" ? "text-emerald-600 bg-emerald-50 font-medium" : ""
            }`}
          >
            <ShoppingBag className="w-5 h-5 mr-3 text-gray-500" />
            Orders
          </Link>
          <Link
            href="/restaurant-admin/menu"
            className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
              pathname === "/restaurant-admin/menu" ? "text-emerald-600 bg-emerald-50 font-medium" : ""
            }`}
          >
            <MenuIcon className="w-5 h-5 mr-3 text-gray-500" />
            Menu
          </Link>
          <Link
            href="/restaurant-admin/analytics"
            className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
              pathname === "/restaurant-admin/analytics" ? "text-emerald-600 bg-emerald-50 font-medium" : ""
            }`}
          >
            <BarChart3 className="w-5 h-5 mr-3 text-gray-500" />
            Analytics
          </Link>
          <Link
            href="/restaurant-admin/customers"
            className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
              pathname === "/restaurant-admin/customers" ? "text-emerald-600 bg-emerald-50 font-medium" : ""
            }`}
          >
            <Users className="w-5 h-5 mr-3 text-gray-500" />
            Customers
          </Link>
          <Link
            href="/restaurant-admin/settings"
            className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
              pathname === "/restaurant-admin/settings" ? "text-emerald-600 bg-emerald-50 font-medium" : ""
            }`}
          >
            <Settings className="w-5 h-5 mr-3 text-gray-500" />
            Settings
          </Link>
        </nav>

        <div className="p-4 border-t">
          <div className="flex items-center">
            <Avatar className="h-8 w-8">
              <AvatarImage src="/smiling-restaurant-owner.png" alt="Restaurant Owner" />
              <AvatarFallback>RO</AvatarFallback>
            </Avatar>
            <div className="ml-3">
              <p className="text-sm font-medium">Jersey Grill</p>
              <p className="text-xs text-gray-500">Restaurant Manager</p>
            </div>
            <Button variant="ghost" size="icon" className="ml-auto">
              <LogOut className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </aside>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Top Navigation */}
        <header className="bg-white border-b">
          <div className="flex items-center justify-between p-4">
            {/* Mobile Menu Button */}
            <Button variant="ghost" size="icon" className="md:hidden">
              <MenuIcon className="h-6 w-6" />
            </Button>

            <Link href="/restaurant-admin" className="md:hidden flex items-center">
              <span className="text-xl font-bold text-emerald-600">Loop Jersey</span>
              <span className="ml-2 text-xs bg-emerald-100 text-emerald-800 px-2 py-0.5 rounded">Restaurant</span>
            </Link>

            {/* Search - Hidden on Mobile */}
            <div className="hidden md:flex md:flex-1 mx-4">
              <div className="relative w-full max-w-md">
                <input
                  type="text"
                  placeholder="Search orders, customers..."
                  className="w-full pl-3 pr-10 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-emerald-500"
                />
                <button className="absolute right-0 top-0 h-full px-3 text-gray-400">
                  <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </button>
              </div>
            </div>

            {/* Right Actions */}
            <div className="flex items-center space-x-3">
              <Button variant="ghost" size="icon" className="relative">
                <Bell className="h-5 w-5" />
                <span className="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full"></span>
              </Button>

              {/* User Menu - Desktop */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild className="hidden md:flex">
                  <Button variant="ghost" className="flex items-center">
                    <Avatar className="h-8 w-8 mr-2">
                      <AvatarImage src="/smiling-restaurant-owner.png" alt="Restaurant Owner" />
                      <AvatarFallback>RO</AvatarFallback>
                    </Avatar>
                    <span>Jersey Grill</span>
                    <ChevronDown className="ml-2 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuLabel>My Account</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>Profile</DropdownMenuItem>
                  <DropdownMenuItem>Settings</DropdownMenuItem>
                  <DropdownMenuItem>Billing</DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>Log out</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="flex-1 overflow-y-auto p-4 md:p-6">{children}</main>
      </div>
    </div>
  )
}
