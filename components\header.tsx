"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Menu, X, ShoppingBasket, LogIn, UserPlus, ChevronDown, Utensils, Pill, Coffee, Car, Package, Search, MapPin, AlertTriangle, MessageSquare } from "lucide-react"
import WheelLogoIcon from "./wheel-logo-icon"
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem
} from "@/components/ui/dropdown-menu"
import { useRealtimeCart } from "@/context/realtime-cart-context"
import { useAuth } from "@/context/unified-auth-context"
import { useLocation } from "@/context/location-context"
import CartSheet from "./cart-sheet"
import UserMenu from "./auth/user-menu"
import { useRouter } from "next/navigation"
import { cn } from "@/lib/utils"; // Make sure cn is imported

// Client-side only cart badge component to avoid hydration mismatches
function CartBadge({ count }: { count: number }) {
  const [mounted, setMounted] = useState(false)

  // Only render the badge on the client side
  useEffect(() => {
    setMounted(true)
  }, [])

  // Always return a span with consistent structure
  return (
    <span
      className={`text-xs font-medium rounded-full min-w-[20px] h-5 flex items-center justify-center
        absolute -top-2 -right-2 px-1.5
        transition-opacity duration-200
        ${mounted ? 'bg-white text-emerald-600 border border-emerald-100 shadow-sm' : 'bg-transparent text-transparent'}
        ${mounted && count > 0 ? 'opacity-100' : 'opacity-0'}`}
      aria-hidden={!mounted}
    >
      {mounted ? (count || 0) : '0'}
    </span>
  )
}

export default function Header() {
  // Always call all hooks in the same order
  const pathname = usePathname()
  const router = useRouter()
  const { cart, totalItems, totalPrice } = useRealtimeCart()
  const { user, isLoading: authLoading } = useAuth()
  const { postcode, setPostcode: setContextPostcode, geocodePostcode } = useLocation()

  // State hooks - always call these in the same order
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [postcodeDialogOpen, setPostcodeDialogOpen] = useState(false)
  const [newPostcode, setNewPostcode] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [validationError, setValidationError] = useState<string | null>(null)

  // Derived state - compute these after all hooks are called
  // Use empty string as fallback for pathname to avoid null/undefined issues
  const isHomePage = (pathname || '') === '/'

  // Check if we're on a business detail page
  // Use empty string as fallback for pathname to avoid null/undefined issues
  const isBusinessDetailPage = !!(pathname || '').match(/^\/(restaurants|cafes|pharmacies|shops)\/[^\/]+$/)

  // Check if we're on a partners page
  const isPartnersPage = !!(pathname || '').startsWith('/partners')

  // Hide search bar on home page, business detail pages, and partners pages
  const hideSearchBar = isHomePage || isBusinessDetailPage || isPartnersPage

  // Hide location field on partners pages
  const hideLocationField = isPartnersPage

  // Hide sections dropdown everywhere (as requested by user)
  const hideSections = true

  // Ensure all hooks are called before any conditional returns
  // Add any additional hooks here

  // Function to validate Jersey postcode format
  const isValidJerseyPostcode = (postcode: string): boolean => {
    if (!postcode || typeof postcode !== 'string') return false;

    // Jersey postcodes follow the format JE1 1AA, JE2 3BT, etc.
    // JE followed by a digit (1-4), then a space, then another digit and two letters
    const jerseyPostcodeRegex = /^JE[1-4]\s\d[A-Z]{2}$/i;

    // Also accept format without space (JE11AA) and standardize it
    const noSpaceRegex = /^JE[1-4]\d[A-Z]{2}$/i;

    return jerseyPostcodeRegex.test(postcode.trim()) || noSpaceRegex.test(postcode.trim());
  };

  // Format a Jersey postcode to ensure it has a space
  const formatJerseyPostcode = (postcode: string): string => {
    const trimmed = postcode.trim().toUpperCase();

    // If it already has a space, return as is
    if (trimmed.includes(' ')) return trimmed;

    // Otherwise, insert a space after the 3rd character (e.g., JE11AA -> JE1 1AA)
    if (trimmed.length >= 5) {
      return `${trimmed.substring(0, 3)} ${trimmed.substring(3)}`;
    }

    return trimmed;
  };

  // Handle postcode submission
  const handlePostcodeSubmit = async () => {
    // Don't use early returns that might skip hooks
    // Instead, use conditional logic that always executes the full function
    const hasPostcode = !!newPostcode.trim();

    if (hasPostcode) {
      setIsSubmitting(true);
      try {
        // Store the trimmed postcode
        const trimmedPostcode = newPostcode.trim();

        // Validate the postcode format
        if (!isValidJerseyPostcode(trimmedPostcode)) {
          setValidationError(`"${trimmedPostcode}" is not a valid Jersey postcode. Jersey postcodes should be in the format JE1 1AA, JE2 3BT, etc. Default Jersey coordinates will be used to calculate delivery times and fee.`);
          setIsSubmitting(false);
        } else {

          // Format the postcode properly
          const formattedPostcode = formatJerseyPostcode(trimmedPostcode);

          console.log('Updating postcode to:', formattedPostcode);

          // Update the context with the new postcode
          setContextPostcode(formattedPostcode);

          // Try to geocode the postcode
          const newCoordinates = await geocodePostcode(formattedPostcode);

          console.log('Geocoded coordinates:', newCoordinates);

          // Close the dialog
          setPostcodeDialogOpen(false);

          // Reset the form
          setNewPostcode('');

          // Force a re-render to update the UI
          if (typeof window !== 'undefined') {
            // Store the postcode in localStorage to ensure it's available for delivery time calculations
            localStorage.setItem('loop_jersey_postcode', formattedPostcode);

            // If we have coordinates, store them too
            if (newCoordinates) {
              localStorage.setItem('loop_jersey_coordinates', JSON.stringify(newCoordinates));
            }

            // Use a timeout to ensure state updates have completed
            setTimeout(() => {
              // Update URL with the new postcode without page reload
              try {
                const url = new URL(window.location.href);
                url.searchParams.set('postcode', formattedPostcode);
                window.history.pushState({}, '', url.toString());

                // Create a custom event to notify components about the postcode change
                const postcodeChangeEvent = new CustomEvent('postcodeChanged', {
                  detail: { postcode: formattedPostcode, coordinates: newCoordinates }
                });
                window.dispatchEvent(postcodeChangeEvent);

                // Force a page refresh to update all delivery times
                // This is a simple way to ensure all components recalculate with the new postcode
                window.location.reload();
              } catch (err) {
                console.error('Error updating URL:', err);
              }
            }, 100);
          }
        }
      } catch (error) {
        console.error('Error updating postcode:', error);
      } finally {
        setIsSubmitting(false);
      }
    } else {
      // Handle the case where there's no postcode
      console.log('No postcode entered');
    }
  };

  // REMOVED useEffect for renderKey

  const partnerLinks = [
    { name: "Register Business", href: "/partners/register-business" },
    { name: "Riders", href: "/partners/riders" },
    { name: "Partners", href: "/partners/businesses" },
    { name: "Collaborators", href: "/partners/collaborators" },
  ]

  // Add keyframes and styles
  useEffect(() => {
    // Check if we're in a browser environment
    if (typeof document !== 'undefined') {
      // Check if the style already exists to avoid duplicates
      const existingStyle = document.getElementById('loop-jersey-animations');

      if (!existingStyle) {
        // Add keyframes to the document
        try {
          const style = document.createElement("style");
          style.id = 'loop-jersey-animations';
          style.innerHTML = `
  /* Removed fade and slide animations */

  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
    100% {
      transform: scale(1);
    }
  }

  @keyframes shimmer {
    0% {
      box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
  }

  @keyframes jiggle {
    0%, 100% {
      transform: rotate(0deg);
    }
    25% {
      transform: rotate(-5deg);
    }
    75% {
      transform: rotate(5deg);
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .animate-shimmer {
    animation: shimmer 1s forwards;
  }

  .animate-jiggle {
    animation: jiggle 0.5s ease-in-out;
  }

  .animate-pulse {
    animation: pulse 0.5s ease-in-out;
  }

  .animate-spin {
    animation: spin 1.5s cubic-bezier(0.2, 0.8, 0.3, 1);
  }

  .animate-spin-on-load {
    animation: spin 3s cubic-bezier(0.3, 0, 0.2, 1) forwards;
  }
`;
          document.head.appendChild(style);
        } catch (error) {
          console.error("Error adding animation styles:", error);
        }
      }

      // Return cleanup function
      return () => {
        // Only remove if it exists and is the one we added
        const styleToRemove = document.getElementById('loop-jersey-animations');
        if (styleToRemove) {
          document.head.removeChild(styleToRemove);
        }
      };
    }

    // Return empty cleanup function for SSR
    return () => {};
  }, []);

  // Handle wheel spin animation on page load
  useEffect(() => {
    // Only run on client-side
    if (typeof document !== 'undefined') {
      try {
        // Apply the animation immediately
        const timeoutId = setTimeout(() => {
          const wheelSvg = document.querySelector('.wheel-logo svg') as HTMLElement
          if (wheelSvg) {
            wheelSvg.style.animation = "spin 3s cubic-bezier(0.3, 0, 0.2, 1)"
          }
        }, 100); // Small delay to ensure DOM is ready

        // Clean up timeout
        return () => clearTimeout(timeoutId);
      } catch (error) {
        console.error("Error applying wheel animation:", error);
      }
    }

    // Return empty cleanup function
    return () => {};
  }, [])

  // Define all event handlers regardless of mounted state
  // This ensures all hooks are called in the same order on every render

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`)
    }
  }

  // Animation is now handled by CSS hover

  // Always return the same structure to avoid hook count mismatches
  return (
    <header className="bg-white shadow-sm sticky top-0 z-50 transition-opacity duration-300 pt-3 pb-2">
      {/* Postcode Dialog */}
      <Dialog open={postcodeDialogOpen} onOpenChange={(open) => {
        setPostcodeDialogOpen(open);
        if (!open) setValidationError(null);
      }}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>{validationError ? "Invalid Postcode" : "Update Delivery Location"}</DialogTitle>
          </DialogHeader>
          {validationError ? (
            <div className="flex flex-col gap-4">
              <div className="flex items-start gap-3">
                <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />
                <div>
                  <h3 className="text-lg font-semibold text-red-600">Invalid Postcode</h3>
                </div>
              </div>
              <p className="text-gray-600 mt-1 pl-8">
                {validationError}
              </p>
              <div className="flex flex-col sm:flex-row gap-2 pt-4 w-full justify-center">
                <Button
                  variant="outline"
                  onClick={() => setValidationError(null)}
                  className="w-[90%] mx-auto sm:w-[45%] text-[11px] sm:text-sm px-1 sm:px-2 whitespace-nowrap"
                  size="sm"
                  style={{ minHeight: "36px" }}
                >
                  Enter a different postcode
                </Button>
                <Button
                  onClick={() => {
                    setPostcodeDialogOpen(false);
                    setValidationError(null);
                  }}
                  className="w-[90%] mx-auto sm:w-[55%] bg-emerald-600 hover:bg-emerald-700 text-[11px] sm:text-sm px-1 sm:px-2 whitespace-nowrap"
                  size="sm"
                  style={{ minHeight: "36px" }}
                >
                  Continue with default location
                </Button>
              </div>
            </div>
          ) : (
            <>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="postcode">Enter your postcode</Label>
                  <div className="relative">
                    <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="postcode"
                      placeholder="e.g. JE2 3NG"
                      className="pl-10"
                      value={newPostcode}
                      onChange={(e) => setNewPostcode(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          handlePostcodeSubmit();
                        }
                      }}
                    />
                  </div>
                  <p className="text-xs text-gray-500">
                    Enter a Jersey postcode (e.g., JE2 3NG) to get accurate delivery times
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    Jersey postcodes must be in the format JE1-4 followed by a space, a digit, and two letters (e.g., JE2 3NG)
                  </p>
                </div>
              </div>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setPostcodeDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handlePostcodeSubmit}
                  disabled={isSubmitting || !newPostcode.trim()}
                  className="bg-emerald-600 hover:bg-emerald-700 text-white"
                >
                  {isSubmitting ? "Updating..." : "Update Location"}
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>

      <div className="container-fluid px-1 sm:px-3 md:px-4">
        <div className="flex items-center justify-between h-14 gap-2 sm:gap-4">
          {/* Logo with responsive design based on screen size */}
          <Link
            href="/"
            className="flex items-center group"
          >
            {/* Mobile Logo (Wheel + Loop) - Visible on small screens */}
            <div className="flex items-center bg-emerald-600 rounded-full px-3 py-1.5 border border-emerald-500 shadow-sm h-11 md:hidden">
              <div className="wheel-logo mr-2 group-hover:animate-spin">
                <WheelLogoIcon
                  size={24}
                  color="white"
                  className="text-white w-6 h-6"
                />
              </div>
              <span className="text-lg font-bold text-white">Loop Jersey</span>
            </div>

            {/* Tablet Logo (Wheel + Loop) - Visible on medium screens */}
            <div className="hidden md:flex lg:hidden items-center bg-emerald-600 rounded-full px-3 py-1.5 border border-emerald-500 shadow-sm h-11">
              <div className="wheel-logo mr-2 group-hover:animate-spin">
                <WheelLogoIcon
                  size={24}
                  color="white"
                  className="text-white w-6 h-6"
                />
              </div>
              <span className="text-lg font-bold text-white">Loop Jersey</span>
            </div>

            {/* Desktop Logo (Loop only) - Visible on large screens */}
            <div className="hidden lg:flex items-center bg-emerald-600 rounded-full px-3 py-1.5 border border-emerald-500 shadow-sm h-11">
              <div className="wheel-logo mr-2 group-hover:animate-spin">
                <WheelLogoIcon
                  size={24}
                  color="white"
                  className="text-white w-6 h-6"
                />
              </div>
              <span className="text-lg font-bold text-white">Loop Jersey</span>
            </div>
          </Link>

          {/* Mobile Search removed as requested - search bar is available below the header */}

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center flex-1 space-x-3 lg:space-x-4">
            {/* Search Bar - Hidden on home page and on smaller screens */}
            {!hideSearchBar && (
              <form onSubmit={handleSearch} className="relative hidden lg:block flex-1 min-w-0 max-w-xl">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                <Input
                  type="text"
                  placeholder="Search by name, location, or business type..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 w-full h-11 bg-white border border-gray-300 rounded-md text-gray-800 placeholder:text-gray-500 focus:border-emerald-500"
                />
              </form>
            )}
            {/* Partner with us dropdown - On home page and partners pages */}
            {(isHomePage || isPartnersPage) && (
              <div className="hidden lg:block">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <button className="text-sm font-medium text-gray-700 hover:text-emerald-600 flex items-center gap-1 border border-gray-300 rounded-md px-3 h-11">
                      Partner with us
                      <ChevronDown className="h-4 w-4 ml-1" />
                    </button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start" className="w-48">
                    {partnerLinks.map((item) => (
                      <Link key={item.name} href={item.href}>
                        <DropdownMenuItem>
                          {item.name}
                        </DropdownMenuItem>
                      </Link>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            )}

            {/* Location button - Hidden on partners pages */}
            {!hideLocationField && (
              <div className="flex items-center">
                {postcode ? (
                  <button
                    onClick={() => {
                      setNewPostcode(postcode);
                      setPostcodeDialogOpen(true);
                    }}
                    className="text-sm font-medium text-gray-700 hover:text-emerald-600 flex items-center gap-1 group border border-gray-300 rounded-md px-2 sm:px-3 h-11"
                  >
                    <MapPin className="h-4 w-4" />
                    <span className="hidden lg:inline">Delivering to: </span>
                    <span>{postcode}</span>
                    <span className="text-xs text-gray-500 group-hover:text-emerald-600 ml-1">(Change)</span>
                  </button>
                ) : (
                  <button
                    onClick={() => setPostcodeDialogOpen(true)}
                    className="text-sm font-medium text-gray-700 hover:text-emerald-600 flex items-center gap-1 border border-gray-300 rounded-md px-3 h-11"
                  >
                    <MapPin className="h-4 w-4" />
                    <span className="hidden sm:inline">Set delivery location</span>
                    <span className="sm:hidden">Set location</span>
                  </button>
                )}
              </div>
            )}
          </nav>

          {/* Right Actions */}
          <div className="flex items-center space-x-2 sm:space-x-3 md:space-x-4">
            {/* Messages - Only shown for authenticated users */}
            {user && (
              <Link href="/messages">
                <Button
                  variant="ghost"
                  size="sm"
                  className="relative text-gray-700 hover:bg-gray-100 flex items-center gap-0 px-2 sm:px-3 md:px-4 min-w-[48px] h-11 border border-gray-300 rounded-md"
                >
                  <MessageSquare className="h-5 w-5 sm:h-6 sm:w-6" />
                  <span className="hidden lg:inline lg:ml-2 text-sm">Messages</span>
                  {/* Unread message badge - placeholder */}
                  {/* <div className="absolute -top-1 -right-1 h-4 w-4 bg-emerald-600 text-white text-xs rounded-full flex items-center justify-center">3</div> */}
                </Button>
              </Link>
            )}

            {/* Account Button - Always shown */}
            <div>
              <UserMenu />
            </div>

            {/* Cart - Always shown and functional for all users */}
            <Sheet>
              <SheetTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="relative text-gray-700 hover:bg-gray-100 flex items-center gap-0 px-2 sm:px-3 md:px-4 min-w-[48px] h-11 border border-gray-300 rounded-md"
                >
                  <div className="relative p-1">
                    <ShoppingBasket className="h-6 w-6 sm:h-7 sm:w-7 md:h-8 md:w-8" />
                    {/* Cart count badge - client-side only rendering with useEffect */}
                    <CartBadge count={totalItems} />
                  </div>
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-full sm:max-w-md p-0">
                <CartSheet />
              </SheetContent>
            </Sheet>

            {/* Mobile menu button */}
            <Button
              variant="ghost"
              size="lg"
              className="md:hidden text-gray-700 hover:bg-gray-100 h-11 w-11 border border-gray-300 rounded-md p-0"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation - Always render but conditionally show */}
      <div className={`md:hidden ${mobileMenuOpen ? 'block' : 'hidden'}`}>
          <div className="px-2 pt-2 pb-3 space-y-3 sm:px-3 bg-gray-100">

            {/* Search Bar - Only shown when not on home page or business detail page */}
            {!hideSearchBar && (
              <div className="px-2 py-2">
                <div className="text-sm font-medium text-gray-700 mb-2">Search</div>
                <form onSubmit={(e) => {
                  handleSearch(e);
                  setMobileMenuOpen(false);
                }} className="relative w-full">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                  <Input
                    type="text"
                    placeholder="Search businesses..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 w-full h-10 bg-white border border-gray-300 rounded-md text-gray-800 placeholder:text-gray-500 focus:border-emerald-500"
                  />
                </form>
              </div>
            )}

            {/* Location information - Hidden on partners pages */}
            {!hideLocationField && (
              <div className="px-2 py-2">
                <div className="text-sm font-medium text-gray-700 mb-2">Delivery Location</div>
                {postcode ? (
                  <button
                    className="flex items-center justify-center p-2 rounded-md bg-white hover:bg-gray-200 w-full"
                    onClick={() => {
                      setMobileMenuOpen(false);
                      setNewPostcode(postcode);
                      setPostcodeDialogOpen(true);
                    }}
                  >
                    <MapPin className="h-4 w-4 text-gray-700 mr-1" />
                    <span className="text-sm text-gray-700">{postcode}</span>
                    <span className="text-xs text-gray-500 ml-1">(Change)</span>
                  </button>
                ) : (
                  <button
                    className="flex items-center justify-center p-2 rounded-md bg-white hover:bg-gray-200 w-full"
                    onClick={() => {
                      setMobileMenuOpen(false);
                      setPostcodeDialogOpen(true);
                    }}
                  >
                    <MapPin className="h-4 w-4 text-gray-700 mr-1" />
                    <span className="text-sm text-gray-700">Set delivery location</span>
                  </button>
                )}
              </div>
            )}

            {/* Partner with us - On home page and partners pages */}
            {(isHomePage || isPartnersPage) && (
              <div className="px-2 py-2">
                <div className="text-sm font-medium text-gray-700 mb-2">Partner with us</div>
                <div className="grid grid-cols-2 gap-2">
                  {partnerLinks.map((item) => (
                    <Link
                      key={item.name}
                      href={item.href}
                      className="flex items-center justify-center p-2 rounded-md bg-white hover:bg-gray-200"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      <span className="text-xs text-gray-700">{item.name}</span>
                    </Link>
                  ))}
                </div>
              </div>
            )}
          </div>
      </div>
    </header>
  )
}

// Add corresponding CSS classes for animations if using class-based approach
/*
In your global CSS or the style tag:
.animate-spin-on-load-css {
  animation: spin 3s cubic-bezier(0.3, 0, 0.2, 1) forwards;
}
.animate-spin-manual {
  animation: spin 1.5s cubic-bezier(0.2, 0.8, 0.3, 1);
}
*/
