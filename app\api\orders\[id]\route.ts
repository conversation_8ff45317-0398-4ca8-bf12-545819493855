import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create Supabase client with service role key (only available server-side)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Helper function to verify user access
async function verifyUserAccess(request: NextRequest) {
  // Get the authorization header
  const authorization = request.headers.get('Authorization');

  // Check if we have an authorization header
  if (!authorization) {
    console.log("No authorization header found in orders/[id] API")
    // Skip auth check in development for easier testing
    if (process.env.NODE_ENV === 'development') {
      console.log("Development mode: Skipping auth check in orders/[id] API")
      return { authorized: true };
    } else {
      return {
        authorized: false,
        error: "Authentication required",
        status: 401
      };
    }
  }

  console.log("Found authorization header in orders/[id] API, attempting to verify")

  // Extract the token
  const token = authorization.replace('Bearer ', '');

  try {
    // Verify the token
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      console.error("Invalid token in orders/[id] API:", error)
      return {
        authorized: false,
        error: "Invalid authentication token",
        status: 401
      };
    }

    console.log("Token verified for user in orders/[id] API:", user.email)
    return { authorized: true, user };
  } catch (authError) {
    console.error("Error verifying token in orders/[id] API:", authError)
    // Continue anyway in development mode
    if (process.env.NODE_ENV !== 'development') {
      return {
        authorized: false,
        error: "Authentication error",
        status: 401
      };
    } else {
      console.log("Development mode: Continuing despite auth error in orders/[id] API")
      return { authorized: true };
    }
  }
}

// GET endpoint to fetch a specific order
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify user access
    const accessCheck = await verifyUserAccess(request);
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      );
    }

    // Get the user from the access check
    const user = accessCheck.user;

    // Get the order ID from the URL
    const orderId = params.id;

    if (!orderId) {
      return NextResponse.json(
        { error: 'Order ID is required' },
        { status: 400 }
      );
    }

    // Fetch the order with all related tables
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        order_items(*),
        order_status_history(*),
        order_payment_allocations(*)
      `)
      .eq('id', orderId)
      .single();

    if (error) {
      console.error('Error fetching order:', error);
      return NextResponse.json(
        { error: 'Failed to fetch order', details: error.message },
        { status: 500 }
      );
    }

    if (!data) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    // Check if the user is authorized to view this order
    if (data.user_id !== user.id) {
      // Check if the user is an admin or business manager for this order
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('role')
        .eq('id', user.id)
        .single();

      if (userError || !userData) {
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 403 }
        );
      }

      const isAdmin = userData.role === 'admin' || userData.role === 'super_admin';

      if (!isAdmin) {
        // Check if user is a business manager for the business in this order
        const businessId = data.business_id;

        if (!businessId) {
          return NextResponse.json(
            { error: 'Unauthorized' },
            { status: 403 }
          );
        }

        const { data: managerData, error: managerError } = await supabase
          .from('business_managers')
          .select('business_id')
          .eq('user_id', user.id)
          .eq('business_id', businessId);

        if (managerError || !managerData || managerData.length === 0) {
          return NextResponse.json(
            { error: 'Unauthorized' },
            { status: 403 }
          );
        }
      }
    }

    return NextResponse.json({
      order: data
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}

// Import the transaction helper
import { updateOrderStatus } from '../transaction-helper';

// PATCH endpoint to update an order's status
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify user access
    const accessCheck = await verifyUserAccess(request);
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      );
    }

    // Get the user from the access check
    const user = accessCheck.user;

    // Get the order ID from the URL
    const orderId = parseInt(params.id);

    if (isNaN(orderId)) {
      return NextResponse.json(
        { error: 'Invalid order ID format' },
        { status: 400 }
      );
    }

    // Parse the request body
    const body = await request.json();

    if (!body.status) {
      return NextResponse.json(
        { error: 'Status is required' },
        { status: 400 }
      );
    }

    // Validate the status
    const validStatuses = [
      'pending', 'confirmed', 'preparing', 'ready',
      'out_for_delivery', 'delivered', 'cancelled'
    ];

    if (!validStatuses.includes(body.status)) {
      return NextResponse.json(
        { error: 'Invalid status', validStatuses },
        { status: 400 }
      );
    }

    try {
      // Use enhanced status update with automatic notifications
      const { updateOrderStatusWithNotifications } = await import('../enhanced-status-update');

      const result = await updateOrderStatusWithNotifications({
        orderId: parseInt(orderId),
        newStatus: body.status,
        notes: body.notes || null,
        updatedBy: user.id
      });

      return NextResponse.json({
        success: true,
        order: result.order
      });
    } catch (transactionError) {
      console.error('Error in order status update transaction:', transactionError);
      return NextResponse.json(
        { error: 'Failed to update order status', details: transactionError.message },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}
