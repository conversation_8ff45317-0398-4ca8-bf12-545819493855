#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to examine the connections-related database tables
 * This script connects to Supabase and analyzes the schema and data
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  console.error('Please ensure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in .env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function examineConnectionsTables() {
  console.log('🔍 Examining Connections Hub Database Tables\n');

  try {
    // 1. Check if connections tables exist
    console.log('📋 Checking table existence...');
    const tableQueries = [
      'connections',
      'communications', 
      'connection_profiles',
      'communication_attachments'
    ];

    for (const tableName of tableQueries) {
      try {
        const { data, error } = await supabase
          .from(tableName)
          .select('*')
          .limit(1);
        
        if (error) {
          console.log(`❌ Table '${tableName}': ${error.message}`);
        } else {
          console.log(`✅ Table '${tableName}': EXISTS`);
        }
      } catch (err) {
        console.log(`❌ Table '${tableName}': ERROR - ${err.message}`);
      }
    }

    console.log('\n📊 Analyzing table schemas and data...\n');

    // 2. Examine connections table
    await examineConnectionsTable();
    
    // 3. Examine communications table
    await examineCommunicationsTable();
    
    // 4. Examine connection_profiles table
    await examineConnectionProfilesTable();
    
    // 5. Examine communication_attachments table
    await examineCommunicationAttachmentsTable();

    // 6. Check for related views
    await examineViews();

  } catch (error) {
    console.error('❌ Error examining database:', error.message);
  }
}

async function examineConnectionsTable() {
  console.log('🔗 CONNECTIONS TABLE');
  console.log('='.repeat(50));

  try {
    // Get table info
    const { data: connections, error } = await supabase
      .from('connections')
      .select('*')
      .limit(10);

    if (error) {
      console.log(`❌ Error querying connections: ${error.message}`);
      return;
    }

    console.log(`📈 Total sample records: ${connections?.length || 0}`);
    
    if (connections && connections.length > 0) {
      console.log('\n📋 Sample connection record:');
      console.log(JSON.stringify(connections[0], null, 2));
      
      // Analyze connection types
      const { data: typeStats } = await supabase
        .from('connections')
        .select('connection_type')
        .limit(1000);
      
      if (typeStats) {
        const typeCounts = typeStats.reduce((acc, conn) => {
          acc[conn.connection_type] = (acc[conn.connection_type] || 0) + 1;
          return acc;
        }, {});
        
        console.log('\n📊 Connection types distribution:');
        Object.entries(typeCounts).forEach(([type, count]) => {
          console.log(`  ${type}: ${count}`);
        });
      }

      // Analyze status distribution
      const { data: statusStats } = await supabase
        .from('connections')
        .select('status')
        .limit(1000);
      
      if (statusStats) {
        const statusCounts = statusStats.reduce((acc, conn) => {
          acc[conn.status] = (acc[conn.status] || 0) + 1;
          return acc;
        }, {});
        
        console.log('\n📊 Status distribution:');
        Object.entries(statusCounts).forEach(([status, count]) => {
          console.log(`  ${status}: ${count}`);
        });
      }
    } else {
      console.log('📭 No connections found in database');
    }

  } catch (error) {
    console.log(`❌ Error examining connections table: ${error.message}`);
  }
  
  console.log('\n');
}

async function examineCommunicationsTable() {
  console.log('💬 COMMUNICATIONS TABLE');
  console.log('='.repeat(50));

  try {
    const { data: communications, error } = await supabase
      .from('communications')
      .select('*')
      .limit(10);

    if (error) {
      console.log(`❌ Error querying communications: ${error.message}`);
      return;
    }

    console.log(`📈 Total sample records: ${communications?.length || 0}`);
    
    if (communications && communications.length > 0) {
      console.log('\n📋 Sample communication record:');
      console.log(JSON.stringify(communications[0], null, 2));
      
      // Analyze channel types
      const { data: channelStats } = await supabase
        .from('communications')
        .select('channel_type')
        .limit(1000);
      
      if (channelStats) {
        const channelCounts = channelStats.reduce((acc, comm) => {
          acc[comm.channel_type] = (acc[comm.channel_type] || 0) + 1;
          return acc;
        }, {});
        
        console.log('\n📊 Channel types distribution:');
        Object.entries(channelCounts).forEach(([type, count]) => {
          console.log(`  ${type}: ${count}`);
        });
      }

      // Analyze message types
      const { data: messageStats } = await supabase
        .from('communications')
        .select('message_type')
        .limit(1000);
      
      if (messageStats) {
        const messageCounts = messageStats.reduce((acc, comm) => {
          acc[comm.message_type] = (acc[comm.message_type] || 0) + 1;
          return acc;
        }, {});
        
        console.log('\n📊 Message types distribution:');
        Object.entries(messageCounts).forEach(([type, count]) => {
          console.log(`  ${type}: ${count}`);
        });
      }
    } else {
      console.log('📭 No communications found in database');
    }

  } catch (error) {
    console.log(`❌ Error examining communications table: ${error.message}`);
  }
  
  console.log('\n');
}

async function examineConnectionProfilesTable() {
  console.log('👤 CONNECTION_PROFILES TABLE');
  console.log('='.repeat(50));

  try {
    const { data: profiles, error } = await supabase
      .from('connection_profiles')
      .select('*')
      .limit(10);

    if (error) {
      console.log(`❌ Error querying connection_profiles: ${error.message}`);
      return;
    }

    console.log(`📈 Total sample records: ${profiles?.length || 0}`);
    
    if (profiles && profiles.length > 0) {
      console.log('\n📋 Sample profile record:');
      console.log(JSON.stringify(profiles[0], null, 2));
      
      // Analyze profile types
      const { data: typeStats } = await supabase
        .from('connection_profiles')
        .select('profile_type')
        .limit(1000);
      
      if (typeStats) {
        const typeCounts = typeStats.reduce((acc, profile) => {
          acc[profile.profile_type] = (acc[profile.profile_type] || 0) + 1;
          return acc;
        }, {});
        
        console.log('\n📊 Profile types distribution:');
        Object.entries(typeCounts).forEach(([type, count]) => {
          console.log(`  ${type}: ${count}`);
        });
      }

      // Analyze public vs private profiles
      const { data: privacyStats } = await supabase
        .from('connection_profiles')
        .select('is_public, allow_direct_messages')
        .limit(1000);
      
      if (privacyStats) {
        const publicCount = privacyStats.filter(p => p.is_public).length;
        const dmAllowedCount = privacyStats.filter(p => p.allow_direct_messages).length;
        
        console.log('\n📊 Privacy settings:');
        console.log(`  Public profiles: ${publicCount}/${privacyStats.length}`);
        console.log(`  Allow direct messages: ${dmAllowedCount}/${privacyStats.length}`);
      }
    } else {
      console.log('📭 No connection profiles found in database');
    }

  } catch (error) {
    console.log(`❌ Error examining connection_profiles table: ${error.message}`);
  }
  
  console.log('\n');
}

async function examineCommunicationAttachmentsTable() {
  console.log('📎 COMMUNICATION_ATTACHMENTS TABLE');
  console.log('='.repeat(50));

  try {
    const { data: attachments, error } = await supabase
      .from('communication_attachments')
      .select('*')
      .limit(10);

    if (error) {
      console.log(`❌ Error querying communication_attachments: ${error.message}`);
      return;
    }

    console.log(`📈 Total sample records: ${attachments?.length || 0}`);
    
    if (attachments && attachments.length > 0) {
      console.log('\n📋 Sample attachment record:');
      console.log(JSON.stringify(attachments[0], null, 2));
    } else {
      console.log('📭 No communication attachments found in database');
    }

  } catch (error) {
    console.log(`❌ Error examining communication_attachments table: ${error.message}`);
  }
  
  console.log('\n');
}

async function examineViews() {
  console.log('👁️ DATABASE VIEWS');
  console.log('='.repeat(50));

  // Check for the views mentioned in the migration
  const views = [
    'user_connections_with_profiles',
    'unread_messages_count'
  ];

  for (const viewName of views) {
    try {
      const { data, error } = await supabase
        .from(viewName)
        .select('*')
        .limit(5);
      
      if (error) {
        console.log(`❌ View '${viewName}': ${error.message}`);
      } else {
        console.log(`✅ View '${viewName}': EXISTS (${data?.length || 0} sample records)`);
        if (data && data.length > 0) {
          console.log(`   Sample: ${JSON.stringify(data[0], null, 2)}`);
        }
      }
    } catch (err) {
      console.log(`❌ View '${viewName}': ERROR - ${err.message}`);
    }
  }
  
  console.log('\n');
}

// Run the examination
examineConnectionsTables()
  .then(() => {
    console.log('✅ Database examination completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Fatal error:', error);
    process.exit(1);
  });
