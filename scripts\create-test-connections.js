const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function createTestConnections() {
  console.log('🔗 Creating test connections for the test user');
  
  const testUserId = '43560081-d469-4d4e-85e2-457bda286397';
  const dominosUserId = '4eef2859-b5f5-440d-b5ca-086040de4f91';
  const bengalUserId = 'e08ef187-3773-4026-b277-c66b8fdbf1dd';
  
  // Create connection between test user and <PERSON><PERSON><PERSON>
  const { data: conn1, error: err1 } = await supabase
    .from('connections')
    .insert({
      user1_id: testUserId < dominosUserId ? testUserId : dominosUserId,
      user2_id: testUserId < dominosUserId ? dominosUserId : testUserId,
      connection_type: 'customer-business',
      status: 'active',
      user1_favorite: testUserId < dominosUserId ? true : false,
      user2_favorite: testUserId < dominosUserId ? false : true,
      created_by: testUserId,
      notes: 'Regular customer - loves the pizza!'
    })
    .select()
    .single();
  
  if (err1) {
    console.log('❌ Error creating connection 1:', err1.message);
  } else {
    console.log('✅ Created connection with Dominos:', conn1.id);
  }
  
  // Create connection between test user and Bengal Spice
  const { data: conn2, error: err2 } = await supabase
    .from('connections')
    .insert({
      user1_id: testUserId < bengalUserId ? testUserId : bengalUserId,
      user2_id: testUserId < bengalUserId ? bengalUserId : testUserId,
      connection_type: 'customer-business',
      status: 'active',
      user1_favorite: testUserId < bengalUserId ? false : true,
      user2_favorite: testUserId < bengalUserId ? true : false,
      created_by: testUserId,
      notes: 'Great curry restaurant'
    })
    .select()
    .single();
  
  if (err2) {
    console.log('❌ Error creating connection 2:', err2.message);
  } else {
    console.log('✅ Created connection with Bengal Spice:', conn2.id);
  }
  
  console.log('🎉 Test connections created successfully!');
}

createTestConnections().catch(console.error);
